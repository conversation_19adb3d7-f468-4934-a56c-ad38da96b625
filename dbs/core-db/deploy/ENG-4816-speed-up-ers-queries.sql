-- Deploy ENG-4816-speed-up-ers-queries
SET search_path TO public;
SET ROLE migration;

-- case filter
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_program 
ON cases(program_id);

-- user lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_applications_submitter 
ON applications(submitter_id);

-- latest application version
CREATE INDEX CONCURRENTLY idx_application_versions_latest
ON public.application_versions (application_id, created_at DESC);

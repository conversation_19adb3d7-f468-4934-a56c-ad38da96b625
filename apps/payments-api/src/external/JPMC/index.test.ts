import { mockLogger } from '@payments-test/setup/globals.js';
import {
  Endpoint,
  ExternalAccount,
  ExternalAccountType,
  ResponseData,
  SendRequest,
  StandardEndpoint,
  UpdateEndpoint,
} from '@payments/@types/jpmc.js';
import { JPMCRecipientKeys } from '@payments/@types/recipient.js';
import { ProviderError, ValidationError } from '@payments/utilities/errors/extensions/index.js';
import waitBackoff from '@payments/utilities/waitBackoff.js';
import axios from 'axios';
import JPMCService from './index.js';

vi.mock('axios');
vi.mock('@payments/utilities/waitBackoff');
const { mockX509 } = vi.hoisted(() => {
  return { mockX509: vi.fn() };
});
vi.mock('crypto', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as object),
    X509Certificate: mockX509,
  };
});

describe('JPMCService', () => {
  process.env.JPMC_CERTIFICATES = JSON.stringify({
    digitalsig_key: 'TEST DIGITAL SIGNATURE KEY',
    transport_key: 'TEST TRANSPORT KEY',
    transport_cert: 'TEST TRANSPORT CERT',
  });
  process.env.JPMC_BASE_URL = 'http://mockJPMC.com';

  describe('sendRequest', () => {
    beforeEach(() => {
      vi.spyOn(mockJPMCService, 'lookupExternalAccount').mockResolvedValue({
        externalAccountAliasID: 'mockAccountAliasId',
      } as unknown as ExternalAccount);
    });
    const mockJPMCService = new JPMCService();
    const method = 'POST';
    const route = 'test' as unknown as Endpoint;
    it('should use an HTTPS request with the secret certificates', async () => {
      axios.request = vi.fn().mockResolvedValueOnce({ status: 200 });
      await mockJPMCService.sendRequest({ route, method });
      expect(axios.request).toHaveBeenCalledWith(
        expect.objectContaining({
          httpsAgent: expect.objectContaining({
            options: expect.objectContaining({
              cert: 'TEST TRANSPORT CERT',
              key: 'TEST TRANSPORT KEY',
            }),
            protocol: 'https:',
          }),
        }),
      );
    });
    it('should use the endpoint and method provided', async () => {
      axios.request = vi.fn().mockResolvedValueOnce({ status: 200 });
      await mockJPMCService.sendRequest({ route, method });
      expect(axios.request).toHaveBeenCalledWith(
        expect.objectContaining({
          url: expect.stringContaining(route),
          method,
        }),
      );
    });
    describe('known errors', () => {
      it('should handle existing counterparty entities', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'ER001' }] } } });
        const result = await mockJPMCService.sendRequest({
          route: StandardEndpoint.CounterpartyEntity,
        } as unknown as SendRequest);
        expect(result).toEqual({});
      });
      it('should handle existing counterparty users', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R0022' }] } } });
        const result = await mockJPMCService.sendRequest({
          route: StandardEndpoint.CounterpartyUser,
        } as unknown as SendRequest);
        expect(result).toEqual({});
      });
      it('should handle existing external accounts', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R0116' }] } } });
        const request = {
          route: StandardEndpoint.ExternalAccount,
          payload: 'mockPayload',
          relations: 'mockRelations',
        } as unknown as SendRequest;
        const response = await mockJPMCService.sendRequest(request);
        expect(mockJPMCService.lookupExternalAccount).toHaveBeenCalledWith(
          request.payload,
          request.relations,
        );
        expect(response).toEqual({ externalAccountAliasId: 'mockAccountAliasId' });
      });
      it('should handle updating missing external accounts', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R1001' }] } } });
        const request = {
          route: UpdateEndpoint.ExternalAccount,
          payload: 'mockPayload',
          relations: 'mockRelations',
        } as unknown as SendRequest;
        const response = await mockJPMCService.sendRequest(request);
        expect(mockLogger.warn).toHaveBeenCalledWith(
          'JPMCService.handleExpectedErrors: account does not exist to update',
        );
        expect(response).toEqual({ externalAccountAliasId: undefined });
      });
      it('should handle existing payables', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R0210' }] } } });
        const result = await mockJPMCService.sendRequest({
          route: StandardEndpoint.Payable,
          payload: { payableNumber: 'mockRefId' },
        } as unknown as SendRequest);
        expect(result).toEqual({ payableNumber: 'mockRefId' });
      });
      it('should throw invalid address error code if the address is not passed', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R0140' }] } } });
        try {
          await mockJPMCService.sendRequest({
            route: StandardEndpoint.CounterpartyEntity,
          } as unknown as SendRequest);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Missing or invalid address');
        }
      });
      it('should throw invalid account error code if the account number is invalid', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R0036' }] } } });
        try {
          await mockJPMCService.sendRequest({
            route: StandardEndpoint.ExternalAccount,
            payload: { counterpartyUserAliasId: 'mockRefId' },
          } as unknown as SendRequest);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Invalid account number');
        }
      });
      it('should throw invalid account error code if the account cannot be validated', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R1039' }] } } });
        try {
          await mockJPMCService.sendRequest({
            route: StandardEndpoint.ExternalAccount,
            payload: { counterpartyUserAliasId: 'mockRefId' },
          } as unknown as SendRequest);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Invalid account number');
        }
      });
      it('should throw invalid routing number error code if the routing number is invalid', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R0035' }] } } });
        try {
          await mockJPMCService.sendRequest({
            route: StandardEndpoint.ExternalAccount,
            payload: { counterpartyUserAliasId: 'mockRefId' },
          } as unknown as SendRequest);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Invalid routing number');
        }
      });
      it('should throw account not found error code if the account is not found', async () => {
        axios.request = vi
          .fn()
          .mockRejectedValueOnce({ response: { data: { errors: [{ errorCode: 'R1210' }] } } });
        try {
          await mockJPMCService.sendRequest({
            route: StandardEndpoint.ExternalAccount,
            payload: { counterpartyUserAliasId: 'mockRefId' },
          } as unknown as SendRequest);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('No matching Zelle account found');
        }
      });
      it('should throw a useful error if unrecognized', async () => {
        axios.request = vi.fn().mockRejectedValueOnce({
          response: {
            data: {
              errors: [
                { errorCode: '1', errorMsg: 'Bad' },
                { errorCode: '2', errorMsg: 'things' },
              ],
            },
          },
        });
        await expect(
          mockJPMCService.sendRequest({
            route: StandardEndpoint.Payout,
            method: 'UNKNOWN-JPMC-ERROR',
          } as unknown as SendRequest),
        ).rejects.toThrow(new Error('Unexpected error with payins-payouts > 1: Bad,2: things'));
      });
    });
    it('should throw the error if wholly unrecognized', async () => {
      axios.request = vi.fn().mockRejectedValueOnce(new Error('oh no no no no this is bad'));
      await expect(
        mockJPMCService.sendRequest({ route } as unknown as SendRequest),
      ).rejects.toThrow();
    });
    it('should retry the request if a 5xx is returned', async () => {
      axios.request = vi
        .fn()
        .mockRejectedValueOnce({ response: { status: 500 } })
        .mockRejectedValueOnce({ response: { status: 500 } })
        .mockResolvedValueOnce({ response: { status: 200 } });
      await mockJPMCService.sendRequest({ route, method });
      expect(waitBackoff).toHaveBeenCalledTimes(2);
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 1, baseDelay: 250 });
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 2, baseDelay: 250 });
    });
    it('should retry the request if no response is returned', async () => {
      axios.request = vi
        .fn()
        .mockRejectedValueOnce({ request: { path: route }, response: undefined })
        .mockRejectedValueOnce({ request: { path: route }, response: undefined })
        .mockResolvedValueOnce({ request: { path: route }, response: undefined });
      await mockJPMCService.sendRequest({ route, method });
      expect(waitBackoff).toHaveBeenCalledTimes(2);
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 1, baseDelay: 250 });
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 2, baseDelay: 250 });
    });
    it('should not retry after the maximum retry value is reached', async () => {
      axios.request = vi.fn().mockRejectedValue({ response: { status: 500 } });
      try {
        await mockJPMCService.sendRequest({ route, method });
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(ProviderError);
        expect((error as Error).message).toBe(
          'JPMCService.sendRequest: JPMC server error > 500 - undefined - max retries of 5 reached',
        );
      }
      expect(waitBackoff).toHaveBeenCalledTimes(4);
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 1, baseDelay: 250 });
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 2, baseDelay: 250 });
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 3, baseDelay: 250 });
      expect(waitBackoff).toHaveBeenCalledWith({ retries: 4, baseDelay: 250 });
      expect(waitBackoff).not.toHaveBeenCalledWith({ retries: 5 });
    });
    it('should not retry if the route is "payins-payouts"', async () => {
      axios.request = vi.fn().mockRejectedValue({ response: { status: 500 } });
      await expect(
        mockJPMCService.sendRequest({ route: StandardEndpoint.Payout, method }),
      ).rejects.toThrow();
      expect(waitBackoff).not.toHaveBeenCalled();
    });
  });

  describe('lookupExternalAccount', () => {
    beforeEach(() => {
      vi.spyOn(mockJPMCService, 'sendRequest').mockImplementation(({ route }) => {
        if (route.includes(ExternalAccountType.Bank))
          return Promise.resolve<ResponseData>({
            externalAccounts: [existingBankAccount],
          } as unknown as ResponseData);
        if (route.includes(ExternalAccountType.Zelle))
          return Promise.resolve<ResponseData>({
            externalAccounts: [existingZelleAccount],
          } as unknown as ResponseData);
        return Promise.reject();
      });
    });
    const mockJPMCService = new JPMCService();
    const existingBankAccount = {
      bankAccount: { accountNumberLastFour: '3456', routingNumber: '*********' },
    };
    const existingZelleAccount = { zelleAccount: { email: '<EMAIL>' } };
    const mockPayload = (type: ExternalAccountType) =>
      ({
        programId: 'mockProgramId',
        counterpartyUserAliasId: `mockCounterpartyUserAliasId-${type}`,
        externalAccountType: type,
        ...(type === ExternalAccountType.Bank && {
          bankAccount: {
            bankAccountNumber: '1233456',
            routingNumber: '*********',
            type: 'checking',
          },
        }),
        ...(type === ExternalAccountType.Zelle && {
          zelleAccount: { email: '<EMAIL>' },
        }),
      }) as ExternalAccount;
    const relations = { counterpartyEntityAliasId: 'mockCounterpartyEntityAliasId' };
    it('should return the matching bank account if found', async () => {
      const payload = mockPayload(ExternalAccountType.Bank);
      const result = await mockJPMCService.lookupExternalAccount(
        payload,
        relations as JPMCRecipientKeys,
      );
      expect(mockJPMCService.sendRequest).toHaveBeenCalledWith({
        method: 'GET',
        route: `programs/${payload.programId}/counterparty-entities/${relations.counterpartyEntityAliasId}/counterparty-users/${payload.counterpartyUserAliasId}/`,
      });
      expect(result).toEqual(existingBankAccount);
    });
    it('should return the matching zelle account if found', async () => {
      const payload = mockPayload(ExternalAccountType.Zelle);
      const result = await mockJPMCService.lookupExternalAccount(
        payload,
        relations as JPMCRecipientKeys,
      );
      expect(mockJPMCService.sendRequest).toHaveBeenCalledWith({
        method: 'GET',
        route: `programs/${payload.programId}/counterparty-entities/${relations.counterpartyEntityAliasId}/counterparty-users/${payload.counterpartyUserAliasId}/`,
      });
      expect(result).toEqual(existingZelleAccount);
    });
  });
});

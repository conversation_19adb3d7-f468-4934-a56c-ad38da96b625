import { mockLogger } from '@payments-test/setup/globals.js';
import { Endpoint, SendRequest, USIOCredentials } from '@payments/@types/usio.js';
import axios, { AxiosError, AxiosResponse } from 'axios';
import Bottleneck from 'bottleneck';
import { Mocked } from 'vitest';
import USIOService from './index.js';

const originalEnv = { ...process.env };

vi.mock('axios');

afterAll(() => {
  process.env = { ...originalEnv };
});

describe('USIOService', () => {
  const mockCreds = {
    username: 'FAKE_USER_NAME',
    password: 'FAKE_PASSWORD',
    adminKey: 'FAKE_ADMIN_KEY',
  };
  let mockUSIOService: USIOService;

  beforeEach(() => {
    process.env.USIO_BASE_URL = 'https://usiofakeurl';
    process.env.USIO_CREDENTIALS = JSON.stringify(mockCreds);
    mockUSIOService = new USIOService();
    vi.spyOn(mockUSIOService, 'fetchAuthToken').mockResolvedValue('Bearer ACCESS_TOKEN');
  });
  describe('constructor', () => {
    it('should throw error if base url variable not set', async () => {
      // biome-ignore lint/performance/noDelete: must delete to unset key
      delete process.env.USIO_BASE_URL;
      expect(() => new USIOService()).toThrow('missing env variable: USIO_BASE_URL');
    });
    it('should throw error if base url variable not set', async () => {
      // biome-ignore lint/performance/noDelete: must delete to unset key
      delete process.env.USIO_CREDENTIALS;
      expect(() => new USIOService()).toThrow('missing env variable: USIO_CREDENTIALS');
    });
    it('should not throw error if env variable are set', async () => {
      process.env.USIO_BASE_URL = 'https://usiofakeurl';
      process.env.USIO_CREDENTIALS = JSON.stringify(mockCreds);
      expect(() => new USIOService()).not.toThrow();
    });
  });
  describe('getURL', () => {
    it('should build the url with the provided endpoint', () => {
      expect(mockUSIOService.getURL(Endpoint.GiftCard)).toBe('https://usiofakeurl/gift-card');
    });
    it('should allow specifying a suffix to the endpoint', () => {
      expect(mockUSIOService.getURL(Endpoint.GiftCard, 'order')).toBe(
        'https://usiofakeurl/gift-card/order',
      );
    });
  });
  describe('getHeaders', () => {
    it('should add authorization header if withAuthentication is true', async () => {
      await expect(
        mockUSIOService.getHeaders({
          method: 'GET',
          withAuthenticate: true,
        } as unknown as SendRequest),
      ).resolves.toStrictEqual({
        Authorization: 'Bearer ACCESS_TOKEN',
      });
    });
    it('should add super admin username as header if withAdminUsername is true', async () => {
      await expect(
        mockUSIOService.getHeaders({
          method: 'GET',
          withAdminUsername: true,
        } as unknown as SendRequest),
      ).resolves.toStrictEqual({
        'Akimbo-Impersonate-As-Username': mockCreds.username,
      });
    });
    it('should add content type as header if method is POST', async () => {
      await expect(
        mockUSIOService.getHeaders({ method: 'POST' } as unknown as SendRequest),
      ).resolves.toStrictEqual({
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
      });
    });
    it('should throw an error if authentication fails', async () => {
      vi.spyOn(mockUSIOService, 'fetchAuthToken').mockRejectedValueOnce(new Error('Server Error'));
      await expect(
        mockUSIOService.getHeaders({
          method: 'POST',
          withAuthenticate: true,
        } as unknown as SendRequest),
      ).rejects.toThrowError(new Error('issue retrieving usio headers > Error: Server Error'));
    });
  });
  describe('sendRequest', () => {
    it('should use an HTTPS request without authentication', async () => {
      const method = 'POST';
      const route = 'order';
      const routeBase = 'gift-card' as Endpoint;
      (axios as Mocked<typeof axios>).request.mockResolvedValueOnce({});

      await mockUSIOService.sendRequest({ route, method, routeBase });
      expect(axios.request).toBeCalledWith(
        expect.objectContaining({
          url: expect.stringContaining(route),
          method,
        }),
      );
      expect(mockUSIOService.fetchAuthToken).not.toBeCalled();
    });
    it('should do authentication if request has withAuthentication true', async () => {
      const method = 'POST';
      const route = 'order';
      const routeBase = 'gift-card' as Endpoint;
      (axios as Mocked<typeof axios>).request.mockResolvedValueOnce({ status: 200, data: {} });
      await mockUSIOService.sendRequest({ route, method, routeBase, withAuthenticate: true });
      expect(axios.request).toBeCalledWith(
        expect.objectContaining({
          url: expect.stringContaining(route),
          method,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
            Authorization: 'Bearer ACCESS_TOKEN',
          },
          timeout: 30000,
        }),
      );
    });
    it('should throw the error if USIO throws an error', async () => {
      (axios as Mocked<typeof axios>).request.mockRejectedValueOnce(
        new Error('Failed to reach server') as AxiosError,
      );
      (axios as Mocked<typeof axios>).isAxiosError.mockReturnValueOnce(false);
      await expect(
        mockUSIOService.sendRequest({
          routeBase: 'error',
          method: 'POST',
        } as unknown as SendRequest),
      ).rejects.toThrow();
    });
    it('should throw the error if USIO throws an axios error', async () => {
      const error = new Error('error') as AxiosError;
      error.response = {
        status: 500,
        statusText: 'Server Error',
      } as AxiosResponse;
      (axios as Mocked<typeof axios>).request.mockRejectedValueOnce(error);
      (axios as Mocked<typeof axios>).isAxiosError.mockReturnValueOnce(true);
      await expect(
        mockUSIOService.sendRequest({
          routeBase: 'error',
          method: 'POST',
        } as unknown as SendRequest),
      ).rejects.toThrow(new Error('USIO server error > 500 - Server Error'));
    });
    it('should throw the error if USIO return with an error response', async () => {
      const response = {
        status: 200,
        data: {
          ErrorCode: 'USIO_ERROR_CODE',
          ErrorMessage: 'USIO_ERROR_MESSAGE',
        },
      } as AxiosResponse;
      (axios as Mocked<typeof axios>).request.mockResolvedValueOnce(response);
      (axios as Mocked<typeof axios>).isAxiosError.mockReturnValueOnce(false);
      await expect(
        mockUSIOService.sendRequest({
          routeBase: 'error',
          method: 'POST',
        } as unknown as SendRequest),
      ).rejects.toThrow(new Error('USIO server error > USIO_ERROR_CODE - USIO_ERROR_MESSAGE'));
    });
    it('should retry if the error comes from the rate limiter', async () => {
      const response = {
        status: 200,
        data: {
          ErrorCode: 'USIO_ERROR_CODE',
          ErrorMessage: 'USIO_ERROR_MESSAGE',
        },
      } as AxiosResponse;
      (axios as Mocked<typeof axios>).request.mockRejectedValueOnce(
        new Bottleneck.BottleneckError('error with rate limiter'),
      );
      (axios as Mocked<typeof axios>).request.mockResolvedValueOnce(response);
      (axios as Mocked<typeof axios>).isAxiosError.mockReturnValueOnce(false);
      await expect(
        mockUSIOService.sendRequest({
          routeBase: 'error',
          method: 'POST',
          route: 'testing',
        } as unknown as SendRequest),
      ).rejects.toThrow(new Error('USIO server error > USIO_ERROR_CODE - USIO_ERROR_MESSAGE'));
      expect((axios as Mocked<typeof axios>).request).toHaveBeenCalledTimes(2);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        { attempt: 1, method: 'POST', route: 'testing' },
        'USIOService.sendRequest: error with rate limiter, retrying',
      );
    });
  });
  describe('authentication', () => {
    it('should throw an error if credentials are not passed', async () => {
      await expect(mockUSIOService.authenticate(undefined as unknown as USIOCredentials)).rejects.toThrow(
        new Error('USIO Unauthorized: Invalid credentials'),
      );
    });
    it('should return token if request goes successfully', async () => {
      (axios as Mocked<typeof axios>).request.mockResolvedValueOnce({
        status: 200,
        data: {
          access_token: 'ACCESS_TOKEN',
          token_type: 'Bearer',
        },
      });
      const token = await mockUSIOService.authenticate(mockCreds);
      expect(token).toBe('Bearer ACCESS_TOKEN');
      expect(axios.request).toBeCalledWith(
        expect.objectContaining({
          url: expect.stringContaining('login'),
          method: 'POST',
          timeout: 30000,
          data: `username=${mockCreds.username}&password=${mockCreds.password}&_key=${mockCreds.adminKey}`,
          headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
        }),
      );
    });
  });
  describe('fetchAuthToken', () => {
    it('should call authenticate if authToken is not defined', async () => {
      const mockService = new USIOService();
      vi.spyOn(mockService, 'authenticate').mockResolvedValue('Bearer ACCESS_TOKEN');

      await mockService.fetchAuthToken(mockCreds);
      expect(mockService.authenticate).toHaveBeenCalled();
    });
    it('should NOT call authenticate method when the auth token is valid', async () => {
      const mockService = new USIOService();
      vi.spyOn(mockService, 'authenticate').mockResolvedValue('Bearer ACCESS_TOKEN');
      // biome-ignore lint/complexity/useLiteralKeys: this is a private field
      mockService['authToken'] = 'Bearer ACCESS_TOKEN';

      await mockService.fetchAuthToken(mockCreds);
      expect(mockService.authenticate).not.toHaveBeenCalled();
    });
  });
});

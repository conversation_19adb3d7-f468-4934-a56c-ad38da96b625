import { mockLogger } from '@payments-test/setup/globals.js';
import { FundingSource } from '@payments/@types/fundingSource.js';
import { ErrorCodes } from '@payments/@types/index.js';
import { Provider } from '@payments/@types/provider.js';
import { CreateRecipient, UpdateRecipient } from '@payments/@types/recipient.js';
import { RecipientRepository } from '@payments/@types/repository.js';
import ProviderRepositoryFactory from '@payments/repositories/providers/ProviderRepositoryFactory.js';
import { ValidationError } from '@payments/utilities/errors/extensions/index.js';
import { StatusCodes } from 'http-status-codes';
import { Mock } from 'vitest';
import { sendSlackNotification } from '../../utilities/slack/sendSlackNotification.js';
import FundingSourceService from '../FundingSourceService.js';
import RecipientService from './RecipientService.js';

vi.mock('../../utilities/slack/sendSlackNotification');

const mockFundingSource = {
  id: 'mockFundingSourceId',
  referenceId: 'mockFundingSourceReferenceId',
  provider: Provider.JPMC,
} as unknown as FundingSource;

const mockJPMCFundingSource = {
  id: 'mockJPMCFundingSourceId',
  referenceId: 'mockJPMCFundingSourceReferenceId',
  provider: Provider.JPMC,
};

const mockCreateInput = {
  referenceId: 'mockReferenceId',
  email: '<EMAIL>',
  fundingSourceReferenceId: 'mockFundingSourceReferenceId',
  provider: Provider.JPMC,
  client: { id: 'mockClientId' },
} as unknown as CreateRecipient;

const mockUpdateInput = {
  referenceId: 'existingRecipient',
  firstName: 'Test',
  lastName: 'Test',
  provider: Provider.JPMC,
  client: { id: 'mockClientId' },
} as unknown as UpdateRecipient;

const mockSupportsPaymentFn = vi.fn();

// TODO: This is a bit of a mess; it'd be a lot more straightforward to just move the
// mock service creation into each individual test (also fits unit testing best practices)
function createMockRecipientService(): {
  service: RecipientService;
  linkRecipientFn: Mock;
  saveRecipientFn: Mock;
  updateRecipientFn: Mock;
  needsRecipientLinkFn: Mock;
  supportsPaymentFn: Mock;
  accountIsRequiredFn: Mock;
  isShellRecipientFn: Mock;
  isItProviderRecipient: Mock;
} {
  const saveRecipientFn = vi.fn();
  const linkRecipientFn = vi.fn();
  const updateRecipientFn = vi.fn();
  const needsRecipientLinkFn = vi.fn();
  const supportsPaymentFn = vi.fn();
  const accountIsRequiredFn = vi.fn();
  const isShellRecipientFn = vi.fn();
  const isItProviderRecipient = vi.fn();
  return {
    saveRecipientFn,
    linkRecipientFn,
    updateRecipientFn,
    needsRecipientLinkFn,
    supportsPaymentFn,
    accountIsRequiredFn,
    isShellRecipientFn,
    isItProviderRecipient,
    service: new RecipientService(
      {
        findByReferenceId: vi.fn(({ referenceId }) => {
          if (['existingRecipient', 'failProvider', 'failSaving'].includes(referenceId)) {
            const isSuccess = referenceId === 'existingRecipient';
            return {
              id: 'existingRecipient',
              referenceId: isSuccess ? 'mockReferenceId' : referenceId,
              fundingSources: [mockFundingSource],
              keys: { providerRecipientId: isSuccess ? 'mockReferenceId' : referenceId },
            };
          }
          if (referenceId === 'lookupOrLinkRecipient') {
            return {
              id: referenceId,
              referenceId,
              fundingSources: [mockFundingSource, mockJPMCFundingSource],
              keys: { existingKey: 'existingKey' },
            };
          }
        }),
        save: saveRecipientFn.mockImplementation(({ referenceId }) => {
          if (
            referenceId === mockCreateInput.referenceId ||
            referenceId === mockUpdateInput.referenceId
          )
            return { id: 'mockRecipientId' };
          if (referenceId === 'lookupOrLinkRecipient') return { id: referenceId };
          return undefined;
        }),
      } as unknown as RecipientRepository,
      {
        findByReferenceId: vi.fn((refId) => {
          if (refId === mockFundingSource.referenceId) return mockFundingSource;
          if ([mockJPMCFundingSource.referenceId, 'differentReferenceId'].includes(refId)) {
            return mockJPMCFundingSource;
          }
          if (refId === 'secondFundingSourceReferenceId')
            return { ...mockFundingSource, referenceId: 'secondFundingSourceReferenceId' };
          return undefined;
        }),
      } as unknown as FundingSourceService,
      {
        get: vi.fn(() => ({
          createRecipient: vi.fn(({ email }) => {
            if (email === '<EMAIL>') return undefined;
            if (email === '<EMAIL>')
              return Promise.reject(
                new ValidationError({
                  message: 'Invalid Email',
                  errorCode: ErrorCodes.InvalidZelleEmail,
                  retryable: true,
                }),
              );
            return { providerRecipientId: 'mockRecipientId' };
          }),
          updateRecipient: updateRecipientFn.mockImplementation(({ email }) =>
            email === '<EMAIL>' ? undefined : { providerRecipientId: 'mockRecipientId' },
          ),
          retrieveRecipient: vi.fn(({ providerRecipientId }) =>
            providerRecipientId === 'failProvider'
              ? Promise.reject(new Error('Provider Error'))
              : {
                  providerRecipientId: 'mockRecipientId',
                },
          ),
          linkRecipient: linkRecipientFn.mockResolvedValue({
            providerRecipientId: 'mockRecipientId',
          }),
          hasRecipientChanges: vi.fn((_, { email }) => email === '<EMAIL>'),
          needsRecipientLink: needsRecipientLinkFn.mockReturnValue(true),
          supportsPayment: mockSupportsPaymentFn,
          accountIsRequired: needsRecipientLinkFn.mockReturnValue(true),
          isShellRecipient: isShellRecipientFn.mockReturnValue(false),
          isItProviderRecipient: isItProviderRecipient.mockReturnValue(true),
        })),
      } as unknown as ProviderRepositoryFactory,
    ),
  };
}

describe('RecipientService', () => {
  describe('upsert', () => {
    describe('when there is missing or invalid input data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          try {
            await createMockRecipientService().service.upsert({
              ...mockCreateInput,
              referenceId: undefined as unknown as string,
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe('recipient referenceId required for creation');
          }
        });
      });
      describe('when there is an existing recipient with the provided reference id', () => {
        it('should return a 200', async () => {
          // Setup
          mockSupportsPaymentFn.mockReturnValue(true);

          // Action
          const { status } = await createMockRecipientService().service.upsert({
            ...mockCreateInput,
            referenceId: 'existingRecipient',
          });

          // Expectation
          expect(status).toEqual(200);
        });
      });
      describe('when there is no funding source with the provided funding source reference id accessible to the client', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          try {
            await createMockRecipientService().service.upsert({
              ...mockCreateInput,
              fundingSourceReferenceId: undefined as unknown as string,
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe('no funding source found for referenceId undefined and clientId mockClientId');
          }
        });
      });
    });
    describe('when the provider on the funding source does not match the input provider', () => {
      it('throws a ValidationError with an appropriate message', async () => {
        const { service } = createMockRecipientService();
        mockSupportsPaymentFn.mockReturnValue(false);
        try {
          await service.upsert({
            ...mockCreateInput,
            provider: 'unsupportedProvider' as unknown as Provider,
          } as unknown as CreateRecipient);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe(`mismatched provider on funding source for referenceId ${mockCreateInput.fundingSourceReferenceId}`);
        }
      });
    });
    describe('when there is a non-retryable error in one of the provider services', () => {
      it('throws an Error with an appropriate message and send Slack notification', async () => {
        mockSupportsPaymentFn.mockReturnValue(true);
        try {
          await createMockRecipientService().service.upsert({
            ...mockCreateInput,
            email: '<EMAIL>',
          } as unknown as CreateRecipient);
          expect.fail('Should have thrown');
        } catch (error) {
          expect((error as Error).message).toBe(`issue creating recipient with ${mockCreateInput.provider}`);
        }
        expect(sendSlackNotification).toHaveBeenCalled();
      });
    });
    describe('when there is a retryable error in one of the provider services', () => {
      it('throws an Error with an appropriate message', async () => {
        mockSupportsPaymentFn.mockReturnValue(true);
        try {
          await createMockRecipientService().service.upsert({
            ...mockCreateInput,
            email: '<EMAIL>',
          } as unknown as CreateRecipient);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Invalid Email');
        }
        expect(sendSlackNotification).not.toHaveBeenCalled();
      });
    });
    describe('when the provider service runs successfully', () => {
      describe('when the repository fails to save the new row', () => {
        it('throws a DatabaseError with an appropriate message', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);
          try {
            await createMockRecipientService().service.upsert({
              ...mockCreateInput,
              referenceId: 'badRefId',
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect((error as Error).message).toBe('issue saving recipient to database');
          }
        });
      });
      describe('when the new row is saved', () => {
        it('should return a 201 and the new row', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);
          const { status } = await createMockRecipientService().service.upsert(mockCreateInput);
          expect(status).toEqual(201);
          expect(mockLogger.info).toHaveBeenCalledWith(
            'RecipientService.create: created new record with id mockRecipientId',
          );
        });
      });
    });
    describe('when the recipient already exists', () => {
      describe('when it is already linked to the requested fund', () => {
        describe('when the request is the same as the existing recipient', () => {
          it('returns the existing recipient without making changes', async () => {
            const { service, updateRecipientFn, saveRecipientFn } = createMockRecipientService();
            const { status } = await service.upsert({
              ...mockCreateInput,
              referenceId: 'existingRecipient',
            });
            expect(status).toEqual(200);
            expect(updateRecipientFn).not.toHaveBeenCalled();
            expect(saveRecipientFn).not.toHaveBeenCalled();
          });
        });

        describe('when the request has changes to the existing recipient', () => {
          it('updates the existing recipient', async () => {
            const { service, updateRecipientFn, saveRecipientFn } = createMockRecipientService();
            const { status } = await service.upsert({
              ...mockCreateInput,
              referenceId: 'existingRecipient',
              email: '<EMAIL>',
            } as CreateRecipient);
            expect(status).toEqual(200);
            expect(updateRecipientFn).toHaveBeenCalledWith({
              providerRecipientId: 'mockReferenceId',
              ...mockCreateInput,
              referenceId: 'existingRecipient',
              email: '<EMAIL>',
              fundingSource: mockFundingSource,
            });
            expect(saveRecipientFn).toHaveBeenCalledWith({
              id: 'existingRecipient',
              keys: { providerRecipientId: 'mockRecipientId' },
            });
          });
        });
      });

      describe('when the recipient already exists linked to a different fund', () => {
        it('links the recipient to the new fund', async () => {
          const { service, linkRecipientFn, saveRecipientFn } = createMockRecipientService();
          const { status } = await service.upsert({
            ...mockCreateInput,
            referenceId: 'existingRecipient',
            fundingSourceReferenceId: 'secondFundingSourceReferenceId',
          });
          expect(status).toEqual(201);
          expect(linkRecipientFn).toHaveBeenCalledWith({
            providerRecipientId: 'mockReferenceId',
            ...mockCreateInput,
            referenceId: 'existingRecipient',
            fundingSourceReferenceId: 'secondFundingSourceReferenceId',
            fundingSource: {
              ...mockFundingSource,
              referenceId: 'secondFundingSourceReferenceId',
            },
          });
          expect(saveRecipientFn).toHaveBeenCalledWith({
            id: 'existingRecipient',
            referenceId: 'existingRecipient',
            fundingSources: [
              mockFundingSource,
              { ...mockFundingSource, referenceId: 'secondFundingSourceReferenceId' },
            ],
            keys: { providerRecipientId: 'mockRecipientId' },
          });
        });
      });
    });
  });
  describe('update', () => {
    describe('when there is missing or invalid input data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          try {
            await createMockRecipientService().service.update({
              ...mockUpdateInput,
              referenceId: undefined as unknown as string,
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe('recipient referenceId required for update');
          }
        });
      });
      describe('when there is no recipient found', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          try {
            await createMockRecipientService().service.update({
              ...mockUpdateInput,
              referenceId: 'undefined' as unknown as string,
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe('recipient not found with referenceId undefined');
          }
        });
      });
    });
    describe('when the provider on the funding source does not match the input provider', () => {
      it('throws a ValidationError with an appropriate message', async () => {
        try {
          await createMockRecipientService().service.update({
            ...mockUpdateInput,
            provider: 'unsupportedProvider' as unknown as Provider,
          } as unknown as UpdateRecipient);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('mismatched provider on funding source for referenceId mockFundingSourceReferenceId');
        }
      });
    });
    describe('when there is an error in one of the provider services', () => {
      it('throws an Error with an appropriate message', async () => {
        // Setup
        mockSupportsPaymentFn.mockReturnValue(true);

        // Action & Expectation
        try {
          await createMockRecipientService().service.update({
            ...mockUpdateInput,
            email: '<EMAIL>',
          } as unknown as UpdateRecipient);
          expect.fail('Should have thrown');
        } catch (error) {
          expect((error as Error).message).toBe(`issue updating recipient with ${mockUpdateInput.provider}`);
        }
      });
    });
    describe('when the provider service runs successfully', () => {
      describe('when the repository fails to save the record', () => {
        it('throws a DatabaseError with an appropriate message', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);
          try {
            await createMockRecipientService().service.update({
              ...mockUpdateInput,
              referenceId: 'failSaving',
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect((error as Error).message).toBe('issue updating recipient to database');
          }
        });
      });
      describe('when the record is saved', () => {
        it('should return a 200 and update the row', async () => {
          const { status } = await createMockRecipientService().service.update(mockUpdateInput);
          expect(status).toEqual(200);
          expect(mockLogger.info).toHaveBeenCalledWith(
            'RecipientService.update: updated record with id mockRecipientId',
          );
        });
      });
    });
  });
  describe('retrieve', () => {
    describe('when there is missing or invalid input data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          try {
            await createMockRecipientService().service.retrieve(
              undefined as unknown as string,
              'clientId',
            );
            expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('recipient referenceId required');
        }
        });
      });
      describe('when there is no recipient found with provided reference id', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          try {
            await createMockRecipientService().service.retrieve('undefined', 'mockClientId');
            expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('no recipient found for referenceId undefined and clientId mockClientId');
        }
        });
      });
    });
    describe('when there is an error in one of the provider services', () => {
      it('throws an Error with an appropriate message', async () => {
        try {
          await createMockRecipientService().service.retrieve(
            'failProvider' as unknown as string,
            'clientId',
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect((error as Error).message).toBe('Provider Error');
        }
      });
    });
    describe('when the provider service runs successfully', () => {
      it('should return a 200 and a recipient', async () => {
        const { status } = await createMockRecipientService().service.retrieve(
          'existingRecipient',
          'mockClientId',
        );
        expect(status).toEqual(200);
        expect(mockLogger.info).toHaveBeenCalledWith(
          'RecipientService.retrieve: receive a record with referenceId mockReferenceId',
        );
      });
    });
  });
  describe('lookupOrLink', () => {
    describe('when the provider does not require the recipient to be linked to a funding source', () => {
      it('should return the existing recipient', async () => {
        const { needsRecipientLinkFn, saveRecipientFn, accountIsRequiredFn, service } =
          createMockRecipientService();
        needsRecipientLinkFn.mockReturnValue(false);
        accountIsRequiredFn.mockReturnValue(false);
        const { status } = await service.lookupOrLink({
          ...mockCreateInput,
          provider: Provider.USIO,
          referenceId: 'lookupOrLinkRecipient',
          fundingSourceReferenceId: mockJPMCFundingSource.referenceId,
        });
        expect(status).toEqual(StatusCodes.OK);
        expect(saveRecipientFn).toHaveBeenCalledTimes(0);
      });
    });
    describe('when the provider requires the recipient to be linked to a funding source', () => {
      it('should return a recipient with an existing funding source', async () => {
        const { service, saveRecipientFn } = createMockRecipientService();
        const { status } = await service.lookupOrLink({
          ...mockCreateInput,
          provider: Provider.JPMC,
          referenceId: 'lookupOrLinkRecipient',
          fundingSourceReferenceId: mockJPMCFundingSource.referenceId,
        });
        expect(saveRecipientFn).toHaveBeenCalledTimes(0);
        expect(status).toEqual(StatusCodes.OK);
      });
      it('should save a recipient with a new funding source', async () => {
        const { service, saveRecipientFn } = createMockRecipientService();
        const { status } = await service.lookupOrLink({
          ...mockCreateInput,
          provider: Provider.JPMC,
          referenceId: 'lookupOrLinkRecipient',
          fundingSourceReferenceId: 'differentReferenceId',
        });
        expect(saveRecipientFn).toHaveBeenCalled();
        expect(status).toEqual(StatusCodes.CREATED);
      });
    });
  });
});

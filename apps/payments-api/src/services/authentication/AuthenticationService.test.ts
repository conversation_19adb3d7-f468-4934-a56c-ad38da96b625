import { mockLogger } from '@payments-test/setup/globals.js';
import { encodeBase64 } from '@payments/utilities/cryptography.js';
import { AuthorizationError } from '@payments/utilities/errors/extensions/index.js';
import AuthenticationService from '../authentication/AuthenticationService.js';
import ClientService from '../clients/ClientService.js';

const mockAuthenticationService = new AuthenticationService({
  getByClientId: vi.fn((clientId) =>
    clientId === 'existingClient'
      ? { clientId: 'existingClient', clientSecret: 'S3cr3t' }
      : undefined,
  ),
} as unknown as ClientService);

describe('AuthenticationService', () => {
  describe('authenticate', () => {
    describe('when the client cannot be authenticated due to invalid credentials', () => {
      describe('when there is no matching clientId', () => {
        it('should throw an AuthorizationError and log it', async () => {
          try {
            await mockAuthenticationService.authenticate({
              clientId: 'nonExistingClient',
              clientSecret: 'S3cr3t',
            });
            expect.fail('Expected an error to be thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(AuthorizationError);
            expect(error).toHaveProperty('message', 'Invalid Credentials');
          }
          expect(mockLogger.error).toHaveBeenCalledWith(
            { clientId: 'nonExistingClient' },
            'AuthenticationService.authenticate: No client found',
          );
        });
      });

      describe('when the client secret does not match', () => {
        it('should throw an AuthorizationError and log it', async () => {
          try {
            await mockAuthenticationService.authenticate({
              clientId: 'existingClient',
              clientSecret: 'Wrong Secret!',
            });
            expect.fail('Expected an error to be thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(AuthorizationError);
            expect(error).toHaveProperty('message', 'Invalid Credentials');
          }
          expect(mockLogger.error).toHaveBeenCalledWith(
            { clientId: 'existingClient' },
            'AuthenticationService.authenticate: Secret mismatch',
          );
        });
      });
    });

    describe('when the client is found using matching credentials', () => {
      describe('when there is a matching clientId and matching clientSecret', () => {
        it('should return a 200', async () => {
          const { status } = await mockAuthenticationService.authenticate({
            clientId: 'existingClient',
            clientSecret: encodeBase64('S3cr3t'),
          });
          expect(status).toEqual(200);
        });
      });
    });
  });
});

import { mockLogger } from '@payments-test/setup/globals.js';
import { ErrorCodes } from '@payments/@types/index.js';
import { CreateOrReloadPayment, PaymentMethod, PaymentStatus } from '@payments/@types/payment.js';
import { Provider } from '@payments/@types/provider.js';
import { PaymentRepository } from '@payments/@types/repository.js';
import ProviderRepositoryFactory from '@payments/repositories/providers/ProviderRepositoryFactory.js';
import { ValidationError } from '@payments/utilities/errors/extensions/index.js';
import { sendSlackNotification } from '../../utilities/slack/sendSlackNotification.js';
import AccountService from '../accounts/AccountService.js';
import RecipientService from '../recipients/RecipientService.js';
import PaymentService from './PaymentService.js';

vi.mock('../../utilities/slack/sendSlackNotification');

describe('PaymentService', () => {
  describe('create', () => {
    describe('when there is missing or invalid data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({ id: 'existingPayment' }),
            } as unknown as PaymentRepository,
            {
              get: vi.fn(() => ({
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              })),
            } as unknown as ProviderRepositoryFactory,
            {} as unknown as RecipientService,
            {} as unknown as AccountService,
          );

          await expect(
            service.create({
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
              client: { id: 'mockClientId' },
              referenceId: undefined as unknown as string,
            } as CreateOrReloadPayment),
          ).rejects.toThrowError(
            new ValidationError({ message: 'payment referenceId required for creation' }),
          );
        });
      });
      describe('when there is an existing payment with the provided referenceId', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({ id: 'existingPayment' }),
            } as unknown as PaymentRepository,
            {
              get: vi.fn(() => ({
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              })),
            } as unknown as ProviderRepositoryFactory,
            {} as unknown as RecipientService,
            {} as unknown as AccountService,
          );

          await expect(
            service.create({
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
              client: { id: 'mockClientId' },
              referenceId: 'existingPayment',
            } as unknown as CreateOrReloadPayment),
          ).rejects.toThrow(ValidationError);
        });
      });
      describe('when there is no recipient found for the provided referenceId', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as PaymentRepository,
            {
              get: vi.fn(() => ({
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              })),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({ body: {} }),
            } as unknown as RecipientService,
            {} as unknown as AccountService,
          );

          try {
            await service.create({
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe(
              'no recipient found for referenceId mockRecipientReferenceId and clientId mockClientId',
            );
          }
        });
      });
      describe('when there is no funding source found for the provided referenceId', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as PaymentRepository,
            {
              get: vi.fn(() => ({
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              })),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  id: 'mockRecipientId',
                  referenceId: 'mockRecipientReferenceId',
                  fundingSources: undefined,
                },
              }),
            } as unknown as RecipientService,
            {} as unknown as AccountService,
          );

          try {
            await service.create({
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
              client: { id: 'mockClientId' },
              recipientReferenceId: 'mockRecipientReferenceId',
              referenceId: 'mockReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe(
              'no recipient found for referenceId mockRecipientReferenceId and clientId mockClientId',
            );
          }
          expect(sendSlackNotification).toHaveBeenCalled();
        });
      });
      describe('when there is a non-retryable error in one of the provider services', () => {
        it('throws an Error with an appropriate message and send a Slack notification', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
              save: vi.fn().mockResolvedValueOnce({ id: 'mockPaymentId' }),
            } as unknown as PaymentRepository,
            {
              get: vi.fn().mockReturnValue({
                createPayment: vi.fn().mockResolvedValueOnce(undefined),
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              }),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  recipient: {
                    id: 'mockRecipientId',
                    referenceId: 'mockRecipientReferenceId',
                    fundingSources: [
                      {
                        id: 'mockFundingSourceId',
                        referenceId: 'mockFundingSourceReferenceId',
                        provider: Provider.JPMC,
                      },
                    ],
                  },
                },
              }),
            } as unknown as RecipientService,
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({
                id: 'mockAccountId',
                referenceId: 'mockAccountReferenceId',
                recipientId: 'mockRecipientId',
              }),
            } as unknown as AccountService,
          );

          try {
            await service.create({
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect((error as Error).message).toBe('issue creating payment with jpmc');
          }
          expect(sendSlackNotification).toHaveBeenCalled();
        });
      });
      describe('when there is a retryable error in one of the provider services', () => {
        it('throws an Error with an appropriate message and send a Slack notification', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
              save: vi.fn().mockResolvedValueOnce({ id: 'mockPaymentId' }),
            } as unknown as PaymentRepository,
            {
              get: vi.fn().mockReturnValue({
                createPayment: vi.fn().mockRejectedValueOnce(
                  new ValidationError({
                    message: 'Account Not Found',
                    errorCode: ErrorCodes.AccountNotFound,
                    retryable: true,
                  }),
                ),
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              }),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  recipient: {
                    id: 'mockRecipientId',
                    referenceId: 'mockRecipientReferenceId',
                    fundingSources: [
                      {
                        id: 'mockFundingSourceId',
                        referenceId: 'mockFundingSourceReferenceId',
                        provider: Provider.JPMC,
                      },
                    ],
                  },
                },
              }),
            } as unknown as RecipientService,
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({
                id: 'mockAccountId',
                referenceId: 'mockAccountReferenceId',
                recipientId: 'mockRecipientId',
              }),
            } as unknown as AccountService,
          );

          try {
            await service.create({
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe('Account Not Found');
          }
          expect(sendSlackNotification).not.toHaveBeenCalled();
        });
      });
      describe('when the repository fails to save the new row', () => {
        it('throws a DatabaseError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
              save: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as PaymentRepository,
            {
              get: vi.fn().mockReturnValue({
                createPayment: vi.fn().mockResolvedValueOnce({}),
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              }),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  recipient: {
                    id: 'mockRecipientId',
                    referenceId: 'mockRecipientReferenceId',
                    fundingSources: [
                      {
                        id: 'mockFundingSourceId',
                        referenceId: 'mockFundingSourceReferenceId',
                        provider: Provider.JPMC,
                      },
                    ],
                  },
                },
              }),
            } as unknown as RecipientService,
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({
                id: 'mockAccountId',
                referenceId: 'mockAccountReferenceId',
                recipientId: 'mockRecipientId',
              }),
            } as unknown as AccountService,
          );

          try {
            await service.create({
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect((error as Error).message).toBe('issue saving pending payment to database');
          }
          expect(sendSlackNotification).toHaveBeenCalled();
        });
      });
      describe('when there is no accountReferenceId provided when it is required', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
              save: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as PaymentRepository,
            {
              get: vi.fn().mockReturnValue({
                createPayment: vi.fn().mockResolvedValueOnce({}),
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              }),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  recipient: {
                    id: 'mockRecipientId',
                    referenceId: 'mockRecipientReferenceId',
                    fundingSources: [
                      {
                        id: 'mockFundingSourceId',
                        referenceId: 'mockFundingSourceReferenceId',
                        provider: Provider.JPMC,
                      },
                    ],
                  },
                },
              }),
            } as unknown as RecipientService,
            {} as unknown as AccountService,
          );

          try {
            await service.create({
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: undefined,
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe(
              'accountReferenceId must be provided for this payment',
            );
          }
        });
      });
      describe('when the there is no account found for the provided referenceId', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
              save: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as PaymentRepository,
            {
              get: vi.fn().mockReturnValue({
                createPayment: vi.fn().mockResolvedValueOnce({}),
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              }),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  recipient: {
                    id: 'mockRecipientId',
                    referenceId: 'mockRecipientReferenceId',
                    fundingSources: [
                      {
                        id: 'mockFundingSourceId',
                        referenceId: 'mockFundingSourceReferenceId',
                        provider: Provider.JPMC,
                      },
                    ],
                  },
                },
              }),
            } as unknown as RecipientService,
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as AccountService,
          );

          try {
            await service.create({
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe(
              'no account found for referenceId mockAccountReferenceId',
            );
          }
        });
      });
      describe('when the referenced account does not belong to the referenced recipient', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          const service = new PaymentService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
              save: vi.fn().mockResolvedValueOnce(undefined),
            } as unknown as PaymentRepository,
            {
              get: vi.fn().mockReturnValue({
                createPayment: vi.fn().mockResolvedValueOnce({}),
                needsRecipientLink: vi.fn(() => true),
                shouldUpdateStatus: vi.fn(() => false),
                accountIsRequired: vi.fn(() => true),
              }),
            } as unknown as ProviderRepositoryFactory,
            {
              lookupOrLink: vi.fn().mockResolvedValueOnce({
                body: {
                  recipient: {
                    id: 'mockRecipientId',
                    referenceId: 'mockRecipientReferenceId',
                    fundingSources: [
                      {
                        id: 'mockFundingSourceId',
                        referenceId: 'mockFundingSourceReferenceId',
                        provider: Provider.JPMC,
                      },
                    ],
                  },
                },
              }),
            } as unknown as RecipientService,
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({
                id: 'mockAccountId',
                referenceId: 'mockAccountReferenceId',
                recipientId: 'mockOtherRecipientId',
              }),
            } as unknown as AccountService,
          );

          try {
            await service.create({
              client: { id: 'mockClientId' },
              referenceId: 'mockReferenceId',
              recipientReferenceId: 'mockRecipientReferenceId',
              accountReferenceId: 'mockAccountReferenceId',
              fundingSourceReferenceId: 'mockFundingSourceReferenceId',
            } as CreateOrReloadPayment);
            expect.fail('Should have thrown');
          } catch (error) {
            expect(error).toBeInstanceOf(ValidationError);
            expect((error as Error).message).toBe(
              'account mockAccountReferenceId not associated with recipient mockRecipientReferenceId',
            );
          }
        });
      });
    });

    describe('when payment creation is successful', () => {
      it('should return a 201 and the new row', async () => {
        const mockSavePayment = vi
          .fn()
          .mockResolvedValueOnce({ id: 'mockPaymentId' })
          .mockResolvedValueOnce({ id: 'mockUpdatedPaymentId' });
        const mockProviderCreatePayment = vi.fn().mockResolvedValueOnce({ key: 'value' });
        const service = new PaymentService(
          {
            findByReferenceId: vi.fn().mockResolvedValueOnce(undefined),
            save: mockSavePayment,
          } as unknown as PaymentRepository,
          {
            get: vi.fn().mockReturnValue({
              createPayment: mockProviderCreatePayment,
              needsRecipientLink: vi.fn(() => true),
              shouldUpdateStatus: vi.fn(() => false),
              accountIsRequired: vi.fn(() => true),
            }),
          } as unknown as ProviderRepositoryFactory,
          {
            lookupOrLink: vi.fn().mockResolvedValueOnce({
              body: {
                recipient: {
                  id: 'mockRecipientId',
                  referenceId: 'mockRecipientReferenceId',
                  fundingSources: [
                    {
                      id: 'mockFundingSourceId',
                      referenceId: 'mockFundingSourceReferenceId',
                      provider: Provider.JPMC,
                    },
                  ],
                },
              },
            }),
          } as unknown as RecipientService,
          {
            findByReferenceId: vi.fn().mockResolvedValueOnce({
              id: 'mockAccountId',
              referenceId: 'mockAccountReferenceId',
              recipientId: 'mockRecipientId',
            }),
          } as unknown as AccountService,
        );

        const {
          status,
          body: { payment },
        } = await service.create({
          client: { id: 'mockClientId' },
          referenceId: 'mockReferenceId',
          recipientReferenceId: 'mockRecipientReferenceId',
          accountReferenceId: 'mockAccountReferenceId',
          fundingSourceReferenceId: 'mockFundingSourceReferenceId',
          callbackUrl: 'mockCallbackUrl',
          paymentMethod: PaymentMethod.ACH,
          amount: 30000,
        } as CreateOrReloadPayment);

        expect(status).toEqual(201);
        expect(payment).toEqual({ id: 'mockUpdatedPaymentId' });
        expect(mockLogger.info).toHaveBeenCalledWith(
          'PaymentService.create: created new record with id mockPaymentId',
        );
        expect(mockSavePayment).toHaveBeenNthCalledWith(1, {
          referenceId: 'mockReferenceId',
          recipientId: 'mockRecipientId',
          accountId: 'mockAccountId',
          fundingSourceId: 'mockFundingSourceId',
          status: PaymentStatus.Pending,
          callbackUrl: 'mockCallbackUrl',
          paymentMethod: PaymentMethod.ACH,
        });
        expect(mockProviderCreatePayment).toHaveBeenCalledWith({
          client: { id: 'mockClientId' },
          referenceId: 'mockReferenceId',
          recipientReferenceId: 'mockRecipientReferenceId',
          accountReferenceId: 'mockAccountReferenceId',
          fundingSourceReferenceId: 'mockFundingSourceReferenceId',
          callbackUrl: 'mockCallbackUrl',
          paymentMethod: PaymentMethod.ACH,
          amount: 30000,
          fundingSource: {
            id: 'mockFundingSourceId',
            referenceId: 'mockFundingSourceReferenceId',
            provider: Provider.JPMC,
          },
          recipient: {
            id: 'mockRecipientId',
            referenceId: 'mockRecipientReferenceId',
            fundingSources: [
              {
                id: 'mockFundingSourceId',
                referenceId: 'mockFundingSourceReferenceId',
                provider: Provider.JPMC,
              },
            ],
          },
          account: {
            id: 'mockAccountId',
            referenceId: 'mockAccountReferenceId',
            recipientId: 'mockRecipientId',
          },
          payment: { id: 'mockPaymentId' },
        });
        expect(mockSavePayment).toHaveBeenNthCalledWith(2, {
          id: 'mockPaymentId',
          status: PaymentStatus.Initiated,
          callbackUrl: 'mockCallbackUrl',
          amount: 30000,
          keys: { key: 'value' },
        });
      });
    });
  });
});

import MailgunRepository from '@payments/repositories/MailgunRepository.js';
import { Mock } from 'vitest';
import EmailService from './EmailService.js';

const originalEnv = { ...process.env };

describe('EmailService', () => {
  let sendEmailFn: Mock;
  let service: EmailService;

  beforeEach(() => {
    process.env.BEAM_FINANCE_EMAIL = '<EMAIL>';
    sendEmailFn = vi.fn();
    const emailRepository = { sendEmail: sendEmailFn } as unknown as MailgunRepository;
    service = new EmailService(emailRepository);
  });
  afterAll(() => {
    process.env = { ...originalEnv };
  });

  const mockEmail = { recipient: ['test'], subject: 'test', text: 'test' };
  it('should call the repository send method', async () => {
    await service.sendEmail(mockEmail);
    expect(sendEmailFn).toBeCalledWith({ ...mockEmail, from: '<EMAIL>' });
  });

  it('should log if the repository throws an error ', async () => {
    sendEmailFn.mockRejectedValueOnce(new Error('Bad'));
    await expect(async () => await service.sendEmail(mockEmail)).rejects.toThrowError('Bad');
  });
});

import { mockLogger } from '@payments-test/setup/globals.js';
import {
  CallbackBody,
  CallbackEntities,
  PaymentUpdate,
  TransferUpdate,
} from '@payments/@types/callback.js';
import { PaymentStatus } from '@payments/@types/payment.js';
import { Provider } from '@payments/@types/provider.js';
import ProviderRepositoryFactory from '@payments/repositories/providers/ProviderRepositoryFactory.js';
import { ValidationError } from '@payments/utilities/errors/extensions/index.js';
import axios from 'axios';
import { Mock, Mocked } from 'vitest';
import PaymentService from '../payments/PaymentService.js';
import TransferService from '../transfers/TransferService.js';
import CallbackService from './CallbackService.js';

vi.mock('axios');
const mockedAxios = axios as Mocked<typeof axios>;

const { mockedFailedPaymentCallback } = vi.hoisted(() => {
  return { mockedFailedPaymentCallback: vi.fn() };
});
vi.mock('../../utilities/slack', () => ({
  sendSlackNotification: vi.fn(),
  failedPaymentCallback: mockedFailedPaymentCallback,
}));

const mockParams = { provider: Provider.JPMC };
const mockCallbackBody = {
  id: 'mockPaymentId',
  status: PaymentStatus.Succeeded,
  amount: 100,
} as unknown as CallbackBody;
const mockError = new Error("bad news, pal, it's busted");
const mockPaymentUpdate = {
  entityType: CallbackEntities.Payment,
  id: 'mockPaymentId',
  status: PaymentStatus.Succeeded,
  amount: 100,
  callbackUrl: 'https://foo.bar/callback',
  referenceId: 'mockReferenceId',
} as PaymentUpdate;
const mockTransferUpdate = {
  entityType: CallbackEntities.Transfer,
  id: 'mockTransferId',
  status: PaymentStatus.Succeeded,
  amount: 100,
} as TransferUpdate;

describe('CallbackService', () => {
  let getCallbackIdentifiersFn: Mock;
  let getProviderFn: Mock;
  let handleCallbackFn: Mock;
  let paymentFindFn: Mock;
  let paymentUpdateFn: Mock;
  let transferFindFn: Mock;
  let transferUpdateFn: Mock;
  let mockCallbackService: CallbackService;
  beforeEach(() => {
    getCallbackIdentifiersFn = vi.fn();
    handleCallbackFn = vi.fn();
    getProviderFn = vi.fn().mockReturnValue({
      getCallbackIdentifiers: getCallbackIdentifiersFn,
      handleCallback: handleCallbackFn,
    });
    paymentFindFn = vi.fn();
    paymentUpdateFn = vi.fn();
    transferFindFn = vi.fn();
    transferUpdateFn = vi.fn();
    mockCallbackService = new CallbackService(
      { get: getProviderFn } as unknown as ProviderRepositoryFactory,
      { find: paymentFindFn, update: paymentUpdateFn } as unknown as PaymentService,
      { findById: transferFindFn, update: transferUpdateFn } as unknown as TransferService,
    );
  });
  describe('forwardCallback', () => {
    describe('payment updates', () => {
      it('should forward the callback to the callback url', async () => {
        await mockCallbackService.forwardCallback(mockPaymentUpdate);
        expect(mockedAxios.request).toHaveBeenCalledWith({
          url: mockPaymentUpdate.callbackUrl,
          method: 'POST',
          data: {
            referenceId: mockPaymentUpdate.referenceId,
            status: mockPaymentUpdate.status,
            amount: mockPaymentUpdate.amount,
          },
        });
      });
      describe('when the request fails', () => {
        it('should never throw an error', async () => {
          mockedAxios.request = vi.fn().mockRejectedValue(mockError);
          expect(() => mockCallbackService.forwardCallback(mockPaymentUpdate)).not.toThrow();
        });
        // TODO: extend tests to check for the error being logged
        // describe('when the response is not 2xx', () => {});
        // describe('when the request fails', () => {});
        // describe('when an unexpected error happens', () => {});
      });
    });
  });
  describe('handle', () => {
    describe('error states', () => {
      it('should log if an invalid provider is used and return a 200', async () => {
        const response = await mockCallbackService.handle(
          { provider: 'Invalid' },
          mockCallbackBody,
        );
        expect(mockLogger.error).toHaveBeenCalledWith(
          { error: expect.objectContaining({ message: 'invalid provider: Invalid' }) },
          'CallbackService.handle: unexpected error >',
        );
        expect(response.status).toBe(200);
      });
      it('should log if there is an issue with the provider repo and return a 200', async () => {
        getProviderFn.mockReturnValueOnce(undefined);
        const response = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(mockLogger.error).toHaveBeenCalledWith(
          { error: expect.objectContaining({ message: `error creating provider repository for ${mockParams.provider}` }) },
          'CallbackService.handle: unexpected error >',
        );
        expect(response.status).toBe(200);
      });
      it('should log if no identifier is found and return a 200', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({});
        const response = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            error: new ValidationError({
              message: 'no identifiers found in callback payload for jpmc',
              provider: mockParams.provider,
            }),
          },
          'CallbackService.handle: unexpected error >',
        );
        expect(response.status).toBe(200);
      });
      it('should log if there is no entity found in the database to update and return a 200', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce(undefined);
        transferFindFn.mockReturnValueOnce(undefined);
        const response = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            error: new ValidationError({
              message: 'no entity found for referenceId mockCallbackValue',
              provider: mockParams.provider,
            }),
          },
          'CallbackService.handle: unexpected error >',
        );
        expect(response.status).toBe(200);
      });
      it('should log if the provider callback handler fails and return a 200', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce(mockPaymentUpdate);
        handleCallbackFn.mockReturnValueOnce(undefined);
        const response = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(mockLogger.error).toHaveBeenCalledWith(
          { error: new Error(`issue handling callback with ${mockParams.provider}`) },
          'CallbackService.handle: unexpected error >',
        );
        expect(response.status).toBe(200);
      });
      it('should log if the payment entity update fails and return a 200', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce(mockPaymentUpdate);
        handleCallbackFn.mockReturnValueOnce(mockPaymentUpdate);
        paymentUpdateFn.mockReturnValueOnce(undefined);
        const response = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(mockLogger.error).toHaveBeenCalledWith(
          expect.objectContaining({ error: expect.objectContaining({ message: 'issue updating payment or transfer in database' }) }),
          expect.any(String),
        );
        expect(response.status).toBe(200);
      });
      it('should log if the transfer entity update fails and return a 200', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce(undefined);
        transferFindFn.mockReturnValueOnce(mockTransferUpdate);
        handleCallbackFn.mockReturnValueOnce(mockTransferUpdate);
        transferUpdateFn.mockReturnValueOnce(undefined);
        const response = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(mockLogger.error).toHaveBeenCalledWith(
          expect.objectContaining({ error: expect.objectContaining({ message: 'issue updating payment or transfer in database' }) }),
          expect.any(String),
        );
        expect(response.status).toBe(200);
      });
      it('should log an error and send a slack notification if the callback returns "rejected"', async () => {
        const rejected = {
          ...mockCallbackBody,
          status: PaymentStatus.Rejected,
          details: { error: { message: 'An error!' } },
        } as unknown as CallbackBody;
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce({
          ...mockPaymentUpdate,
          status: PaymentStatus.Initiated,
        });
        transferFindFn.mockReturnValueOnce(undefined);
        handleCallbackFn.mockReturnValueOnce({ ...rejected, entityType: 'payment' });
        paymentUpdateFn.mockReturnValueOnce({
          ...mockPaymentUpdate,
          status: PaymentStatus.Rejected,
        });
        await mockCallbackService.handle(mockParams, rejected);
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            ...mockPaymentUpdate,
            status: PaymentStatus.Rejected,
            details: { error: { message: 'An error!' } },
          },
          'CallbackService.handle: the payment was was updated to a failed status, see details >',
        );
        expect(mockedFailedPaymentCallback).toHaveBeenCalledWith({
          message: 'Callback returned status equivalent to rejected',
          updatedEntity: { ...mockPaymentUpdate, status: PaymentStatus.Rejected },
          details: { error: { message: 'An error!' } },
        });
      });
      it('should log an error and send a slack notification if the callback returns "returned"', async () => {
        const returned = {
          ...mockCallbackBody,
          status: PaymentStatus.Returned,
          details: { error: { message: 'An error!' } },
        } as unknown as CallbackBody;
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce({
          ...mockPaymentUpdate,
          status: PaymentStatus.Initiated,
        });
        transferFindFn.mockReturnValueOnce(undefined);
        handleCallbackFn.mockReturnValueOnce({
          ...returned,
          entityType: 'payment',
        });
        paymentUpdateFn.mockReturnValueOnce({
          ...mockPaymentUpdate,
          status: PaymentStatus.Returned,
        });
        await mockCallbackService.handle(mockParams, returned);
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            ...mockPaymentUpdate,
            status: PaymentStatus.Returned,
            details: { error: { message: 'An error!' } },
          },
          'CallbackService.handle: the payment was was updated to a failed status, see details >',
        );
        expect(mockedFailedPaymentCallback).toHaveBeenCalledWith({
          message: 'Callback returned status equivalent to returned',
          updatedEntity: { ...mockPaymentUpdate, status: PaymentStatus.Returned },
          details: { error: { message: 'An error!' } },
        });
      });
      it('should log an error and send a slack notification if the callback returns "failed"', async () => {
        const failed = {
          ...mockCallbackBody,
          status: PaymentStatus.Failed,
          details: { error: { message: 'An error!' } },
        } as unknown as CallbackBody;
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce({
          ...mockPaymentUpdate,
          status: PaymentStatus.Initiated,
        });
        transferFindFn.mockReturnValueOnce(undefined);
        handleCallbackFn.mockReturnValueOnce({ ...failed, entityType: 'payment' });
        paymentUpdateFn.mockReturnValueOnce({ ...mockPaymentUpdate, status: PaymentStatus.Failed });
        await mockCallbackService.handle(mockParams, failed);
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            ...mockPaymentUpdate,
            status: PaymentStatus.Failed,
            details: { error: { message: 'An error!' } },
          },
          'CallbackService.handle: the payment was was updated to a failed status, see details >',
        );
        expect(mockedFailedPaymentCallback).toHaveBeenCalledWith({
          message: 'Callback returned status equivalent to failed',
          updatedEntity: { ...mockPaymentUpdate, status: PaymentStatus.Failed },
          details: { error: { message: 'An error!' } },
        });
      });
      it('should not send a slack notification if the callback returns a retryable error', async () => {
        const body = {
          ...mockCallbackBody,
          status: PaymentStatus.Rejected,
        } as unknown as CallbackBody;
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce({
          ...mockPaymentUpdate,
          status: PaymentStatus.Initiated,
        });
        transferFindFn.mockReturnValueOnce(undefined);
        handleCallbackFn.mockReturnValueOnce({
          ...body,
          entityType: 'payment',
          details: { error: { retryable: true } },
        });
        paymentUpdateFn.mockReturnValueOnce({ ...mockPaymentUpdate, status: PaymentStatus.Failed });

        await mockCallbackService.handle(mockParams, body);

        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            ...mockPaymentUpdate,
            details: { error: { retryable: true } },
            status: PaymentStatus.Failed,
          },
          'CallbackService.handle: the payment was was updated to a failed status, see details >',
        );
        expect(mockLogger.info).toHaveBeenCalledWith(
          'CallbackService.handle: skipping slack notification for retryable error for payment mockPaymentId',
        );
        expect(mockedFailedPaymentCallback).not.toBeCalled();
      });
    });
    describe('payment updates', () => {
      it('should return a 200, forward the callback, and return the updated payment if successful', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce(mockPaymentUpdate);
        handleCallbackFn.mockReturnValueOnce(mockPaymentUpdate);
        paymentUpdateFn.mockReturnValueOnce(mockPaymentUpdate);
        const { status, body } = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(status).toEqual(200);
        expect(axios.request).toBeCalled();
        expect(body?.message).toEqual('callback received');
      });
    });
    describe('transfer updates', () => {
      it('should return a 200, not forward the callback, and return the updated transfer if successful', async () => {
        getCallbackIdentifiersFn.mockReturnValueOnce({ referenceId: 'mockCallbackValue' });
        paymentFindFn.mockReturnValueOnce(undefined);
        transferFindFn.mockReturnValueOnce(mockTransferUpdate);
        handleCallbackFn.mockReturnValueOnce(mockTransferUpdate);
        transferUpdateFn.mockReturnValueOnce(mockTransferUpdate);
        const { status, body } = await mockCallbackService.handle(mockParams, mockCallbackBody);
        expect(status).toEqual(200);
        expect(axios.request).not.toBeCalled();
        expect(body?.message).toEqual('callback received');
      });
    });
  });
});

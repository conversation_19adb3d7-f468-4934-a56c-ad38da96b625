import { mockLogger } from '@payments-test/setup/globals.js';
import { TransferMessage } from '@payments/@types/consumer.js';
import { PaymentMethod, PaymentStatus, Provider } from '@payments/@types/index.js';
import { PaymentRepository, TransferRepository } from '@payments/@types/repository.js';
import ProviderRepositoryFactory from '@payments/repositories/providers/ProviderRepositoryFactory.js';
import { ServiceError, ValidationError } from '@payments/utilities/errors/extensions/index.js';
import dayjs from 'dayjs';
import { Mock } from 'vitest';
import AccountService from '../accounts/AccountService.js';
import EmailService from '../email/EmailService.js';
import RecipientService from '../recipients/RecipientService.js';
import TransferService from './TransferService.js';
import { getTransferConfig } from './utilities.js';

vi.mock('../../utilities/slack/sendSlackNotification');
vi.mock('./utilities', () => ({
  getTransferConfig: vi.fn().mockReturnValue({
    transferThreshold: 100000,
    paymentMethods: [PaymentMethod.PhysicalCard],
    emailGenerators: {
      failure: vi.fn(() => 'mockFailureEmailContent'),
      success: vi.fn(() => 'mockSuccessEmailContent'),
    },
    getTransferWindow: vi.fn().mockReturnValue({
      start: dayjs('2020-01-01T16:00:00.000Z'),
      end: dayjs('2020-01-01T16:00:00.000Z').subtract(24, 'hours'),
    }),
  }),
}));

describe('TransferService', () => {
  const mockPayload: TransferMessage = {
    id: 'mockMessageId',
    payload: { provider: Provider.USIO },
  };
  const mockFundingSourcePayments = [
    {
      id: 'mockFundingSource1',
      combinedTotal: 20000,
      paymentIds: ['mockPaymentId1', 'mockPaymentId2'],
      physicalCardTotal: 20000,
      virtualCardTotal: 0,
    },
  ];
  const mockRecipient = {
    id: 'mockRecipientId',
    referenceId: 'mockRecipientReferenceId',
    fundingSources: [{ id: 'mockFundingSource1' }],
  };
  const mockAccount = { id: 'mockAccountId', referenceId: 'mockAccountReferenceId' };

  describe('handleMessage', () => {
    let mockAccountFindFn: Mock;
    let mockEmailFn: Mock;
    let mockPaymentAggregateFn: Mock;
    let mockPaymentFindFn: Mock;
    let mockPaymentUpdateFn: Mock;
    let mockProviderCreatePaymentFn: Mock;
    let mockProviderGetFn: Mock;
    let mockRecipientFindFn: Mock;
    let mockRecipientLinkFn: Mock;
    let mockTransferFindOrFail: Mock;
    let mockTransferSaveFn: Mock;
    let mockTransferService: TransferService;
    beforeEach(() => {
      mockAccountFindFn = vi.fn();
      mockEmailFn = vi.fn();
      mockPaymentAggregateFn = vi.fn();
      mockPaymentFindFn = vi.fn();
      mockPaymentUpdateFn = vi.fn();
      mockProviderCreatePaymentFn = vi.fn();
      mockProviderGetFn = vi.fn(() => ({ createPayment: mockProviderCreatePaymentFn }));
      mockRecipientFindFn = vi.fn();
      mockRecipientLinkFn = vi.fn();
      mockTransferFindOrFail = vi.fn();
      mockTransferSaveFn = vi.fn();
      mockTransferService = new TransferService(
        {
          findOneByOrFail: mockTransferFindOrFail,
          save: mockTransferSaveFn,
        } as unknown as TransferRepository,
        { findByReferenceId: mockAccountFindFn } as unknown as AccountService,
        { sendEmail: mockEmailFn } as unknown as EmailService,
        {
          aggregatePaymentsByFundingSource: mockPaymentAggregateFn,
          update: mockPaymentUpdateFn,
          find: mockPaymentFindFn,
        } as unknown as PaymentRepository,
        { get: mockProviderGetFn } as unknown as ProviderRepositoryFactory,
        {
          findByReferenceId: mockRecipientFindFn,
          linkRecipientToFundingSource: mockRecipientLinkFn,
        } as unknown as RecipientService,
      );
    });
    describe('input validation', () => {
      it('should throw if the payload is missing a provider', async () => {
        await expect(
          async () => await mockTransferService.handleMessage({} as unknown as TransferMessage),
        ).rejects.toThrow(
          new ValidationError({
            message: 'TransferService.handleMessage: invalid provider undefined',
          }),
        );
      });
      it('should throw if the provider is not Usio', async () => {
        await expect(
          async () =>
            await mockTransferService.handleMessage({
              payload: { provider: Provider.JPMC },
            } as unknown as TransferMessage),
        ).rejects.toThrow(
          new ValidationError({
            message: `TransferService.handleMessage: invalid provider ${Provider.JPMC}`,
          }),
        );
      });
    });
    describe('data retrieval', () => {
      it('should call the paymentRepository to aggregate payments according to the provider config', async () => {
        await mockTransferService.handleMessage(mockPayload);
        expect(mockPaymentAggregateFn).toBeCalledWith([PaymentMethod.PhysicalCard], {
          start: dayjs('2020-01-01T16:00:00.000Z'),
          end: dayjs('2020-01-01T16:00:00.000Z').subtract(24, 'hours'),
        });
      });
      it('should use the transfer window override if provided in the message', async () => {
        await mockTransferService.handleMessage({
          id: mockPayload.id,
          payload: { ...mockPayload.payload, windowHoursOverride: 120 },
        });
        expect(getTransferConfig).toBeCalledWith('usio', 120);
      });
      it('should log an info message and return early if no payments are found', async () => {
        mockPaymentAggregateFn.mockResolvedValueOnce([]);
        await mockTransferService.handleMessage(mockPayload);
        expect(mockPaymentAggregateFn).toBeCalled();
        expect(mockLogger.info).toHaveBeenNthCalledWith(
          3,
          'TransferService.handleMessage: no payments found for provider usio',
        );
        expect(mockRecipientFindFn).not.toBeCalled();
        expect(mockAccountFindFn).not.toBeCalled();
      });
      it('should throw an error if the provider recipient is not found', async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockRecipientFindFn.mockReturnValueOnce(undefined);
        await expect(
          async () => await mockTransferService.handleMessage(mockPayload),
        ).rejects.toThrow(
          new ServiceError({
            message: 'TransferService.handleMessage: usio account or recipient not found > {}',
          }),
        );
      });
      it('should throw an error if the provider account is not found', async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(undefined);
        await expect(
          async () => await mockTransferService.handleMessage(mockPayload),
        ).rejects.toThrow(
          new ServiceError({
            message: `TransferService.handleMessage: usio account or recipient not found > ${JSON.stringify(
              { recipient: mockRecipient },
            )}`,
          }),
        );
      });
    });
    describe('sending funding source transfers', () => {
      it('should skip the ACH transfer if the funding source has skipACHTransfer as true', async () => {
        mockPaymentAggregateFn.mockResolvedValue([
          { ...mockFundingSourcePayments[0], keys: { skipACHTransfer: true } },
        ]);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockProviderCreatePaymentFn).not.toHaveBeenCalled();
      });
      it('should not skip the ACH transfer if the funding source has skipACHTransfer as false', async () => {
        const mockFundingSource = {
          ...mockFundingSourcePayments[0],
          keys: { skipACHTransfer: false },
        };
        mockPaymentAggregateFn.mockResolvedValue([mockFundingSource]);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockProviderCreatePaymentFn).toBeCalledWith({
          fundingSource: mockFundingSource,
          account: mockAccount,
          recipient: mockRecipient,
          referenceId: 'mockTransferId',
          amount: mockFundingSourcePayments[0].combinedTotal,
          payment: { transactionNumber: 'mockTransactionNumber' },
          paymentMethod: PaymentMethod.ACH,
        });
      });
      it('should save a pending transfer row into the database', async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockTransferSaveFn).toHaveBeenCalledWith({
          amount: mockFundingSourcePayments[0].combinedTotal,
          fundingSourceId: mockFundingSourcePayments[0].id,
          recipientId: mockRecipient.id,
          accountId: mockAccount.id,
          status: PaymentStatus.Pending,
        });
      });
      it('should update the transfer row to failed if there is an error thrown', async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        mockProviderCreatePaymentFn.mockRejectedValueOnce(new Error('bad bad news'));
        await mockTransferService.handleMessage(mockPayload);
        expect(mockTransferSaveFn).toHaveBeenCalledWith({
          id: 'mockTransferId',
          status: PaymentStatus.Failed,
        });
      });
      it('should attempt to link the funding source to the transfer recipient if the link does not exist', async () => {
        const unlinkedFundingSource = {
          ...mockFundingSourcePayments[0],
          id: 'mockUnlinkedFundingSourceId',
        };
        mockPaymentAggregateFn.mockResolvedValue([unlinkedFundingSource]);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockRecipientLinkFn).toBeCalledWith({
          fundingSource: unlinkedFundingSource,
          recipient: mockRecipient,
        });
      });
      it("should create an ACH transfer with the funding source's provider", async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockProviderCreatePaymentFn).toBeCalledWith({
          fundingSource: mockFundingSourcePayments[0],
          account: mockAccount,
          recipient: mockRecipient,
          referenceId: 'mockTransferId',
          amount: mockFundingSourcePayments[0].combinedTotal,
          payment: { transactionNumber: 'mockTransactionNumber' },
          paymentMethod: PaymentMethod.ACH,
        });
      });
      it('should save the transfer with the returned keys and a status of initiated', async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockTransferSaveFn).toHaveBeenNthCalledWith(2, {
          id: 'mockTransferId',
          status: PaymentStatus.Initiated,
          keys: { keyOne: 'good' },
        });
      });
      it('should update the payment rows fulfilled by the transfer', async () => {
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSourcePayments);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSourcePayments[0].paymentIds.map((id) => ({
            id,
            amount:
              mockFundingSourcePayments[0].combinedTotal /
              mockFundingSourcePayments[0].paymentIds.length,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockReturnValueOnce(mockRecipient);
        mockAccountFindFn.mockReturnValueOnce(mockAccount);
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId',
          transactionNumber: 'mockTransactionNumber',
        });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId' });
        await mockTransferService.handleMessage(mockPayload);
        expect(mockPaymentUpdateFn).toBeCalledWith(
          expect.objectContaining({
            id: expect.objectContaining({
              _type: 'in',
              _value: mockFundingSourcePayments[0].paymentIds,
            }),
          }),
          { transferId: 'mockTransferId' },
        );
      });
      it('should continue processing transfers if one fails for any reason', async () => {
        const mockFundingSources = [...Array(4)].map((_, idx) => ({
          id: `mockFundingSource${idx + 1}`,
          combinedTotal: 10000 * (idx + 1),
          paymentIds: [
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
          ],
        }));
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSources);
        mockFundingSources.map(({ paymentIds, combinedTotal }) =>
          mockPaymentFindFn.mockResolvedValueOnce(
            paymentIds.map((id) => ({
              id,
              amount: combinedTotal / paymentIds.length,
              paymentMethod: PaymentMethod.PhysicalCard,
            })),
          ),
        );
        mockRecipientFindFn.mockReturnValue(mockRecipient);
        mockAccountFindFn.mockReturnValue(mockAccount);

        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId1',
          transactionNumber: 'mockTransactionNumber1',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId2',
          transactionNumber: 'mockTransactionNumber2',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId3',
          transactionNumber: 'mockTransactionNumber3',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId4',
          transactionNumber: 'mockTransactionNumber4',
        });

        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockRejectedValueOnce(new Error('this one failed'));
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });

        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId1' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId2' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId3' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId4' });

        await mockTransferService.handleMessage(mockPayload);
        expect(mockProviderCreatePaymentFn).toHaveBeenCalledTimes(4);
        expect(mockPaymentUpdateFn).toHaveBeenCalledTimes(4);
        expect(mockTransferSaveFn).toHaveBeenCalledTimes(8);
        expect(mockTransferSaveFn).toHaveBeenCalledWith({
          id: 'mockTransferId2',
          status: PaymentStatus.Failed,
        });
        expect(mockLogger.error).toHaveBeenCalledWith(
          {
            error: 'this one failed',
            fundingSource: expect.objectContaining({ id: 'mockFundingSource2' }),
          },
          'TransferService.initiateACH: unexpected error creating transfer for funding source mockFundingSource2',
        );
      });
      it('should should send the correct amount for each funding source', async () => {
        const mockFundingSources = [...Array(4)].map((_, idx) => ({
          id: `mockFundingSource${idx + 1}`,
          combinedTotal: 10000 * (idx + 1),
          paymentIds: [
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
          ],
        }));
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSources);
        mockFundingSources.map(({ paymentIds, combinedTotal }) =>
          mockPaymentFindFn.mockResolvedValueOnce(
            paymentIds.map((id) => ({
              id,
              amount: combinedTotal / paymentIds.length,
              paymentMethod: PaymentMethod.PhysicalCard,
            })),
          ),
        );
        mockRecipientFindFn.mockReturnValue(mockRecipient);
        mockAccountFindFn.mockReturnValue(mockAccount);

        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId1',
          transactionNumber: 'mockTransactionNumber1',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId2',
          transactionNumber: 'mockTransactionNumber2',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId3',
          transactionNumber: 'mockTransactionNumber3',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId4',
          transactionNumber: 'mockTransactionNumber4',
        });

        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });

        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId1' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId2' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId3' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId4' });

        await mockTransferService.handleMessage(mockPayload);
        [...Array(4)].map((_, idx) => {
          const id = idx + 1;
          expect(mockProviderCreatePaymentFn).toHaveBeenNthCalledWith(id, {
            fundingSource: expect.objectContaining({ id: `mockFundingSource${id}` }),
            account: mockAccount,
            recipient: mockRecipient,
            referenceId: `mockTransferId${id}`,
            amount: id * 10000,
            payment: { transactionNumber: `mockTransactionNumber${id}` },
            paymentMethod: PaymentMethod.ACH,
          });
        });
      });
      it('should create an multiple transfers if it is above the funding source threshold', async () => {
        const mockFundingSource = {
          ...mockFundingSourcePayments[0],
          combinedTotal: 160000,
          keys: { fakeKey1: 'test', usioKey: 'not allowed' },
          paymentIds: ['mockPaymentId1', 'mockPaymentId2', 'mockPaymentId3', 'mockPaymentId4'],
        };
        mockPaymentAggregateFn.mockResolvedValueOnce([mockFundingSource]);
        mockPaymentFindFn.mockResolvedValueOnce(
          mockFundingSource.paymentIds.map((id) => ({
            id,
            amount: 40000,
            paymentMethod: PaymentMethod.PhysicalCard,
          })),
        );
        mockRecipientFindFn.mockResolvedValueOnce(mockRecipient);
        mockAccountFindFn.mockResolvedValueOnce(mockAccount);
        mockTransferSaveFn
          .mockReturnValueOnce({
            id: 'mockTransferId',
            transactionNumber: 'mockTransactionNumber',
            status: PaymentStatus.Pending,
          })
          .mockReturnValueOnce({
            id: 'mockTransferId2',
            transactionNumber: 'mockTransactionNumber2',
            status: PaymentStatus.Pending,
          })
          .mockReturnValueOnce({
            id: 'mockTransferId',
            transactionNumber: 'mockTransactionNumber',
            status: PaymentStatus.Initiated,
          })
          .mockReturnValueOnce({
            id: 'mockTransferId2',
            transactionNumber: 'mockTransactionNumber2',
            status: PaymentStatus.Initiated,
          });

        mockProviderCreatePaymentFn.mockResolvedValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockResolvedValueOnce({ keyOne: 'good' });

        mockTransferFindOrFail.mockResolvedValueOnce({ id: 'mockTransferId1' });
        mockTransferFindOrFail.mockResolvedValueOnce({ id: 'mockTransferId2' });
        await mockTransferService.handleMessage(mockPayload);

        expect(mockLogger.info).toHaveBeenCalledWith(
          'TransferService.transfer: creating 2 transfer(s) for funding source mockFundingSource1',
        );
        expect(mockProviderCreatePaymentFn).toHaveBeenCalledTimes(2);
        expect(mockProviderCreatePaymentFn).toBeCalledWith({
          fundingSource: {
            ...mockFundingSource,
            combinedTotal: 80000,
            paymentIds: ['mockPaymentId1', 'mockPaymentId2'],
            physicalCardTotal: 80000,
          },
          account: mockAccount,
          recipient: mockRecipient,
          referenceId: 'mockTransferId',
          amount: 80000,
          payment: { transactionNumber: 'mockTransactionNumber' },
          paymentMethod: PaymentMethod.ACH,
        });
        expect(mockProviderCreatePaymentFn).toBeCalledWith({
          fundingSource: {
            ...mockFundingSource,
            combinedTotal: 80000,
            paymentIds: ['mockPaymentId3', 'mockPaymentId4'],
            physicalCardTotal: 80000,
          },
          account: mockAccount,
          recipient: mockRecipient,
          referenceId: 'mockTransferId2',
          amount: 80000,
          payment: { transactionNumber: 'mockTransactionNumber2' },
          paymentMethod: PaymentMethod.ACH,
        });
      });
    });
    describe('notifications', () => {
      it('should send a failure email for each failed transfer', async () => {
        const mockFundingSources = [...Array(2)].map((_, idx) => ({
          id: `mockFundingSource${idx + 1}`,
          combinedTotal: 10000 * (idx + 1),
          keys: { fakeKey1: 'test', usioKey: 'not allowed' },
          paymentIds: [
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
          ],
        }));
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSources);
        mockFundingSources.map(({ paymentIds, combinedTotal }) =>
          mockPaymentFindFn.mockResolvedValueOnce(
            paymentIds.map((id) => ({
              id,
              amount: combinedTotal / paymentIds.length,
              paymentMethod: PaymentMethod.PhysicalCard,
            })),
          ),
        );
        mockRecipientFindFn.mockReturnValue(mockRecipient);
        mockAccountFindFn.mockReturnValue(mockAccount);

        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId1',
          transactionNumber: 'mockTransactionNumber1',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId2',
          transactionNumber: 'mockTransactionNumber2',
        });

        mockProviderCreatePaymentFn.mockRejectedValueOnce(new Error('this one failed'));
        mockProviderCreatePaymentFn.mockRejectedValueOnce(new Error('this one also failed'));

        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId1' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId2' });

        await mockTransferService.handleMessage(mockPayload);

        expect(mockLogger.error).toHaveBeenNthCalledWith(
          1,
          {
            error: 'this one failed',
            fundingSource: expect.objectContaining({ id: 'mockFundingSource1' }),
          },
          'TransferService.initiateACH: unexpected error creating transfer for funding source mockFundingSource1',
        );
        expect(mockLogger.error).toHaveBeenNthCalledWith(
          2,
          {
            error: 'this one also failed',
            fundingSource: expect.objectContaining({ id: 'mockFundingSource2' }),
          },
          'TransferService.initiateACH: unexpected error creating transfer for funding source mockFundingSource2',
        );
        expect(mockEmailFn).toHaveBeenCalledTimes(2);
        expect(mockEmailFn).toHaveBeenCalledWith('mockFailureEmailContent');
      });
      it('should send summarized success email for all successful transfers', async () => {
        const mockFundingSources = [...Array(2)].map((_, idx) => ({
          id: `mockFundingSource${idx + 1}`,
          combinedTotal: 10000,
          physicalTotal: 5000,
          virtualTotal: 5000,
          paymentIds: [
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
            `mockPaymentId${Math.floor(Math.random() * 1000)}`,
          ],
        }));
        mockPaymentAggregateFn.mockResolvedValue(mockFundingSources);
        mockFundingSources.map(({ paymentIds }) =>
          mockPaymentFindFn.mockResolvedValueOnce([
            { id: paymentIds[0], amount: 5000, paymentMethod: PaymentMethod.PhysicalCard },
            { id: paymentIds[1], amount: 5000, paymentMethod: PaymentMethod.VirtualCard },
          ]),
        );
        mockRecipientFindFn.mockReturnValue(mockRecipient);
        mockAccountFindFn.mockReturnValue(mockAccount);

        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId1',
          transactionNumber: 'mockTransactionNumber1',
        });
        mockTransferSaveFn.mockReturnValueOnce({
          id: 'mockTransferId2',
          transactionNumber: 'mockTransactionNumber2',
        });

        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });
        mockProviderCreatePaymentFn.mockReturnValueOnce({ keyOne: 'good' });

        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId1' });
        mockTransferFindOrFail.mockReturnValueOnce({ id: 'mockTransferId2' });

        await mockTransferService.handleMessage(mockPayload);

        expect(mockEmailFn).toBeCalledTimes(1);
        expect(mockEmailFn).toBeCalledWith('mockSuccessEmailContent');
      });
    });
  });
});

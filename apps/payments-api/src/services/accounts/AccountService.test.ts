import { mockLogger } from '@payments-test/setup/globals.js';
import { Account, AccountType, CreateAccount, UpdateAccount } from '@payments/@types/account.js';
import { FundingSource } from '@payments/@types/fundingSource.js';
import { ErrorCodes } from '@payments/@types/index.js';
import { Provider } from '@payments/@types/provider.js';
import { Recipient } from '@payments/@types/recipient.js';
import { AccountRepository, ScheduleRepository } from '@payments/@types/repository.js';
import ProviderRepositoryFactory from '@payments/repositories/providers/ProviderRepositoryFactory.js';
import { ValidationError } from '@payments/utilities/errors/extensions/index.js';
import { StatusCodes } from 'http-status-codes';
import { sendSlackNotification } from '../../utilities/slack/sendSlackNotification.js';
import FundingSourceService from '../FundingSourceService.js';
import RecipientService from '../recipients/RecipientService.js';
import AccountService from './AccountService.js';

vi.mock('../../utilities/slack/sendSlackNotification');

const mockFundingSource = {
  id: 'mockFundingSourceId',
  clientId: 'mockClientId',
  referenceId: 'mockFundingSourceReferenceId',
  provider: Provider.JPMC,
} as unknown as FundingSource;

const mockRecipient = {
  id: 'mockRecipientId',
  referenceId: 'mockRecipientReferenceId',
  provider: Provider.JPMC,
  fundingSources: [mockFundingSource],
  keys: { providerRecipientId: 'mockRecipientReferenceId' },
} as unknown as Recipient;

const mockCreateInput = {
  referenceId: 'mockReferenceId',
  email: '<EMAIL>',
  recipientReferenceId: mockRecipient.referenceId,
  provider: Provider.JPMC,
  account: {
    accountType: AccountType.ACH,
  },
  client: { id: 'mockClientId' },
} as unknown as CreateAccount;

const mockUpdateInput = {
  referenceId: 'existingAccount',
  email: '<EMAIL>',
  recipientReferenceId: mockRecipient.referenceId,
  provider: Provider.JPMC,
  type: AccountType.ACH,
  account: {
    accountType: AccountType.ACH,
  },
  client: { id: 'mockClientId' },
} as unknown as UpdateAccount;

const isSameAccountFn = vi.fn();
const mockSupportsPaymentFn = vi.fn();
const mockProviderCreateAccountFn = vi.fn();
const mockProviderRetrieveAccountFn = vi.fn();
const mockProviderUpdateAccountFn = vi.fn();

const accountRepository = {
  findOne: vi.fn(),
  findOneByOrFail: vi.fn(),
  findByReferenceId: vi.fn(({ referenceId }) => {
    if (['existingAccount', 'failProvider', 'updateBadRefId'].includes(referenceId)) {
      const isSuccess = referenceId === 'existingAccount';
      return {
        id: referenceId === 'updateBadRefId' ? referenceId : 'mockExistingAccountId',
        referenceId: isSuccess ? 'mockReferenceId' : referenceId,
        recipient: mockRecipient,
        keys: {
          providerAccountId: isSuccess ? 'mockAccountId' : referenceId,
        },
        type: AccountType.ACH,
        account: {
          accountType: AccountType.ACH,
        },
      };
    }
    return undefined;
  }),
  save: vi.fn(({ referenceId }) =>
    referenceId === mockCreateInput.referenceId || referenceId === mockUpdateInput.referenceId
      ? { id: 'mockAccountId' }
      : undefined,
  ),
  update: vi.fn((id) => {
    if (id === 'mockExistingAccountId') return { id: 'mockExistingAccountId' };
    throw new Error('issue updating account to database');
  }),
  softDelete: vi.fn(),
} as unknown as AccountRepository;

const mockScheduleUpdateFn = vi.fn();
const mockFindFundingSourceFn = vi.fn();
const isAccountAccessibleFn = vi.fn().mockReturnValue(true);

const mockAccountService = new AccountService(
  accountRepository,
  {
    get: vi.fn(() => ({
      createAccount: mockProviderCreateAccountFn.mockImplementation(({ email }) => {
        if (email === '<EMAIL>') return undefined;
        if (email === '<EMAIL>')
          return Promise.reject(
            new ValidationError({
              message: 'Invalid Account Number',
              errorCode: ErrorCodes.InvalidAccountNumber,
              retryable: true,
            }),
          );
        return { providerAccountId: 'mockAccountId' };
      }),
      updateAccount: mockProviderUpdateAccountFn.mockImplementation(({ email }) =>
        email === '<EMAIL>' ? undefined : { providerAccountId: 'mockAccountId' },
      ),
      retrieveRecipient: mockProviderRetrieveAccountFn.mockImplementation(() => ({
        providerRecipientId: 'mockProviderRecipientId',
        accounts: [{ providerAccountId: 'mockAccountId' }],
      })),
      retrieveAccount: vi.fn(({ providerAccountId }) =>
        providerAccountId === 'mockAccountId'
          ? {
              providerAccountId: 'mockAccountId',
            }
          : Promise.reject(new Error('Provider Error')),
      ),
      isSameAccount: isSameAccountFn,
      supportsPayment: mockSupportsPaymentFn,
      isAccountAccessible: isAccountAccessibleFn,
    })),
  } as unknown as ProviderRepositoryFactory,
  { findByReferenceId: mockFindFundingSourceFn } as unknown as FundingSourceService,
  {
    findByReferenceId: vi.fn(({ referenceId }) =>
      referenceId === mockRecipient.referenceId ? mockRecipient : undefined,
    ),
  } as unknown as RecipientService,
  { update: mockScheduleUpdateFn } as unknown as ScheduleRepository,
);

describe('AccountService', () => {
  describe('upsert', () => {
    describe('when there is missing or invalid input data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          await expect(
            mockAccountService.upsert({
              ...mockCreateInput,
              referenceId: undefined as unknown as string,
            }),
          ).rejects.toThrow(
            new ValidationError({
              message: 'account referenceId required for creation',
              provider: mockCreateInput.provider,
            }),
          );
        });
      });
      describe('when there is an existing account with the provided reference id', () => {
        it('should update the account', async () => {
          const service = new AccountService(
            {
              findByReferenceId: vi.fn().mockResolvedValueOnce({ id: 'existingAccountId' }),
            } as unknown as AccountRepository,
            {} as unknown as ProviderRepositoryFactory,
            {} as unknown as FundingSourceService,
            {} as unknown as RecipientService,
            {} as unknown as ScheduleRepository,
          );
          const mockUpdateFn = vi.spyOn(service, 'update').mockResolvedValueOnce({
            status: StatusCodes.OK,
            body: { account: { id: 'updatedAccountId' } as Account },
          });

          const createInput = {
            client: { id: 'mockClientId' },
            provider: Provider.JPMC,
            referenceId: 'mockReferenceId',
          } as CreateAccount;
          const response = await service.upsert(createInput);

          expect(response).toEqual({
            status: StatusCodes.OK,
            body: { account: { id: 'updatedAccountId' } },
          });
          expect(mockUpdateFn).toHaveBeenCalledWith(createInput);
        });
      });
      describe('when there is no recipient with the provided recipient reference id', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          await expect(
            mockAccountService.upsert({
              ...mockCreateInput,
              recipientReferenceId: undefined as unknown as string,
            }),
          ).rejects.toThrow(
            new ValidationError({
              message: 'no recipient found for referenceId undefined',
              provider: mockCreateInput.provider,
            }),
          );
        });
      });
    });
    describe('when there is no funding source that supports the provider payment', () => {
      it('throws a ValidationError with an appropriate message', async () => {
        mockSupportsPaymentFn.mockReturnValue(false);

        try {
          await mockAccountService.upsert({
            ...mockCreateInput,
            provider: 'unsupportedProvider' as unknown as Provider,
          } as unknown as CreateAccount);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe(
            'no funding source found to create account for provider unsupportedProvider',
          );
        }
      });
    });
    describe('when there is a non-retryable error in one of the provider services', () => {
      it('throws an Error with an appropriate message and send a slack notification', async () => {
        mockSupportsPaymentFn.mockReturnValue(true);

        try {
          await mockAccountService.upsert({
            ...mockCreateInput,
            email: '<EMAIL>',
          } as unknown as CreateAccount);
          expect.fail('Should have thrown');
        } catch (error) {
          expect((error as Error).message).toBe(`issue creating account with ${mockCreateInput.provider}`);
        }
        expect(sendSlackNotification).toHaveBeenCalled();
      });
    });
    describe('when there is a retryable error in one of the provider services', () => {
      it('throws an Error with an appropriate message', async () => {
        mockSupportsPaymentFn.mockReturnValue(true);

        try {
          await mockAccountService.upsert({
            ...mockCreateInput,
            email: '<EMAIL>',
          } as unknown as CreateAccount);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Invalid Account Number');
        }
        expect(sendSlackNotification).not.toHaveBeenCalled();
      });
    });
    describe('when the provider service runs successfully', () => {
      describe('when the repository fails to save the new row', () => {
        it('throws a DatabaseError with an appropriate message', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);

          await expect(
            mockAccountService.upsert({
              ...mockCreateInput,
              referenceId: 'badRefId',
            }),
          ).rejects.toThrow('issue saving account to database');
        });
      });
      describe('when the new row is saved', () => {
        it('should return a 201 and the new row', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);

          const { status } = await mockAccountService.upsert(mockCreateInput);
          expect(status).toEqual(201);
          expect(mockLogger.info).toHaveBeenCalledWith(
            'AccountService.create: created new record with id mockAccountId',
          );
        });
      });
      describe('when fundingSourceReferenceId is provided', () => {
        it('should fetch that funding source and use it to create the account', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);
          mockFindFundingSourceFn.mockResolvedValueOnce({ id: 'mockFundingSourceId' });

          const { status } = await mockAccountService.upsert({
            ...mockCreateInput,
            fundingSourceReferenceId: 'mockFundingSourceReferenceId',
          });

          expect(status).toBe(201);
          expect(mockFindFundingSourceFn).toHaveBeenCalledWith('mockFundingSourceReferenceId');
          expect(mockProviderCreateAccountFn).toHaveBeenCalledWith(
            expect.objectContaining({
              fundingSource: { id: 'mockFundingSourceId' },
            }),
          );
        });
      });
    });
  });
  describe('update', () => {
    describe('when there is missing or invalid input data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          await expect(
            mockAccountService.update({
              ...mockUpdateInput,
              referenceId: undefined as unknown as string,
            }),
          ).rejects.toThrow(
            new ValidationError({
              message: 'account referenceId required for update',
              provider: mockUpdateInput.provider,
            }),
          );
        });
      });
      describe('when there is no account with the provided reference id', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          await expect(
            mockAccountService.update({
              ...mockUpdateInput,
              referenceId: 'undefined' as unknown as string,
            }),
          ).rejects.toThrow(
            new ValidationError({
              message: 'account not found with referenceId undefined',
              provider: mockUpdateInput.provider,
            }),
          );
        });
      });
      describe('when the input account type does not match the existing type', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          mockSupportsPaymentFn.mockReturnValue(true);

          await expect(
            mockAccountService.update({
              ...mockUpdateInput,
              account: {
                accountType: AccountType.Zelle,
              },
            } as unknown as UpdateAccount),
          ).rejects.toThrow(
            new ValidationError({
              message: 'mismatched account type. Account: ach, Input: zelle',
              provider: mockUpdateInput.provider,
            }),
          );
        });
      });
    });
    describe('when there is no funding source that supports the provider', () => {
      it('throws a ValidationError with an appropriate message', async () => {
        mockSupportsPaymentFn.mockReturnValue(false);

        try {
          await mockAccountService.update({
            ...mockUpdateInput,
            provider: 'unsupportedProvider' as unknown as Provider,
          } as unknown as UpdateAccount);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('no funding source with provider unsupportedProvider');
        }
      });
    });
    describe('when there is an error in one of the provider services', () => {
      it('throws an Error with an appropriate message', async () => {
        mockSupportsPaymentFn.mockReturnValue(true);

        try {
          await mockAccountService.update({
            ...mockUpdateInput,
            email: '<EMAIL>',
          } as unknown as UpdateAccount);
          expect.fail('Should have thrown');
        } catch (error) {
          expect((error as Error).message).toBe(`issue updating account with ${mockUpdateInput.provider}`);
        }
      });
    });
    describe('when the provider service runs successfully', () => {
      describe('when the repository fails to update the record', () => {
        it('throws a DatabaseError with an appropriate message', async () => {
          isSameAccountFn.mockReturnValue(true);
          mockSupportsPaymentFn.mockReturnValue(true);

          try {
            await mockAccountService.update({
              ...mockUpdateInput,
              referenceId: 'updateBadRefId',
            });
            expect.fail('Should have thrown');
          } catch (error) {
            expect((error as Error).message).toBe('issue updating account to database');
          }
        });
      });
      describe('when the record is saved', () => {
        it('should return a 200, should soft delete the account and create a new one if provider return new keys', async () => {
          isSameAccountFn.mockReturnValue(false);
          mockSupportsPaymentFn.mockReturnValue(true);

          const { status } = await mockAccountService.update(mockUpdateInput);
          expect(accountRepository.softDelete).toHaveBeenCalled();
          expect(mockScheduleUpdateFn).toHaveBeenCalledWith(
            { accountId: 'mockExistingAccountId' },
            { accountId: 'mockAccountId' },
          );
          expect(accountRepository.save).toHaveBeenCalledWith({
            referenceId: 'existingAccount',
            recipientId: mockRecipient.id,
            type: AccountType.ACH,
            keys: { providerAccountId: 'mockAccountId' },
          });
          expect(status).toEqual(200);
          expect(mockLogger.info).toHaveBeenCalledWith(
            'AccountService.update: soft delete the record with id mockExistingAccountId',
          );
          expect(mockLogger.info).toHaveBeenCalledWith(
            'AccountService.update: create a new account with referenceId existingAccount',
          );
        });
        it('should return a 200, and update the existing account if provider return existing keys', async () => {
          isSameAccountFn.mockReturnValue(true);
          mockSupportsPaymentFn.mockReturnValue(true);

          const { status } = await mockAccountService.update(mockUpdateInput);
          expect(accountRepository.softDelete).not.toHaveBeenCalled();
          expect(accountRepository.save).not.toHaveBeenCalled();
          expect(accountRepository.update).toHaveBeenCalledWith('mockExistingAccountId', {
            keys: { providerAccountId: 'mockAccountId' },
          });
          expect(status).toEqual(200);
          expect(mockLogger.info).toHaveBeenCalledWith(
            'AccountService.update: updated record with referenceId existingAccount',
          );
        });
      });
    });
    describe('when account is not accessible', () => {
      it('should not call provider update and return the existing account', async () => {
        isAccountAccessibleFn.mockReturnValueOnce(false);

        const { status } = await mockAccountService.update(mockUpdateInput);
        expect(accountRepository.softDelete).not.toHaveBeenCalled();
        expect(accountRepository.softDelete).not.toHaveBeenCalled();
        expect(accountRepository.update).not.toHaveBeenCalled();
        expect(mockProviderUpdateAccountFn).not.toHaveBeenCalled();
        expect(status).toEqual(200);
      });
    });
  });
  describe('retrieve', () => {
    describe('when there is missing or invalid input data', () => {
      describe('when there is no referenceId in the request', () => {
        it('throws a ValidationError with an appropriate message', async () => {
          await expect(
            mockAccountService.retrieve(undefined as unknown as string, 'mockClientId'),
          ).rejects.toThrow(new ValidationError({ message: 'account referenceId required' }));
        });
      });
      describe('when there is no account found with provided reference id', () => {
        it('throws returns a 204 and empty body', async () => {
          const response = await mockAccountService.retrieve('undefined', 'mockClientId');
          expect(response).toEqual({ body: {}, status: 204 });
        });
      });
    });
    describe('when there is an error in one of the provider services', () => {
      it('throws an Error with an appropriate message', async () => {
        await expect(
          mockAccountService.retrieve('failProvider' as unknown as string, 'mockClientId'),
        ).rejects.toThrow(new Error('Provider Error'));
      });
    });
    describe('when the provider service runs successfully', () => {
      it('should return a 200 and an account', async () => {
        const { status } = await mockAccountService.retrieve('existingAccount', 'mockClientId');
        expect(status).toEqual(200);
        expect(mockLogger.info).toHaveBeenCalledWith(
          'AccountService.retrieve: receive a record with referenceId mockReferenceId',
        );
      });
    });
    describe('when account is not accessible', () => {
      it('should not call provider retrieveAccount and return the existing account', async () => {
        isAccountAccessibleFn.mockReturnValueOnce(false);

        const { status } = await mockAccountService.retrieve('existingAccount', 'mockClientId');
        expect(mockProviderRetrieveAccountFn).not.toHaveBeenCalled();
        expect(status).toEqual(200);
      });
    });
  });
});

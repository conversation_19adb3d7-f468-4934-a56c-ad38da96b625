import { mockLogger } from '@payments-test/setup/globals.js';
import {
  AccountType,
  CallbackEntities,
  CreateOrReloadPayment,
  EntityStatus,
  ErrorCodes,
  ExternalAccount,
  FundingSource,
  JPMCAccountKeys,
  JPMCCallbackBody,
  JPMCErrorCode,
  JPMCFundingSourceKeys,
  JPMCPaymentKeys,
  JPMCRecipientKeys,
  JPMCWebhookStatus,
  MappedCallback,
  Payment,
  PaymentMethod,
  PaymentStatus,
  PaymentUpdate,
  Provider,
  StandardEndpoint,
  UpdateEndpoint,
  ValidatedCreateJPMCAccount,
  ValidatedCreateJPMCPayment,
  ValidatedCreateJPMCRecipient,
  ValidatedLinkJPMCRecipient,
  ValidatedUpdateJPMCAccount,
  ValidatedUpdateJPMCRecipient,
  ZelleAccount,
} from '@payments/@types/index.js';
import { centsToDollars } from '@payments/utilities/currency.js';
import { ProviderError, ValidationError } from '@payments/utilities/errors/extensions/index.js';
import { pick } from '@payments/utilities/set.js';
import { capitalizeFirstLetter } from '@payments/utilities/strings.js';
import dayjs from 'dayjs';
import JPMCRepository from './JPMCRepository.js';

const mockSendRequest = vi.fn(({ payload, route, method }) => {
  if (
    [payload?.counterpartyEntityAliasId, payload?.programId].includes('BADNEWS') ||
    route.includes('BADNEWS')
  )
    return Promise.reject(new Error('JPMC Service Error'));
  if (route === StandardEndpoint.ExternalAccount)
    return { externalAccountAliasId: 'mockAccountAliasId' };
  if (route === StandardEndpoint.Payout)
    return { confirmationNumber: 'mockPayoutConfirmationNumber' };
  if (route === UpdateEndpoint.ExternalAccount)
    return { externalAccountAliasId: 'mockAccountAliasId' };
  if (method === 'GET' && route.includes('mockEntityAliasIdWithoutAccount')) {
    return {
      programId: 'mockProgramId',
      counterpartyEntityAliasId: 'mockEntityAliasIdWithAccount',
      counterpartyUserAliasId: '<EMAIL>',
      externalAccounts: [],
    };
  }
  if (method === 'GET' && route.includes('mockEntityAliasIdWithAccount'))
    return {
      programId: 'mockProgramId',
      counterpartyEntityAliasId: 'mockEntityAliasIdWithAccount',
      counterpartyUserAliasId: '<EMAIL>',
      counterpartyUserPreferredLanguage: null,
      counterpartyUserEmailAddress: '<EMAIL>',
      counterpartyUserFirstName: 'Mock',
      counterpartyUserLastName: 'Mock',
      counterpartyUserMobilePhoneNumber: null,
      counterpartyUserNotificationPreference: 'EMAIL',
      counterpartyUserStatus: 'Active',
      registrationStatus: 'N',
      lastLoginDateAndTime: '',
      externalAccounts: [
        {
          externalAccountAliasID: 'mockAccountAliasId',
          externalAccountStatus: 'ACTIVE',
          verified: null,
          verificationDate: null,
          lastUsedDate: null,
          bankAccount: {
            accountNumberLastFour: 'XXXX',
            category: 'Personal',
            country: 'US',
            nameOnAccount: 'Mock Mock',
            routingNumber: 'XXXXXXXXX',
            type: 'Checking',
          },
        },
      ],
    };
  if (method === 'GET' && route.includes('mockEntityAliasId'))
    return {
      programId: 'mockProgramId',
      counterpartyEntityAliasId: 'mockEntityAliasId',
      counterpartyUserAliasId: '<EMAIL>',
      counterpartyUserPreferredLanguage: null,
      counterpartyUserEmailAddress: '<EMAIL>',
      counterpartyUserFirstName: 'Mock',
      counterpartyUserLastName: 'Mock',
      counterpartyUserMobilePhoneNumber: null,
      counterpartyUserNotificationPreference: 'EMAIL',
      counterpartyUserStatus: 'Active',
      address: {
        line1: null,
        line2: null,
        city: null,
        province: null,
        country: null,
        postalCode: null,
      },
      registrationStatus: 'N',
      lastLoginDateAndTime: '',
    };
});
vi.mock('../../external/JPMC/index.ts', () => {
  return {
    default: vi.fn().mockImplementation(() => {
      return {
        sendRequest: mockSendRequest,
        lookupExternalAccount: vi.fn(
          async (payload: ExternalAccount, relations: JPMCRecipientKeys) => {
            const { programId, counterpartyUserAliasId } = payload;
            const { counterpartyEntityAliasId } = relations;
            return mockSendRequest({
              route: `programs/${programId}/counterparty-entities/${counterpartyEntityAliasId}/counterparty-users/${counterpartyUserAliasId}/`,
              method: 'GET',
            });
          },
        ),
      };
    }),
  };
});

const mockFundingSource = (valid = true) =>
  ({
    keys: { programId: valid ? 'mockProgramId' : 'BADNEWS' },
  }) as unknown as FundingSource<JPMCFundingSourceKeys>;
const mockBankInfo = {
  accountNumber: '123456',
  routingNumber: '*********',
  accountType: 'checking',
};
const mockCreateRecipient = {
  firstName: 'Test',
  lastName: 'Tester',
  email: '<EMAIL>',
  referenceId: 'mockRecipientReferenceId',
  fundingSourceReferenceId: 'mockFundingSourceReferenceId',
  provider: Provider.JPMC,
  fundingSource: { ...mockFundingSource() },
  address: {
    street: '10 Main Street',
    unit: '10B',
    city: 'Philadelphia',
    state: 'PA',
    zip: '00000',
  },
} as ValidatedCreateJPMCRecipient;
const mockUpdateRecipient = {
  counterpartyEntityAliasId: 'mockRecipientReferenceId',
  counterpartyUserAliasId: '<EMAIL>',
  firstName: 'Test',
  lastName: 'Tester',
  email: '<EMAIL>',
  provider: Provider.JPMC,
  fundingSource: { ...mockFundingSource() },
  address: {
    street: '10 Main Street',
    unit: '10B',
    city: 'Philadelphia',
    state: 'PA',
    zip: '00000',
  },
} as ValidatedUpdateJPMCRecipient;
const mockCreateAccount = (method = PaymentMethod.ACH) =>
  ({
    provider: Provider.JPMC,
    referenceId: 'mockAccountReferenceId',
    recipientReferenceId: 'mockRecipientReferenceId',
    fundingSource: { ...mockFundingSource() },
    recipient: {
      id: 'mockRecipientId',
      referenceId: mockCreateRecipient.referenceId,
      keys: {
        counterpartyEntityAliasId: mockCreateRecipient.referenceId,
        counterpartyUserAliasId: mockCreateRecipient.email,
      },
    },
    account: {
      accountType: method,
      accountInfo: method === PaymentMethod.Zelle ? { email: '<EMAIL>' } : mockBankInfo,
    },
  }) as unknown as ValidatedCreateJPMCAccount;
const mockUpdateAccount = (method = PaymentMethod.ACH) =>
  ({
    provider: Provider.JPMC,
    referenceId: 'mockAccountReferenceId',
    recipientReferenceId: 'mockRecipientReferenceId',
    fundingSource: { ...mockFundingSource() },
    recipient: {
      id: 'mockRecipientId',
      referenceId: mockCreateRecipient.referenceId,
      keys: {
        counterpartyEntityAliasId: mockCreateRecipient.referenceId,
        counterpartyUserAliasId: mockCreateRecipient.email,
      },
    },
    keys: { externalAccountAliasId: 'mockAccountAliasId' },
    account: {
      accountType: method,
      accountInfo: method === PaymentMethod.Zelle ? { email: '<EMAIL>' } : mockBankInfo,
    },
  }) as unknown as ValidatedUpdateJPMCAccount;
const mockCreatePayment = (method = PaymentMethod.ACH) =>
  ({
    referenceId: 'mockPaymentReferenceId',
    recipientReferenceId: mockCreateRecipient.referenceId,
    amount: 100,
    paymentMethod: method,
    callbackUrl: '',
    recipient: {
      id: 'mockRecipientId',
      referenceId: mockCreateRecipient.referenceId,
      keys: {
        counterpartyEntityAliasId: mockCreateRecipient.referenceId,
        counterpartyUserAliasId: mockCreateRecipient.email,
      },
    },
    account: {
      id: 'mockAccountId',
      recipientId: 'mockRecipientId',
      referenceId: 'mockAccountReferenceId',
      type: ([PaymentMethod.PhysicalCard, PaymentMethod.VirtualCard].includes(method)
        ? undefined
        : method) as unknown as AccountType,
      keys: { externalAccountAliasId: 'mockExternalAccountId' },
    },
    fundingSource: { ...mockFundingSource() },
    payment: {
      id: 'mockPaymentUuid',
      transactionNumber: 1,
    },
  }) as unknown as ValidatedCreateJPMCPayment;
const mockPayment = {
  id: 'MockPayment',
  referenceId: 'MockPaymentReferenceId',
  keys: {
    payableStatementId: 'MockPayableNumber',
    confirmationNumber: 'MockConfirmationNumber',
  },
  amount: 100,
} as unknown as Payment<JPMCPaymentKeys>;
const mockCallbackBody = (
  data: {
    invoicePayableNumbers?: null | string[];
    status?: null | JPMCWebhookStatus;
    paymentAmount?: null | string;
    confirmationNumber?: null | string;
  } = {},
) => {
  if (data.invoicePayableNumbers === undefined) data.invoicePayableNumbers = ['MockPayableNumber'];
  if (data.invoicePayableNumbers === null) data.invoicePayableNumbers = undefined;
  if (data.status === undefined) data.status = JPMCWebhookStatus.Scheduled;
  if (data.status === null) data.status = undefined;
  if (data.paymentAmount === undefined) data.paymentAmount = '1.00';
  if (data.paymentAmount === null) data.paymentAmount = undefined;
  if (data.confirmationNumber === undefined) data.confirmationNumber = 'MockConfirmationNumber';
  return { data } as JPMCCallbackBody;
};
process.env.JPMC_CERTIFICATES = JSON.stringify({});
const mockJPMCRepository = new JPMCRepository();
const mockRecipientKeys = {
  counterpartyEntityAliasId: 'mockEntityAliasId',
  counterpartyUserAliasId: '<EMAIL>',
} as JPMCRecipientKeys;
const mockAccountKeys = {
  externalAccountAliasId: 'mockAccountAliasId',
} as JPMCAccountKeys;

describe('JPMCRepository', () => {
  describe('isSameAccount', () => {
    it('should return true if the externalAccountAliasId is the same', () => {
      expect(
        mockJPMCRepository.isSameAccount(
          { externalAccountAliasId: 'acc1' },
          { externalAccountAliasId: 'acc1' },
        ),
      ).toBe(true);
    });

    it('should return false if the externalAccountAliasId is different', () => {
      expect(
        mockJPMCRepository.isSameAccount(
          { externalAccountAliasId: 'acc1' },
          { externalAccountAliasId: 'acc2' },
        ),
      ).toBe(false);
    });
  });

  describe('hasRecipientChanges', () => {
    it('should return false if two addresses are identical', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(false);
    });

    it('should return true if the unit number has changed', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(true);
    });

    it('should return true if the street address has changed', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {
            address: {
              street: '12 Elm Rd',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(true);
    });

    it('should return true if the city has changed', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Exeter',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(true);
    });

    it('should return true if the state has changed', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'MA',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(true);
    });

    it('should return true if the postal code has changed', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '12345',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(true);
    });

    it('should return true if the original had no address', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {} as JPMCRecipientKeys,
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
        ),
      ).toBe(true);
    });

    it('should return false if the new version has no address', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          {
            address: {
              street: '1 Main St',
              unit: 'A',
              city: 'Kingston',
              state: 'NH',
              zip: '98706',
            },
          } as JPMCRecipientKeys,
          {} as JPMCRecipientKeys,
        ),
      ).toBe(false);
    });

    it('should return false if neither the new nor old version has an address', () => {
      expect(
        mockJPMCRepository.hasRecipientChanges(
          { firstName: 'Joe' } as JPMCRecipientKeys,
          { firstName: 'Jim' } as JPMCRecipientKeys,
        ),
      ).toBe(false);
    });
  });

  describe('accountIsRequired', () => {
    test.each([PaymentMethod.ACH, PaymentMethod.Zelle])(
      'returns true for %p payments',
      (paymentMethod) => {
        expect(
          mockJPMCRepository.accountIsRequired({ paymentMethod } as CreateOrReloadPayment),
        ).toBe(true);
      },
    );
    it('returns false for Check payments', () => {
      expect(
        mockJPMCRepository.accountIsRequired({
          paymentMethod: PaymentMethod.Check,
        } as CreateOrReloadPayment),
      ).toBe(false);
    });
  });

  describe('shouldCreateRecurringAccount', () => {
    it('returns false', () => {
      expect(mockJPMCRepository.shouldCreateRecurringAccount()).toBe(false);
    });
  });

  describe('buildRecurringAccount', () => {
    it('throws an error', () => {
      expect(() => mockJPMCRepository.buildRecurringAccount()).toThrow(
        new ProviderError({ message: 'build recurring account is not implemented' }),
      );
    });
  });

  describe('createRecipient', () => {
    it('should throw when the service throws', async () => {
      await expect(
        mockJPMCRepository.createRecipient({ ...mockCreateRecipient, referenceId: 'BADNEWS' }),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should return counterparty entity and user', async () => {
      const result = await mockJPMCRepository.createRecipient({
        firstName: 'Test',
        lastName: 'Tester',
        email: '<EMAIL>',
        referenceId: 'mockRecipientReferenceId',
        fundingSource: { keys: { programId: 'mockProgramId' } },
        address: {
          street: '10 Main Street',
          unit: '10B',
          city: 'Philadelphia',
          state: 'PA',
          zip: '00000',
        },
      } as ValidatedCreateJPMCRecipient);

      expect(mockSendRequest).toHaveBeenCalledWith({
        route: StandardEndpoint.CounterpartyEntity,
        method: 'POST',
        payload: {
          programId: 'mockProgramId',
          counterpartyEntityName: 'Test Tester',
          counterpartyEntityAliasId: 'mockRecipientReferenceId',
          counterpartyEntityStatus: EntityStatus.Active,
          address: {
            line1: '10 Main Street',
            line2: '10B',
            city: 'Philadelphia',
            province: 'PA',
            postalCode: '00000',
            country: 'US',
          },
        },
      });
      expect(mockSendRequest).toHaveBeenCalledWith({
        route: StandardEndpoint.CounterpartyUser,
        method: 'POST',
        payload: {
          programId: 'mockProgramId',
          counterpartyEntityAliasId: 'mockRecipientReferenceId',
          counterpartyUserFirstName: 'Test',
          counterpartyUserLastName: 'Tester',
          counterpartyUserEmailAddress: '<EMAIL>',
          counterpartyUserAliasId: 'mockRecipientReferenceId',
        },
      });
      expect(result).toEqual({
        counterpartyEntityAliasId: 'mockRecipientReferenceId',
        counterpartyUserAliasId: 'mockRecipientReferenceId',
        firstName: 'Test',
        lastName: 'Tester',
        email: '<EMAIL>',
        address: {
          street: '10 Main Street',
          unit: '10B',
          city: 'Philadelphia',
          state: 'PA',
          zip: '00000',
        },
      });
    });
  });

  describe('updateRecipient', () => {
    it('should throw when the service throws', async () => {
      await expect(
        mockJPMCRepository.updateRecipient({
          ...mockUpdateRecipient,
          counterpartyEntityAliasId: 'BADNEWS',
        }),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should return counterparty entity and user', async () => {
      const result = await mockJPMCRepository.updateRecipient({
        counterpartyEntityAliasId: 'mockRecipientReferenceId',
        counterpartyUserAliasId: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Tester',
        email: '<EMAIL>',
        provider: Provider.JPMC,
        fundingSource: { keys: { programId: 'mockProgramId' } },
        address: {
          street: '10 Main Street',
          unit: '10B',
          city: 'Philadelphia',
          state: 'PA',
          zip: '00000',
        },
      } as unknown as ValidatedUpdateJPMCRecipient);
      expect(mockSendRequest).toHaveBeenCalledWith({
        route: UpdateEndpoint.CounterpartyEntity,
        method: 'POST',
        payload: {
          programId: 'mockProgramId',
          counterpartyEntityName: 'Test Tester',
          counterpartyEntityAliasId: 'mockRecipientReferenceId',
          address: {
            line1: '10 Main Street',
            line2: '10B',
            city: 'Philadelphia',
            province: 'PA',
            postalCode: '00000',
            country: 'US',
          },
        },
      });
      expect(mockSendRequest).toHaveBeenCalledWith({
        route: UpdateEndpoint.CounterpartyUser,
        method: 'POST',
        payload: {
          programId: 'mockProgramId',
          counterpartyEntityAliasId: 'mockRecipientReferenceId',
          counterpartyUserFirstName: 'Test',
          counterpartyUserLastName: 'Tester',
          counterpartyUserEmailAddress: '<EMAIL>',
          counterpartyUserAliasId: '<EMAIL>',
        },
      });
      expect(result).toEqual({
        counterpartyEntityAliasId: 'mockRecipientReferenceId',
        counterpartyUserAliasId: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Tester',
        email: '<EMAIL>',
        address: {
          street: '10 Main Street',
          unit: '10B',
          city: 'Philadelphia',
          state: 'PA',
          zip: '00000',
        },
      });
    });
  });

  describe('linkRecipient', () => {
    it('should throw when the service throws', async () => {
      await expect(
        mockJPMCRepository.linkRecipient({
          firstName: 'Test',
          lastName: 'Tester',
          email: '<EMAIL>',
          referenceId: 'mockRecipientReferenceId',
          // Causes sendRequest to throw
          fundingSource: { keys: { programId: 'BADNEWS' } },
          address: {
            street: '10 Main Street',
            unit: '10B',
            city: 'Philadelphia',
            state: 'PA',
            zip: '00000',
          },
        } as ValidatedLinkJPMCRecipient),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });

    it('should create a new counterparty entity and return all of the keys', async () => {
      const response = await mockJPMCRepository.linkRecipient({
        firstName: 'Test',
        lastName: 'Tester',
        email: '<EMAIL>',
        referenceId: 'mockRecipientReferenceId',
        counterpartyEntityAliasId: 'mockRecipientReferenceId',
        counterpartyUserAliasId: '<EMAIL>',
        fundingSource: { keys: { programId: 'mockProgramId' } },
        address: {
          street: '10 Main Street',
          unit: '10B',
          city: 'Philadelphia',
          state: 'PA',
          zip: '00000',
        },
      } as ValidatedLinkJPMCRecipient);

      expect(mockSendRequest).toHaveBeenCalledWith({
        route: StandardEndpoint.CounterpartyEntity,
        method: 'POST',
        payload: {
          programId: 'mockProgramId',
          counterpartyEntityName: 'Test Tester',
          counterpartyEntityAliasId: 'mockRecipientReferenceId',
          counterpartyEntityStatus: EntityStatus.Active,
          address: {
            line1: '10 Main Street',
            line2: '10B',
            city: 'Philadelphia',
            province: 'PA',
            postalCode: '00000',
            country: 'US',
          },
        },
      });
      expect(response).toEqual({
        counterpartyEntityAliasId: 'mockRecipientReferenceId',
        counterpartyUserAliasId: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Tester',
        email: '<EMAIL>',
        address: {
          street: '10 Main Street',
          unit: '10B',
          city: 'Philadelphia',
          state: 'PA',
          zip: '00000',
        },
      });
    });
  });

  describe('retrieveRecipient', () => {
    it('should throw when the service throws', async () => {
      await expect(
        mockJPMCRepository.retrieveRecipient(mockRecipientKeys, mockFundingSource(false).keys),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should return a recipient object', async () => {
      const result = await mockJPMCRepository.retrieveRecipient(
        mockRecipientKeys,
        mockFundingSource().keys,
      );
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        counterpartyEntityAliasId: 'mockEntityAliasId',
        counterpartyUserAliasId: '<EMAIL>',
        email: '<EMAIL>',
        firstName: 'Mock',
        lastName: 'Mock',
      });
    });
    it('should return a recipient object with accounts if recipient has any accounts', async () => {
      const result = await mockJPMCRepository.retrieveRecipient(
        { ...mockRecipientKeys, counterpartyEntityAliasId: 'mockEntityAliasIdWithAccount' },
        mockFundingSource().keys,
      );
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        counterpartyEntityAliasId: 'mockEntityAliasIdWithAccount',
        counterpartyUserAliasId: '<EMAIL>',
        email: '<EMAIL>',
        firstName: 'Mock',
        lastName: 'Mock',
        accounts: [
          {
            externalAccountAliasId: 'mockAccountAliasId',
            status: EntityStatus.Active,
            accountInfo: {
              accountNumberLastFour: 'XXXX',
              routingNumber: 'XXXXXXXXX',
              accountType: 'checking',
            },
          },
        ],
      });
    });
  });

  describe('createAccount', () => {
    it('should throw when the service throws', async () => {
      const payload = mockCreateAccount();
      await expect(
        mockJPMCRepository.createAccount({
          ...payload,
          fundingSource: mockFundingSource(false),
        } as unknown as ValidatedCreateJPMCAccount),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should return zelle account if provided', async () => {
      const payload = mockCreateAccount(PaymentMethod.Zelle);
      const result = await mockJPMCRepository.createAccount(payload);
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(mockSendRequest).toHaveBeenLastCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            externalAccountType: 'ZELLE',
            zelle: { emailAddress: (payload.account.accountInfo as ZelleAccount).email },
          }),
        }),
      );
      expect(result).toEqual({ externalAccountAliasId: 'mockAccountAliasId' });
    });
    it('should return bank account if provided', async () => {
      const payload = mockCreateAccount();
      const result = await mockJPMCRepository.createAccount(payload);
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(mockSendRequest).toHaveBeenLastCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            externalAccountType: 'BANK',
            bankAccount: expect.objectContaining({
              bankAccountNumber: mockBankInfo.accountNumber,
              routingNumber: mockBankInfo.routingNumber,
              type: capitalizeFirstLetter(mockBankInfo.accountType),
            }),
          }),
        }),
      );
      expect(result).toEqual({ externalAccountAliasId: 'mockAccountAliasId' });
    });
  });

  describe('updateAccount', () => {
    it('should throw when the service throws', async () => {
      const payload = mockUpdateAccount();
      await expect(
        mockJPMCRepository.updateAccount({
          ...payload,
          fundingSource: mockFundingSource(false),
        } as unknown as ValidatedUpdateJPMCAccount),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should return zelle account if provided', async () => {
      const payload = mockUpdateAccount(PaymentMethod.Zelle);
      const result = await mockJPMCRepository.updateAccount(payload);
      expect(mockSendRequest).toHaveBeenCalledTimes(2);
      expect(mockSendRequest).toHaveBeenLastCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            externalAccountType: 'ZELLE',
            zelle: { emailAddress: (payload.account.accountInfo as ZelleAccount).email },
          }),
        }),
      );
      expect(result).toEqual({ externalAccountAliasId: 'mockAccountAliasId' });
    });
    it('should return bank account if provided', async () => {
      const payload = mockUpdateAccount();
      const result = await mockJPMCRepository.updateAccount(payload);
      expect(mockSendRequest).toHaveBeenCalledTimes(2);
      expect(mockSendRequest).toHaveBeenLastCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            externalAccountType: 'BANK',
            bankAccount: expect.objectContaining({
              bankAccountNumber: mockBankInfo.accountNumber,
              routingNumber: mockBankInfo.routingNumber,
              type: capitalizeFirstLetter(mockBankInfo.accountType),
            }),
          }),
        }),
      );
      expect(result).toEqual({ externalAccountAliasId: 'mockAccountAliasId' });
    });
  });

  describe('retrieveAccount', () => {
    it('should throw when the service throws', async () => {
      await expect(
        mockJPMCRepository.retrieveAccount(
          mockAccountKeys,
          mockRecipientKeys,
          mockFundingSource(false).keys,
        ),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should throw validation error if accounts not found', async () => {
      try {
        await mockJPMCRepository.retrieveAccount(
          mockAccountKeys,
          { ...mockRecipientKeys, counterpartyEntityAliasId: 'mockEntityAliasIdWithOutAccount' },
          mockFundingSource().keys,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(ValidationError);
        expect((error as Error).message).toBe('no account found');
      }
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
    });
    it('should return an account object', async () => {
      const result = await mockJPMCRepository.retrieveAccount(
        mockAccountKeys,
        { ...mockRecipientKeys, counterpartyEntityAliasId: 'mockEntityAliasIdWithAccount' },
        mockFundingSource().keys,
      );
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        externalAccountAliasId: 'mockAccountAliasId',
        status: EntityStatus.Active,
        accountInfo: {
          accountNumberLastFour: 'XXXX',
          routingNumber: 'XXXXXXXXX',
          accountType: 'checking',
        },
      });
    });
  });

  describe('createPayment', () => {
    it('should throw when the service throws', async () => {
      const payload = mockCreatePayment();
      await expect(
        mockJPMCRepository.createPayment({
          ...payload,
          fundingSource: mockFundingSource(false),
        } as unknown as ValidatedCreateJPMCPayment),
      ).rejects.toThrowError(new Error('JPMC Service Error'));
    });
    it('should create a payable expiring on same day for check', async () => {
      const payload = mockCreatePayment(PaymentMethod.Check);
      const result = await mockJPMCRepository.createPayment(payload);
      expect(result).toEqual({ payableStatementId: payload.payment.transactionNumber.toString() });
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      // make sure check expires on same day
      expect(mockSendRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            checkPrint: expect.objectContaining({ grossAmount: centsToDollars(payload.amount) }),
            payableExpirationDate: expect.stringContaining(
              dayjs().tz('America/New_York').format('YYYY-MM-DD'),
            ),
          }),
        }),
      );
    });
    it('should create a payable and payout for ACH', async () => {
      const payload = mockCreatePayment(PaymentMethod.ACH);
      const result = await mockJPMCRepository.createPayment(payload);
      expect(Object.keys(result)).toEqual(['payableStatementId', 'confirmationNumber']);
      expect(mockSendRequest).toHaveBeenCalledTimes(2);
      expect(mockSendRequest).toHaveBeenLastCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            externalAccountAliasId: 'mockExternalAccountId',
            invoicePayableNumbers: expect.arrayContaining([
              payload.payment.transactionNumber.toString(),
            ]),
          }),
        }),
      );
    });
    it('should return payable and payout for Zelle', async () => {
      const payload = mockCreatePayment(PaymentMethod.Zelle);
      const result = await mockJPMCRepository.createPayment(payload);
      expect(Object.keys(result)).toEqual(['payableStatementId', 'confirmationNumber']);
      expect(mockSendRequest).toHaveBeenCalledTimes(2);
      // TODO add payload checking
      expect(mockSendRequest).toHaveBeenLastCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            externalAccountAliasId: 'mockExternalAccountId',
            invoicePayableNumbers: expect.arrayContaining([
              payload.payment.transactionNumber.toString(),
            ]),
          }),
        }),
      );
    });
    it('should log a warning if check note is over 30 characters', async () => {
      const payload = {
        ...mockCreatePayment(PaymentMethod.Check),
        note: 'really really really really really long note',
      };
      const result = await mockJPMCRepository.createPayment(payload);
      expect(result).toEqual({ payableStatementId: payload.payment.transactionNumber.toString() });
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      // make sure check expires on same day
      expect(mockSendRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            checkPrint: expect.objectContaining({
              grossAmount: centsToDollars(payload.amount),
              poNumber: 'really really really really re',
            }),
            payableExpirationDate: expect.stringContaining(
              dayjs().tz('America/New_York').format('YYYY-MM-DD'),
            ),
          }),
        }),
      );
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Received a note longer than max length: really really really really really long note',
      );
    });
    it('should strip payee name for Check', async () => {
      const payload = {
        ...mockCreatePayment(PaymentMethod.Check),
        payee: 'My’iane Ja`mes',
      };
      const result = await mockJPMCRepository.createPayment(payload);
      expect(result).toEqual({ payableStatementId: payload.payment.transactionNumber.toString() });
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      // make sure check expires on same day
      expect(mockSendRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            payableNumber: 'Myiane James 1',
          }),
        }),
      );
    });
    it('should include memo in payableReferences when includeCheckMemo is true and payment is check', async () => {
      const payload = {
        ...mockCreatePayment(PaymentMethod.Check),
        note: 'Test memo note',
        fundingSource: {
          ...mockFundingSource(),
          keys: {
            ...mockFundingSource().keys,
            includeCheckMemo: true,
          },
        },
      };
      const result = await mockJPMCRepository.createPayment(payload);
      expect(result).toEqual({ payableStatementId: payload.payment.transactionNumber.toString() });
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(mockSendRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            payableReferences: expect.arrayContaining([
              {
                payableKeyId: 'FndSrc',
                payableKeyValue: payload.fundingSource.keys.name || '',
              },
              {
                payableKeyId: 'Memo',
                payableKeyValue: 'Test memo note',
              },
            ]),
          }),
        }),
      );
    });
    it('should not include memo in payableReferences when includeCheckMemo is false and payment is check', async () => {
      const payload = {
        ...mockCreatePayment(PaymentMethod.Check),
        note: 'Test memo note',
        fundingSource: {
          ...mockFundingSource(),
          keys: {
            ...mockFundingSource().keys,
            includeCheckMemo: false,
          },
        },
      };
      const result = await mockJPMCRepository.createPayment(payload);
      expect(result).toEqual({ payableStatementId: payload.payment.transactionNumber.toString() });
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(mockSendRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            payableReferences: expect.arrayContaining([
              {
                payableKeyId: 'FndSrc',
                payableKeyValue: payload.fundingSource.keys.name || '',
              },
            ]),
          }),
        }),
      );
      // Verify memo is NOT included
      expect(mockSendRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            payableReferences: expect.not.arrayContaining([
              expect.objectContaining({
                payableKeyId: 'Memo',
              }),
            ]),
          }),
        }),
      );
    });
  });

  describe('getCallbackIdentifiers', () => {
    it('should return the endToEndId as the reference id', () => {
      expect(
        mockJPMCRepository.getCallbackIdentifiers({
          data: { endToEndId: 'mockRefId' },
        } as JPMCCallbackBody),
      ).toEqual({ referenceId: 'mockRefId' });
    });

    it('should return reference id = undefined if endToEndId is not provided', () => {
      expect(mockJPMCRepository.getCallbackIdentifiers({ data: {} } as JPMCCallbackBody)).toEqual(
        {},
      );
    });

    it('should return the invoicePayableNumbers as the transaction number', () => {
      expect(
        mockJPMCRepository.getCallbackIdentifiers({
          data: { invoicePayableNumbers: ['12345'] },
        } as JPMCCallbackBody),
      ).toEqual({ transactionNumber: 12345 });
    });

    it('should strip non-numeric characters from the transaction number', () => {
      expect(
        mockJPMCRepository.getCallbackIdentifiers({
          data: { invoicePayableNumbers: ['Payee 12345'] },
        } as JPMCCallbackBody),
      ).toEqual({ transactionNumber: 12345 });
    });

    it('should return both referenceId and transactionNumber if both are provided', () => {
      expect(
        mockJPMCRepository.getCallbackIdentifiers({
          data: { endToEndId: 'mockRefId', invoicePayableNumbers: ['12345'] },
        } as JPMCCallbackBody),
      ).toEqual({ referenceId: 'mockRefId', transactionNumber: 12345 });
    });
  });

  describe('handleCallback', () => {
    describe('when given improper input', () => {
      it('should throw a ValidationError when the payment amount in the callback does not match the payment', () => {
        expect(
          mockJPMCRepository.handleCallback(mockCallbackBody({ paymentAmount: '456' }), {
            amount: 100,
          } as unknown as PaymentUpdate),
        ).rejects.toThrowError(new ValidationError({ message: 'payment amounts do not match' }));
        expect(
          mockJPMCRepository.handleCallback(mockCallbackBody({ paymentAmount: null }), {
            amount: 100,
          } as unknown as PaymentUpdate),
        ).rejects.toThrowError(new ValidationError({ message: 'payment amounts do not match' }));
      });
      it('should throw a ValidationError on unexpected payable values', () => {
        expect(
          mockJPMCRepository.handleCallback(mockCallbackBody({ status: null }), {
            amount: 100,
          } as unknown as PaymentUpdate),
        ).rejects.toThrowError(
          new ValidationError({ message: 'invalid payable status undefined' }),
        );
      });
    });
    describe('when given proper input', () => {
      const mockPaymentUpdate = {
        ...pick(['id', 'referenceId', 'amount', 'keys'], mockPayment),
        callbackUrl: 'http://mock.callback.com',
        status: PaymentStatus.Initiated,
        entityType: CallbackEntities.Payment,
      };
      it('should return an entity update', async () => {
        expect(
          await mockJPMCRepository.handleCallback(mockCallbackBody(), mockPaymentUpdate),
        ).toEqual({
          id: mockPaymentUpdate.id,
          entityType: CallbackEntities.Payment,
          amount: 100,
          status: 'initiated',
          keys: mockPayment.keys,
          callbackUrl: mockPaymentUpdate.callbackUrl,
          referenceId: mockPaymentUpdate.referenceId,
        });
      });

      const {
        Scheduled,
        InProcess,
        Completed,
        Cancelled,
        Rejected: JPMRejected,
        Returned: JPMReturned,
      } = JPMCWebhookStatus;
      const { Initiated, Failed, Succeeded, Rejected, Returned } = PaymentStatus;
      test.each([
        [Scheduled, Initiated],
        [InProcess, Initiated],
        [Completed, Succeeded],
        [Cancelled, Failed],
        [JPMRejected, Rejected],
        [JPMReturned, Returned],
      ])(
        'should map the webhook %p status to the %p status',
        async (webhookStatus: JPMCWebhookStatus, updateStatus: PaymentStatus) => {
          expect(
            await mockJPMCRepository.handleCallback(
              mockCallbackBody({ status: webhookStatus }),
              mockPaymentUpdate,
            ),
          ).toEqual({
            id: mockPaymentUpdate.id,
            entityType: CallbackEntities.Payment,
            amount: 100,
            status: updateStatus,
            keys: mockPaymentUpdate.keys,
            callbackUrl: mockPaymentUpdate.callbackUrl,
            referenceId: mockPaymentUpdate.referenceId,
          });
        },
      );

      it('should ignore mismatched amounts when the payment has no amount saved (failed state)', async () => {
        expect(
          await mockJPMCRepository.handleCallback(mockCallbackBody(), {
            ...mockPaymentUpdate,
            amount: undefined,
          } as MappedCallback),
        ).toEqual({
          id: mockPaymentUpdate.id,
          entityType: CallbackEntities.Payment,
          amount: 100,
          status: 'initiated',
          keys: mockPayment.keys,
          callbackUrl: mockPaymentUpdate.callbackUrl,
          referenceId: mockPaymentUpdate.referenceId,
        });
      });

      it('should populate keys from the callback body if they are not yet set', async () => {
        expect(
          await mockJPMCRepository.handleCallback(
            mockCallbackBody({
              invoicePayableNumbers: ['12345678'],
              confirmationNumber: 'MockConfirmationNumber',
            }),
            { ...mockPaymentUpdate, keys: undefined } as MappedCallback,
          ),
        ).toEqual({
          id: mockPaymentUpdate.id,
          entityType: CallbackEntities.Payment,
          amount: 100,
          status: 'initiated',
          keys: { payableStatementId: '12345678', confirmationNumber: 'MockConfirmationNumber' },
          callbackUrl: mockPaymentUpdate.callbackUrl,
          referenceId: mockPaymentUpdate.referenceId,
        });
      });

      it('should populate confirmation number from the callback body if it is not yet set', async () => {
        expect(
          await mockJPMCRepository.handleCallback(
            mockCallbackBody({
              invoicePayableNumbers: ['12345678'],
              confirmationNumber: 'MockConfirmationNumber',
            }),
            { ...mockPaymentUpdate, keys: { payableStatementId: '12345678' } } as MappedCallback,
          ),
        ).toEqual({
          id: mockPaymentUpdate.id,
          entityType: CallbackEntities.Payment,
          amount: 100,
          status: 'initiated',
          keys: { payableStatementId: '12345678', confirmationNumber: 'MockConfirmationNumber' },
          callbackUrl: mockPaymentUpdate.callbackUrl,
          referenceId: mockPaymentUpdate.referenceId,
        });
      });
    });

    describe('when the payment has been rejected', () => {
      describe('when the error is a recognized retryable error', () => {
        it('returns a known retryable error detail', async () => {
          const response = await mockJPMCRepository.handleCallback(
            {
              data: {
                status: JPMCWebhookStatus.Rejected,
                paymentAmount: '100',
                rejectDetails: [
                  {
                    rejectReasonCode: JPMCErrorCode.InvalidAccountNumber,
                    rejectReasonDescription:
                      'The creditor account number is invalid. Please make the necessary corrections and try again.',
                  },
                ],
              },
            } as JPMCCallbackBody,
            { referenceId: 'mockPaymentId', amount: 10000 } as MappedCallback,
          );

          expect(response).toEqual({
            referenceId: 'mockPaymentId',
            status: 'rejected',
            amount: 10000,
            details: {
              error: {
                errorCode: ErrorCodes.InvalidAccountNumber,
                message: 'Invalid account number',
                retryable: true,
                provider: 'jpmc',
                status: 'error',
              },
            },
          });
        });
      });

      describe('when the error is not recognized', () => {
        it('logs it and returns an unknown error detail', async () => {
          const response = await mockJPMCRepository.handleCallback(
            {
              data: {
                status: JPMCWebhookStatus.Rejected,
                paymentAmount: '100',
                rejectDetails: [
                  {
                    rejectReasonCode: 'R9876',
                    rejectReasonDescription: 'Something strange and unknown happened.',
                  },
                ],
              },
            } as JPMCCallbackBody,
            { referenceId: 'mockPaymentId', amount: 10000 } as MappedCallback,
          );

          expect(response).toEqual({
            referenceId: 'mockPaymentId',
            status: 'rejected',
            amount: 10000,
            details: {
              error: {
                message: 'Something strange and unknown happened.',
                retryable: false,
                provider: 'jpmc',
                status: 'error',
              },
            },
          });
        });
      });
    });

    describe('when the payment has been returned', () => {
      describe('when the error is a recognized retryable error', () => {
        it('returns a known retryable error detail', async () => {
          const response = await mockJPMCRepository.handleCallback(
            {
              data: {
                status: JPMCWebhookStatus.Returned,
                paymentAmount: '100',
                returnDetails: {
                  returnReasonCode: JPMCErrorCode.CannotMatchAccountNumber,
                  returnReasonDescription:
                    'The financial institution recognizes the account structure but cannot match the account number and/or the name on the transaction to an account on their system.',
                },
              },
            } as JPMCCallbackBody,
            { referenceId: 'mockPaymentId', amount: 10000 } as MappedCallback,
          );

          expect(response).toEqual({
            referenceId: 'mockPaymentId',
            status: 'returned',
            amount: 10000,
            details: {
              error: {
                errorCode: ErrorCodes.UnrecognizedBankAccount,
                message: 'Unrecognized bank account',
                retryable: true,
                provider: 'jpmc',
                status: 'error',
              },
            },
          });
        });
      });

      describe('when the error is not recognized', () => {
        it('logs it and returns an unknown error detail', async () => {
          const response = await mockJPMCRepository.handleCallback(
            {
              data: {
                status: JPMCWebhookStatus.Returned,
                paymentAmount: '100',
                returnDetails: {
                  returnReasonCode: 'R9876',
                  returnReasonDescription: 'Something strange and unknown happened.',
                },
              },
            } as JPMCCallbackBody,
            { referenceId: 'mockPaymentId', amount: 10000 } as MappedCallback,
          );

          expect(response).toEqual({
            referenceId: 'mockPaymentId',
            status: 'returned',
            amount: 10000,
            details: {
              error: {
                message: 'Something strange and unknown happened.',
                retryable: false,
                provider: 'jpmc',
                status: 'error',
              },
            },
          });
        });
      });
    });
  });
});

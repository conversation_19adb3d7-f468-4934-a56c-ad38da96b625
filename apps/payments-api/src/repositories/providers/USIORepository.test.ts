import {
  Client,
  CreateOrReloadPayment,
  FundingSource,
  PaymentMethod,
  PaymentPattern,
  Provider,
  Recipient,
  UsioEnabledFundingSourceKeys,
  ValidatedCreateUSIOAccount,
  ValidatedCreateUSIOPayment,
} from '@payments/@types/index.js';
import { Endpoint } from '@payments/@types/usio.js';
import MockUSIOService from '@payments/external/USIO/service.mock.js';
import {
  AuthorizationError,
  ProviderError,
  ValidationError,
} from '@payments/utilities/errors/extensions/index.js';
import UsioRepository from './USIORepository.js';

const originalEnv = { ...process.env };

const mockSendRequest = vi.fn(({ route, routeBase, method }) => {
  if (routeBase === Endpoint.Card && method === 'GET') {
    if (route.includes('ERROR')) throw new Error('Not Found');
    return {
      Data: {
        CardHolderId: 2222222,
        CardId: 1111111,
        Username: '<EMAIL>',
      },
    };
  }
  if (routeBase === Endpoint.GiftCard && route === 'order')
    return {
      Data: {
        CardIDs: [
          {
            CardID: 1111111,
            CardholderID: 2222222,
          },
        ],
        CTAs: [],
        CoID: 333333,
      },
    };
  if (routeBase === Endpoint.Card && route === 'prefunded/load')
    return {
      Data: {},
    };
});

vi.mock('../../external/USIO/index.ts', () => {
  return {
    default: vi.fn().mockImplementation(() => {
      return {
        sendRequest: mockSendRequest,
        fetchAuthToken: vi.fn(async () => 'Bearer ACCESS_TOKEN'),
      };
    }),
  };
});

const mockFundingSource = {
  keys: {
    usioKey: 'FAKE_USIO_KEY',
    physicalDistributorId: 'MOCK_PHYSICAL_DISTRIBUTOR_ID',
    virtualDistributorId: 'MOCK_VIRTUAL_DISTRIBUTOR_ID',
  },
} as unknown as FundingSource<UsioEnabledFundingSourceKeys>;

const mockCreatePayment = (
  cardType: PaymentMethod.VirtualCard | PaymentMethod.PhysicalCard = PaymentMethod.VirtualCard,
  existingCard = true,
  withAddress = true,
  withEmail = true,
): ValidatedCreateUSIOPayment =>
  ({
    fundingSource: { ...mockFundingSource },
    paymentMethod: cardType,
    amount: 100,
    ...(!existingCard && {
      firstName: 'Mock',
      lastName: 'Test',
      payer: 'By Beam',
      ...(withEmail && { email: '<EMAIL>' }),
      ...(withAddress && {
        address: {
          street: '444 Address',
          unit: 'unit 2',
          city: 'city',
          state: 'state',
          zip: '22222',
          country: 'USA',
        },
      }),
    }),
    account: existingCard ? { keys: { cardId: 1111111 } } : undefined,
  }) as unknown as ValidatedCreateUSIOPayment;

describe('USIORepository', () => {
  const mockCreds = {
    username: 'FAKE_USER_NAME',
    password: 'FAKE_PASSWORD',
    adminKey: 'FAKE_ADMIN_KEY',
  };

  const mockUSIORepository = new UsioRepository();

  afterEach(() => {
    process.env = { ...originalEnv };
  });
  beforeEach(() => {
    process.env.USIO_DESIGN_ID = 'FAKE_DESIGN_ID';
    process.env.USIO_CREDENTIALS = JSON.stringify(mockCreds);
  });

  it('uses the MockUSIOService if environment variable is set', () => {
    process.env.MOCK_USIO_REQUESTS = 'true';

    const repo = new UsioRepository();

    // biome-ignore lint/complexity/useLiteralKeys: this is private
    expect(repo['usioService']).toEqual(expect.any(MockUSIOService));
  });

  describe('createPayment', () => {
    describe('create a prepaid card with amount if cardId is not provided', () => {
      it('should create a virtual card successfully', async () => {
        const payload = mockCreatePayment(PaymentMethod.VirtualCard, false);
        const result = await mockUSIORepository.createPayment(payload);
        expect(mockSendRequest).toHaveBeenCalledTimes(1);
        expect(mockSendRequest).toHaveBeenLastCalledWith({
          method: 'POST',
          route: 'order',
          routeBase: 'gift-card',
          payload: {
            Cards: `[{"FromName":"By Beam","RecipientFirstName":"Mock","RecipientLastName":"Test","RecipientEmail":"<EMAIL>","ShippingFirstName":"Mock","ShippingLastName":"Test","ShippingAddress":"444 Address","ShippingAddress2":"unit 2","ShippingCity":"city","ShippingState":"state","ShippingZip":"22222","Amount":0.01,"CardType":"virtual","DistributorId":"${mockFundingSource.keys.virtualDistributorId}"}]`,
            PaymentType: 'distributor',
            _key: mockFundingSource.keys.usioKey,
          },
        });
        expect(result).toEqual({
          cardId: 1111111,
        });
      });
      it('should create a physical card successfully', async () => {
        const payload = mockCreatePayment(PaymentMethod.PhysicalCard, false);
        const result = await mockUSIORepository.createPayment(payload);
        expect(mockSendRequest).toHaveBeenCalledTimes(1);
        expect(mockSendRequest).toHaveBeenLastCalledWith({
          method: 'POST',
          route: 'order',
          routeBase: 'gift-card',
          payload: {
            Cards: `[{"FromName":"By Beam","RecipientFirstName":"Mock","RecipientLastName":"Test","ShippingFirstName":"Mock","ShippingLastName":"Test","ShippingAddress":"444 Address","ShippingAddress2":"unit 2","ShippingCity":"city","ShippingState":"state","ShippingZip":"22222","Amount":0.01,"CardType":"plastic","DistributorId":"${mockFundingSource.keys.physicalDistributorId}"}]`,
            DesignId: 'FAKE_DESIGN_ID',
            PaymentType: 'distributor',
            _key: mockFundingSource.keys.usioKey,
          },
        });
        expect(result).toEqual({
          cardId: 1111111,
        });
      });
      it('should throw a validation error if address is not provided', async () => {
        const payload = mockCreatePayment(PaymentMethod.VirtualCard, false, false, true);
        try {
          await mockUSIORepository.createPayment(payload);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Address is required');
        }
        expect(mockSendRequest).not.toBeCalled();
      });
      it('should throw a validation error if email is not provided', async () => {
        const payload = mockCreatePayment(PaymentMethod.VirtualCard, false, true, false);
        try {
          await mockUSIORepository.createPayment(payload);
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(ValidationError);
          expect((error as Error).message).toBe('Email is required');
        }
        expect(mockSendRequest).not.toBeCalled();
      });
      it('should throw an authorization error if usio key is not provided', async () => {
        const payload = mockCreatePayment(PaymentMethod.VirtualCard, false);
        payload.fundingSource = { keys: {} } as FundingSource<UsioEnabledFundingSourceKeys>;
        await expect(mockUSIORepository.createPayment(payload)).rejects.toThrowError(
          new AuthorizationError({ message: 'Missing USIO key on funding source' }),
        );
        expect(mockSendRequest).not.toBeCalled();
      });
      it('should throw an authorization error if distributor Id is not provided', async () => {
        const payload = mockCreatePayment(PaymentMethod.VirtualCard, false);
        payload.fundingSource = {
          keys: { usioKey: 'MOCK_USIO_KEY' },
        } as FundingSource<UsioEnabledFundingSourceKeys>;
        await expect(mockUSIORepository.createPayment(payload)).rejects.toThrowError(
          new AuthorizationError({ message: 'Missing distributorId key on funding source' }),
        );
        expect(mockSendRequest).not.toBeCalled();
      });
    });
    describe('Load an existing card', () => {
      it('should load pre existing card if account is provided', async () => {
        const payload = mockCreatePayment(PaymentMethod.VirtualCard);
        const result = await mockUSIORepository.createPayment(payload);
        expect(mockSendRequest).toHaveBeenCalledTimes(1);
        expect(mockSendRequest).toHaveBeenCalledWith({
          method: 'POST',
          payload: { Amount: 0.01, CardId: 1111111 },
          route: 'prefunded/load',
          routeBase: 'card',
          withAuthenticate: true,
          withAdminUsername: true,
        });
        expect(result).toEqual({
          cardId: 1111111,
        });
      });
    });
  });

  describe('accountIsRequired', () => {
    it('should return false for a one-time payment', async () => {
      expect(
        mockUSIORepository.accountIsRequired({
          scheduleId: undefined,
          paymentMethod: PaymentMethod.PhysicalCard,
        } as CreateOrReloadPayment),
      ).toBe(false);
    });
    it('should return true for a recurring payment', async () => {
      expect(
        mockUSIORepository.accountIsRequired({
          scheduleId: 'mockScheduleId',
          paymentMethod: PaymentMethod.PhysicalCard,
        } as CreateOrReloadPayment),
      ).toBe(true);
    });
  });

  describe('createAccount', () => {
    it('should create a new card', async () => {
      const payload = {
        ...mockCreatePayment(PaymentMethod.VirtualCard, false),
        account: { accountType: PaymentMethod.VirtualCard },
        recipient: {} as Recipient,
        provider: Provider.USIO as const,
      } as ValidatedCreateUSIOAccount;
      const result = await mockUSIORepository.createAccount(payload);
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(mockSendRequest).toHaveBeenLastCalledWith({
        method: 'POST',
        route: 'order',
        routeBase: 'gift-card',
        payload: {
          Cards: `[{"FromName":"By Beam","RecipientFirstName":"Mock","RecipientLastName":"Test","RecipientEmail":"<EMAIL>","ShippingFirstName":"Mock","ShippingLastName":"Test","ShippingAddress":"444 Address","ShippingAddress2":"unit 2","ShippingCity":"city","ShippingState":"state","ShippingZip":"22222","Amount":0.01,"CardType":"virtual","DistributorId":"${mockFundingSource.keys.virtualDistributorId}"}]`,
          PaymentType: 'distributor',
          _key: mockFundingSource.keys.usioKey,
        },
      });
      expect(result).toEqual({
        cardId: 1111111,
      });
    });

    it('should create a physical card with a customized designId', async () => {
      const payload = {
        ...mockCreatePayment(PaymentMethod.PhysicalCard, false),
        account: { accountType: PaymentMethod.PhysicalCard },
        recipient: {} as Recipient,
        provider: Provider.USIO as const,
      } as ValidatedCreateUSIOAccount;
      payload.fundingSource.keys.usioDesignId = 'CUSTOM_DESIGN_ID';
      const result = await mockUSIORepository.createAccount(payload);
      expect(mockSendRequest).toHaveBeenCalledTimes(1);
      expect(mockSendRequest).toHaveBeenLastCalledWith({
        method: 'POST',
        route: 'order',
        routeBase: 'gift-card',
        payload: {
          Cards: `[{"FromName":"By Beam","RecipientFirstName":"Mock","RecipientLastName":"Test","ShippingFirstName":"Mock","ShippingLastName":"Test","ShippingAddress":"444 Address","ShippingAddress2":"unit 2","ShippingCity":"city","ShippingState":"state","ShippingZip":"22222","Amount":0.01,"CardType":"plastic","DistributorId":"${mockFundingSource.keys.physicalDistributorId}"}]`,
          DesignId: 'CUSTOM_DESIGN_ID',
          PaymentType: 'distributor',
          _key: mockFundingSource.keys.usioKey,
        },
      });
      expect(result).toEqual({
        cardId: 1111111,
      });
    });
  });

  describe('shouldCreateRecurringAccount', () => {
    it('should return true', () => {
      expect(mockUSIORepository.shouldCreateRecurringAccount()).toBe(true);
    });
  });

  describe('buildRecurringAccount', () => {
    it('should return the appropriate fields from the scheduled payment', () => {
      expect(
        mockUSIORepository.buildRecurringAccount({
          referenceId: 'mockReferenceId',
          recipientReferenceId: 'mockRecipientReferenceId',
          address: {
            city: 'City',
            state: 'State',
            street: 'Street',
            zip: '12345',
          },
          email: '<EMAIL>',
          firstName: 'firstName',
          lastName: 'lastName',
          payer: 'Recurring Test Partner',
          paymentMethod: PaymentMethod.VirtualCard,
          provider: Provider.USIO,
          fundingSourceReferenceId: 'mockFundingSourceReferenceId',
          accountReferenceId: 'mockAccountReferenceId',
          client: { id: 'mockClientId' } as unknown as Client,
          dryRun: false,
          amountPerTransaction: 20000,
          numberOfPayments: 12,
          start: new Date(),
          pattern: PaymentPattern.BiWeekly,
          callbackUrl: 'http://test.callback.org',
          amount: 100,
        }),
      ).toEqual({
        referenceId: 'mockAccountReferenceId',
        recipientReferenceId: 'mockRecipientReferenceId',
        fundingSourceReferenceId: 'mockFundingSourceReferenceId',
        address: {
          city: 'City',
          state: 'State',
          street: 'Street',
          zip: '12345',
        },
        email: '<EMAIL>',
        firstName: 'firstName',
        lastName: 'lastName',
        payer: 'Recurring Test Partner',
        account: { accountType: PaymentMethod.VirtualCard },
        provider: Provider.USIO,
        client: { id: 'mockClientId' } as unknown as Client,
      });
    });
  });

  it('Should return empty object for recipient crud operations', async () => {
    await expect(mockUSIORepository.createRecipient()).resolves.toStrictEqual({});
    await expect(mockUSIORepository.updateRecipient()).resolves.toStrictEqual({});
    await expect(mockUSIORepository.retrieveRecipient()).resolves.toStrictEqual({});
    expect(mockUSIORepository.hasRecipientChanges()).toBeFalsy();
  });
  it('Should throw an error for linkRecipient', async () => {
    await expect(mockUSIORepository.linkRecipient()).rejects.toThrowError(
      new ProviderError({ message: 'Link recipient is not implemented' }),
    );
  });
  it('Should return empty object for account read/update operations', async () => {
    await expect(mockUSIORepository.retrieveAccount()).resolves.toStrictEqual({});
    await expect(mockUSIORepository.updateAccount()).resolves.toStrictEqual({});
    expect(mockUSIORepository.isSameAccount()).toBeFalsy();
  });
  it('Should return empty object for payment update', async () => {
    await expect(mockUSIORepository.updatePayment()).resolves.toStrictEqual({});
  });
  it('Should return empty object for handle callback', async () => {
    await expect(mockUSIORepository.handleCallback()).resolves.toStrictEqual({});
  });
  it('Should return false for supportsPayment when the funding source provider does not match', async () => {
    expect(
      mockUSIORepository.supportsPayment({ provider: Provider.USIO } as FundingSource),
    ).toBeFalsy();
  });
});

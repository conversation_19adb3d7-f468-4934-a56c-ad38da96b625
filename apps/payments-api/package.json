{"name": "@bybeam/payments-api", "version": "0.0.1", "private": true, "engines": {"node": "20.10.0", "npm": ">=9.8.1"}, "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc --build --force tsconfig.src.json", "clean": "rm -rf dist", "start": "node ./dist/index.js", "start:dev": "tsc-watch --build tsconfig.src.json --onSuccess 'node --env-file=.env ./dist/index.js'", "test": "vitest", "debug": "tsc-watch --build tsconfig.src.json --onSuccess 'node --inspect ./dist/index.js'"}, "dependencies": {"@bybeam/common-proto": "workspace:^", "@bybeam/infrastructure-lib": "workspace:*", "@bybeam/scheduler-client": "workspace:*", "axios": "1.12.2", "bottleneck": "^2.19.5", "dayjs": "1.11.11", "form-data": "4.0.4", "http-status-codes": "2.3.0", "https": "1.0.0", "jsonwebtoken": "9.0.2", "jws": "4.0.0", "koa": "3.0.3", "koa-body": "6.0.1", "koa-combine-routers": "4.0.2", "koa-router": "13.0.1", "lodash.isequal": "4.5.0", "mailgun.js": "12.1.1", "pg": "8.13.1", "qs": "6.11.2", "reflect-metadata": "0.2.1", "stack-trace": "0.0.10", "typeorm": "0.3.17", "typeorm-encrypted": "0.8.0", "uuid": "9.0.1"}, "devDependencies": {"@tsconfig/node20": "20.1.4", "@types/jsonwebtoken": "9.0.10", "@types/jws": "3.2.11", "@types/koa": "2.15.0", "@types/koa-router": "7.4.8", "@types/lodash.isequal": "4.5.8", "@types/node": "catalog:default", "@types/qs": "^6.9.11", "@types/stack-trace": "0.0.33", "@types/uuid": "catalog:default", "@vitest/coverage-v8": "catalog:default", "mockdate": "catalog:default", "ts-node": "catalog:default", "tsc-watch": "7.2.0", "typescript": "catalog:default", "vite-tsconfig-paths": "5.0.1", "vitest": "catalog:default"}}
from dataclasses import dataclass
import logging
import json
from uuid import UUID

import duckdb
from google.protobuf import json_format

from entity_resolution.generated.ers_pb2 import (
    DataSource,
    Database,
    Detail,
    DuplicateAppVersionMatch,
    DuplicateAppVersionResponse,
    Feature,
    Field,
    FieldType,
    Match,
    Model,
    QueryResponse,
    Entity,
    Similarity,
    Status,
    DuplicateAppVersionMatchField,
)
from entity_resolution.src.datasources.database import DatabaseDatasource
from entity_resolution.src.models.model_evaluator import ModelEvaluator
from entity_resolution.src.services.query_service import QueryService
from entity_resolution.src.db.engine import async_core_engine
from entity_resolution.src.utilities.utilities import must_getenv
from entity_resolution.src.__generated__.repo.models import DuplicateApplicationVersion
from entity_resolution.src.__generated__.repo.duplicate_application_versions import AsyncQuerier
from entity_resolution.src.__generated__.repo.service_cache import AsyncQuerier as CacheQuerier


DB_NAME = must_getenv("DB_NAME")

logger = logging.getLogger(__name__)

FETCH_CANDIDATES_BY_PROGRAM_ID= f"""
-- 1. Find the latest versions, cases, and submitters for the program.
WITH latest_app_versions AS (
    SELECT 
        av.application_id, 
        av.version_id,
        a.case_id,
        a.submitter_id,
        cases.program_id -- Pass this down
    FROM {DB_NAME}.public.latest_application_version av
    INNER JOIN {DB_NAME}.public.applications a ON av.application_id = a.id
    INNER JOIN {DB_NAME}.public.cases cases ON a.case_id = cases.id
    WHERE cases.program_id = $p1
      AND cases.deactivated_at IS NULL
),

-- 2. Fetch *only* the answers we need from Postgres.
filtered_answers AS (
    SELECT version_id, key, value
    FROM {DB_NAME}.public.application_answers
    WHERE version_id IN (SELECT version_id FROM latest_app_versions)
      AND key IN ('dob', 'dateOfBirth', 'studentId', 'address.mailingAddressId')
),

-- 3. Pivot the small, filtered set of answers.
app_answers_pivoted AS (
    SELECT
        lav.application_id,
        lav.version_id,
        lav.case_id,
        lav.submitter_id,
        lav.program_id,
        MAX(CASE WHEN fa.key IN ('dob', 'dateOfBirth') THEN fa.value END) AS dob_str,
        MAX(CASE WHEN fa.key = 'studentId' THEN fa.value END) AS student_id_str,
        MAX(CASE WHEN fa.key = 'address.mailingAddressId' THEN fa.value END) AS mailing_address_id_str
    FROM filtered_answers AS fa
    INNER JOIN latest_app_versions AS lav
        ON fa.version_id = lav.version_id
    GROUP BY 1, 2, 3, 4, 5
),

-- 4. Parse the pivoted answers.
app_answers_parsed AS (
    SELECT
        *,
        -- Corrected and more robust CASE statement for multiple date formats
        CASE
            WHEN regexp_matches(dob_str, '^\d{4}-\d{2}-\d{2}$')
            THEN strptime(dob_str, '%Y-%m-%d')::DATE
            WHEN regexp_matches(dob_str, '^\d{1,2}/\d{1,2}/\d{4}$')
            THEN strptime(dob_str, '%m/%d/%Y')::DATE
            WHEN regexp_matches(dob_str, '^\d{1,2}/\d{1,2}/\d{2}$')
            THEN strptime(dob_str, '%m/%d/%y')::DATE
            ELSE NULL
        END AS dob_date,
        
        -- Safely cast to UUID
        CASE 
            WHEN mailing_address_id_str IS NOT NULL
                 AND TRIM(mailing_address_id_str) != ''
                 AND regexp_matches(mailing_address_id_str, 
                    '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$')
            THEN mailing_address_id_str::UUID
            ELSE NULL
        END AS mailing_address_id
    FROM app_answers_pivoted
),

-- 5. Join the remaining tables.
base_data AS (
    SELECT 
        aap.version_id,
        u.name AS primary_contact_name,
        u.email,
        u.phone,
        aap.dob_str,
        aap.dob_date,
        aap.student_id_str, -- Use the raw string for now
        addr.address_line_1,
        addr.address_line_2,
        addr.city,
        addr.state,
        aap.program_id
    FROM app_answers_parsed AS aap
    INNER JOIN {DB_NAME}.public.users AS u
        ON aap.submitter_id = u.id
    LEFT JOIN {DB_NAME}.public.addresses AS addr
        ON aap.mailing_address_id = addr.id
    WHERE
        u.deactivated_at IS NULL
        -- All other filters were applied in 'latest_app_versions'
)
-- Final SELECT statement with defensive casting
SELECT
    version_id AS id,
    split_part(COALESCE(primary_contact_name, ''), ' ', 1) AS first_name,
    CASE
        WHEN strpos(COALESCE(primary_contact_name, ''), ' ') > 0
        THEN substr(primary_contact_name, strpos(primary_contact_name, ' ') + 1)
        ELSE NULL
    END AS second_name,
    dob_str AS primary_contact_dob,
    -- Safely extract year and month, returning NULL if dob_date is NULL
    CASE 
        WHEN dob_date IS NOT NULL 
        THEN CAST(EXTRACT(YEAR FROM dob_date) AS TEXT) 
        ELSE NULL 
    END AS dob_year,
    CASE 
        WHEN dob_date IS NOT NULL 
        THEN CAST(EXTRACT(MONTH FROM dob_date) AS TEXT) 
        ELSE NULL 
    END AS dob_month,
    -- Clean the student_id here, at the very end
    CASE 
        WHEN student_id_str IS NOT NULL 
             AND TRIM(student_id_str) != '' 
             AND LENGTH(TRIM(student_id_str)) >= 4 
        THEN TRIM(student_id_str) 
        ELSE NULL 
    END AS student_id,
    -- Safely handle email splitting
    CASE 
        WHEN email IS NOT NULL AND strpos(email, '@') > 0
        THEN split_part(email, '@', 1)
        ELSE NULL
    END AS primary_contact_email,
    -- Safely extract phone area code (last 10 digits, first 3)
    CASE
        WHEN phone_digits IS NOT NULL 
             AND LENGTH(phone_digits) >= 10
        THEN CAST(substr(phone_digits, -10, 3) AS TEXT)
        ELSE NULL
    END AS phone_area_code,
    -- Safely extract phone last 7 digits
    CASE
        WHEN phone_digits IS NOT NULL 
             AND LENGTH(phone_digits) >= 7
        THEN CAST(substr(phone_digits, -7) AS TEXT)
        ELSE NULL
    END AS phone_trim,
    lower(regexp_replace(COALESCE(address_line_1, '') || COALESCE(address_line_2, ''), '\s', '', 'g')) AS street_address,
    -- Return NULL if no numbers found in address
    NULLIF(regexp_replace(COALESCE(address_line_1, '') || COALESCE(address_line_2, ''), '[^0-9]', '', 'g'), '') AS street_address_numbers,
    lower(regexp_replace(COALESCE(city, '') || COALESCE(state, ''), '\s', '', 'g')) AS city_state
FROM (
    SELECT
        base_data.*,
        regexp_replace(phone, '[^0-9]', '', 'g') AS phone_digits
    FROM base_data
    WHERE version_id != $query_application_version_id
    ) t
"""

@dataclass
class _AccountMatch:
    application_version_id: UUID
    candidate_application_version_id: UUID
    raw_details: list[Detail]
    str_details: str

    def to_DuplicateAppVersionMatch(
        self, query_application_version_id: UUID
    ) -> DuplicateAppVersionMatch:
        if query_application_version_id == self.application_version_id:
            match_fields = [
                DuplicateAppVersionMatchField(
                    field_name=x.field_name,
                    query_value=x.provided,
                    candidate_value=x.matched,
                    similarity=_calculate_similarity(x.confidence)
                )
                for x in self.raw_details
            ]

            ret = DuplicateAppVersionMatch(
                application_version_id=str(self.application_version_id),
                candidate_application_version_id=str(self.candidate_application_version_id),
                fields=match_fields,
            )

        else:
            match_fields = [
                DuplicateAppVersionMatchField(
                    field_name= x.field_name,
                    query_value= x.matched,
                    candidate_value= x.provided,
                    similarity= _calculate_similarity(x.confidence)
                )
                for x in self.raw_details
            ]

            ret = DuplicateAppVersionMatch(
                application_version_id=str(self.candidate_application_version_id),
                candidate_application_version_id=str(self.application_version_id),
                fields=match_fields,
            )

        return ret

async def upsert_request_status(app_version_id: UUID, status: str) -> None:
    async with async_core_engine.begin() as conn:
        await CacheQuerier(conn).upsert_request_status(entity_id=app_version_id, status=status)


async def fetch_existing_data(app_version_id: UUID):
    # fetch matches from the database and return those
    async with async_core_engine.connect() as conn:
        raw_matches = AsyncQuerier(conn).fetch_duplicate_application_versions(
            application_version_id=app_version_id
        )
        raw_matches = [x async for x in raw_matches]

    # convert to account_matches
    account_matches = [_match_from_dav(x) for x in raw_matches]

    return DuplicateAppVersionResponse(
        status=Status.SUCCESS,
        matches=[x.to_DuplicateAppVersionMatch(app_version_id) for x in account_matches],
    )

async def _fetch_query_entity(app_version_id: UUID) -> list[Entity]:
    async with async_core_engine.connect() as conn:
        query_values = await AsyncQuerier(conn).get_query_application_version(id=app_version_id)

    if not query_values:
        logger.error("Unable to find data for provided application version id.")
        return []

    # build features and entities
    query_entity = Entity(
        entity_id=str(app_version_id),
        fields=[
            Field(
                field_name="first_name",
                value=query_values.first_name,
                field_type=FieldType.GIVEN_NAME,
            ),
            Field(
                field_name="second_name",
                value=query_values.second_name,
                field_type=FieldType.FAMILY_NAME,
            ),
            Field(field_name="dob_year", value=query_values.dob_year, field_type=FieldType.YEAR),
            Field(field_name="dob_month", value=query_values.dob_month, field_type=FieldType.MONTH),
            Field(
                field_name="primary_contact_dob",
                value=query_values.primary_contact_dob,
                field_type=FieldType.DATE_OF_BIRTH,
            ),
            Field(field_name="student_id", value=query_values.student_id, field_type=FieldType.ID),
            Field(
                field_name="primary_contact_email",
                value=query_values.primary_contact_email,
                field_type=FieldType.EMAIL,
            ),
            Field(
                field_name="street_address",
                value=query_values.street_address,
                field_type=FieldType.STREET_ADDRESS,
            ),
            Field(
                field_name="street_address_numbers",
                value=query_values.street_address_numbers,
                field_type=FieldType.STREET_NUMBER,
            ),
            Field(
                field_name="city_state",
                value=query_values.city_state,
                field_type=FieldType.FIELD_TYPE_UNKNOWN,
            ),
            Field(
                field_name="phone_area_code",
                value=query_values.phone_area_code,
                field_type=FieldType.AREA_CODE,
            ),
            Field(
                field_name="phone_trim", value=query_values.phone_trim, field_type=FieldType.PHONE
            ),
        ],
    )

    return [query_entity]


def _resolve_entities(
    app_version_id: UUID,
    program_id: str,
    requested_features: list[Feature],
    requested_entities: list[Entity]
) -> QueryResponse:
    with duckdb.connect() as conn:
        # set up connection
        database = DataSource(
            database=Database(
                name=DB_NAME,
                query_template=FETCH_CANDIDATES_BY_PROGRAM_ID,
                query_params={
                    "p1": program_id,
                    "query_application_version_id": str(app_version_id)},
            )
        )
        datasource = DatabaseDatasource(conn, database)

        # build model evaluator
        model_evaluator = ModelEvaluator(conn, Model.DUPLICATE_ACCOUNT, requested_features)

        # build service
        query_service = QueryService(
            conn,
            datasource,
            model_evaluator,
            requested_entities,
            requested_features,
        )

        # Execute query and format response
        raw_response = query_service.build_and_execute_query()
        logger.debug(f"\n{raw_response}")

        formatted_response = query_service.format_response(result=raw_response)
        logger.debug(f"\n{formatted_response}")

    return formatted_response


async def _persist_results(
    conn, query_response: QueryResponse, application_version_id: UUID
) -> list[_AccountMatch]:
    matches = list(query_response.matches)
    account_matches = [
        _initialize_account_match(application_version_id, match) for match in matches
    ]

    for am in account_matches:
        await AsyncQuerier(conn).upsert_duplicate_application_version(
            application_version_id=am.application_version_id,
            candidate_application_version_id=am.candidate_application_version_id,
            details=am.str_details,
        )
    await conn.commit()
    return account_matches


async def get_and_persist_results(
    app_version_id: UUID, requested_features: list[Feature], program_id: str
) -> DuplicateAppVersionResponse:
    async with async_core_engine.begin() as conn:
        # fetch application_version data
        requested_entities = await _fetch_query_entity(app_version_id)
        logger.debug(f"requested_entities: {requested_entities}")
        if not requested_entities:
            logger.error("No entities found.")
            raise ValueError("Entity not found for requested app version id.")

        # resolve entities
        response = _resolve_entities(
            app_version_id,
            program_id,
            requested_features,
            requested_entities
        )
        logger.debug(f"raw resolved entities: {response}")

        # persist results to the database
        results = await _persist_results(
            conn, query_response=response, application_version_id=app_version_id
        )

        # format for final response
        formatted_response = DuplicateAppVersionResponse(
            status=Status.SUCCESS,
            matches=[x.to_DuplicateAppVersionMatch(app_version_id) for x in results],
        )

    return formatted_response


def _initialize_account_match(application_version_id: UUID, match: Match) -> _AccountMatch:
    candidate_id = UUID(match.candidate_id)

    if application_version_id > candidate_id:
        logger.debug("SWAPPING QUERY AND CANDIDATE ID")
        # if the above holds, we need to swap around for insertion.
        query_id = candidate_id
        candidate_id = application_version_id

        # swap around provided and matched.
        details = [
            Detail(
                field_name=x.field_name,
                matched=x.provided,
                provided=x.matched,
                confidence=x.confidence,
                description=x.description,
                metric=x.metric,
            )
            for x in match.details
        ]

    else:
        query_id = application_version_id
        details = match.details

    # convert message to json serializable string (and back)
    details_final = [json.loads(json_format.MessageToJson(x)) for x in details]

    return _AccountMatch(
        application_version_id=query_id,
        candidate_application_version_id=candidate_id,
        str_details=json.dumps(details_final),
        raw_details=list(details),
    )


def _match_from_dav(dav_row: DuplicateApplicationVersion) -> _AccountMatch:
    """Converts a duplicate_application_versions database row to account match"""
    assert isinstance(dav_row.details, list)

    raw_details = [
        Detail(
            field_name=x.get("field_name"),
            provided=x.get("provided"),
            matched=x.get("matched"),
            confidence=x.get("confidence"),
            description=x.get("description"),
            metric=x.get("metric"),
        )
        for x in dav_row.details
    ]

    return _AccountMatch(
        application_version_id=dav_row.application_version_id,
        candidate_application_version_id=dav_row.candidate_application_version_id,
        raw_details=raw_details,
        str_details=json.dumps(dav_row.details),
    )


def _calculate_similarity(confidence: float) -> Similarity:
    if confidence == 1.0:
        return Similarity.EXACT
    if confidence >= 0.90:
        return Similarity.SIMILAR
    if confidence < 0:
        return Similarity.NOT_CALCULATED
    if confidence < 0.90:
        return Similarity.NOT_SIMILAR
    else:
        return Similarity.UNKNOWN

import { Test } from '@nestjs/testing';
import { describe, expect, it, vi } from 'vitest';
import { PubsubProvider } from '../messaging/pubsub.provider.js';
import { MessagingHealthIndicator } from './messaging.health.js';

describe('MessagingHealth', () => {
  describe('checkTopics', () => {
    it('should throw if any of the topic checks fail', async () => {
      const healthCheckFn = vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(false);

      const app = await Test.createTestingModule({
        providers: [MessagingHealthIndicator],
      })
        .useMocker((token) => {
          if (token === PubsubProvider) return { healthCheck: healthCheckFn };
        })
        .compile();
      const health = app.get(MessagingHealthIndicator);

      await expect(async () => health.checkTopics(['mockTopicA', 'mockTopicB'])).rejects.toThrow(
        'messaging health check failed',
      );
      expect(healthCheckFn).toHaveBeenCalledTimes(2);
      expect(healthCheckFn).toHaveBeenCalledWith('mockTopicA');
      expect(healthCheckFn).toHaveBeenCalledWith('mockTopicB');
    });
    it('should return a positive status with details about the tested topics if the checks all succeed', async () => {
      const healthCheckFn = vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true);

      const app = await Test.createTestingModule({
        providers: [MessagingHealthIndicator],
      })
        .useMocker((token) => {
          if (token === PubsubProvider) return { healthCheck: healthCheckFn };
        })
        .compile();
      const health = app.get(MessagingHealthIndicator);

      const result = await health.checkTopics(['mockTopicA', 'mockTopicB']);
      expect(result).toEqual({
        messaging: { status: 'up', topics: { mockTopicA: true, mockTopicB: true } },
      });
    });
  });
});

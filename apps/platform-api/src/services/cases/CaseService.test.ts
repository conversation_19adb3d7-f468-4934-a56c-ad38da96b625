import {
  CaseSortColumn,
  CaseStatus,
  CreateCaseTagInput,
  DeleteCaseTagInput,
  SortDirection,
  UploadEntityDocumentsInput,
  WorkflowAction,
} from '@bybeam/platform-types';
import { mockAdminToken, mockLoginToken } from '@platform-api-test/mocks.js';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import { CaseMetadataRepository, CaseRepository } from '@platform-api/@types/repositories/index.js';
import {
  CaseParticipantService,
  CaseTagService,
  DocumentService,
} from '@platform-api/@types/services.js';
import LinkingRepository from '@platform-api/repositories/external/LinkingRepository.js';
import { In, Not } from 'typeorm';
import { Mock } from 'vitest';
import ProgramService from '../programs/ProgramService.js';
import WorkflowEventService from '../workflowEvent/WorkflowEventService.js';
import CaseService from './CaseService.js';

describe('CaseService', () => {
  describe('findAndCount', () => {
    let service: CaseService;
    let mockFindAndCountIds: Mock;
    let mockFindAndCountIdsV2: Mock;
    let mockFindByIds: Mock;
    let mockFindByPartnerId: Mock;
    let mockFindParticipant: Mock;
    beforeEach(() => {
      mockFindAndCountIds = vi.fn().mockResolvedValueOnce({ ids: ['case1', 'case2'], count: 10 });
      mockFindAndCountIdsV2 = vi.fn().mockResolvedValueOnce({ ids: ['case1', 'case2'], count: 10 });
      mockFindByIds = vi.fn().mockResolvedValueOnce([]);
      mockFindByPartnerId = vi.fn();
      mockFindParticipant = vi.fn();
      service = new CaseService(
        {
          findAndCountIds: mockFindAndCountIds,
          findAndCountIdsV2: mockFindAndCountIdsV2,
          findBy: mockFindByIds,
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        { findWithOptions: mockFindParticipant } as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        {} as unknown as DocumentService,
        { findByPartnerId: mockFindByPartnerId } as unknown as ProgramService,
        {} as unknown as LinkingRepository,
        {} as unknown as WorkflowEventService,
      );
    });

    describe('When the user provides no filter', () => {
      it('filters by the partner and explicitly by the programs associated with the partner', async () => {
        mockFindByPartnerId.mockResolvedValueOnce([
          { id: 'mockProgramId1' },
          { id: 'mockProgramId2' },
        ]);
        await service.findAndCount(mockAdminToken);

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            partnerId: mockAdminToken.partnerId,
            partnerProgramIds: ['mockProgramId1', 'mockProgramId2'],
          },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2']) });
      });
    });

    describe('When the user provides a filter', () => {
      it('applies that filter', async () => {
        await service.findAndCount(mockAdminToken, {
          programId: 'mockProgramId',
          status: [CaseStatus.InReview, CaseStatus.PaymentSent],
          assigneeId: ['mockAdmin1'],
          search: 'searchString',
        });

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search: 'searchString',
            partnerId: mockAdminToken.partnerId,
          },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindByPartnerId).not.toBeCalled();
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2']) });
      });
    });

    describe('When the user provides a long search string', () => {
      it('truncates the string and applies filters normally', async () => {
        await service.findAndCount(mockAdminToken, {
          programId: 'mockProgramId',
          status: [CaseStatus.InReview, CaseStatus.PaymentSent],
          assigneeId: ['mockAdmin1'],
          search:
            'searchString that is simply much too long to bother our precious database with, we would never want to overburden our hardworking friend that is the database, it does so much for us and asks so little, it is selfless and asks only for tons of cash to be shipped up to the cloud each and every month',
        });

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search:
              'searchString that is simply much too long to bother our precious database with, we would never want',
            partnerId: mockAdminToken.partnerId,
          },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockLogger.warn).toBeCalledWith(
          {
            search:
              'searchString that is simply much too long to bother our precious database with, we would never want to overburden our hardworking friend that is the database, it does so much for us and asks so little, it is selfless and asks only for tons of cash to be shipped up to the cloud each and every month',
          },
          'CaseService.findAndCount: long search string found, truncating',
        );
        expect(mockFindByPartnerId).not.toBeCalled();
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2']) });
      });
    });

    describe('When the user provides pagination', () => {
      it('applies the pagination', async () => {
        await service.findAndCount(
          mockAdminToken,
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search: 'searchString',
          },
          { page: 1, take: 10 },
        );

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search: 'searchString',
            partnerId: mockAdminToken.partnerId,
          },
          { page: 1, take: 10 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2']) });
      });
    });

    describe('When the user does not provides pagination', () => {
      it('applies the default pagination', async () => {
        await service.findAndCount(mockAdminToken, {
          programId: 'mockProgramId',
          status: [CaseStatus.InReview, CaseStatus.PaymentSent],
          assigneeId: ['mockAdmin1'],
          search: 'searchString',
        });

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search: 'searchString',
            partnerId: mockAdminToken.partnerId,
          },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2']) });
      });
    });

    describe('When the user provides a sort', () => {
      it('applies the sort', async () => {
        await service.findAndCount(
          mockAdminToken,
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search: 'searchString',
          },
          { page: 1, take: 10 },
          {
            column: CaseSortColumn.ApplicantName,
            direction: SortDirection.Ascending,
          },
        );

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            programId: 'mockProgramId',
            status: [CaseStatus.InReview, CaseStatus.PaymentSent],
            assigneeId: ['mockAdmin1'],
            search: 'searchString',
            partnerId: mockAdminToken.partnerId,
          },
          { page: 1, take: 10 },
          {
            column: CaseSortColumn.ApplicantName,
            direction: SortDirection.Ascending,
          },
          mockAdminToken,
        );
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2']) });
      });
    });

    describe('When the cases come back in a different order from the ids', () => {
      it('reorders them to the same sort order as the ids', async () => {
        mockFindAndCountIds.mockReset().mockResolvedValueOnce({
          ids: ['case1', 'case2', 'case3'],
          count: 10,
        });
        mockFindByIds
          .mockReset()
          .mockResolvedValueOnce([{ id: 'case2' }, { id: 'case1' }, { id: 'case3' }]);

        const response = await service.findAndCount(mockAdminToken);

        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          { partnerId: mockAdminToken.partnerId },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['case1', 'case2', 'case3']) });
        expect(response.nodes).toEqual([{ id: 'case1' }, { id: 'case2' }, { id: 'case3' }]);
      });
    });

    describe('When the case does not exist because the application was linked to a different one', () => {
      it('looks for a case participant and re-queries for the new case', async () => {
        mockFindByPartnerId.mockResolvedValue([{ id: 'mockProgramId1' }, { id: 'mockProgramId2' }]);
        mockFindAndCountIds
          .mockReset()
          .mockResolvedValueOnce({ ids: [], count: 0 })
          .mockResolvedValueOnce({ ids: ['newCaseId'], count: 1 });
        mockFindAndCountIdsV2.mockResolvedValueOnce({ ids: [], count: 0 });
        mockFindByIds.mockReset().mockResolvedValueOnce([{ id: 'newCaseId', name: 'New Case' }]);
        mockFindParticipant.mockResolvedValueOnce({ caseId: 'newCaseId' });

        const response = await service.findAndCount(mockAdminToken, { ids: ['originalCaseId'] });

        expect(response).toEqual({
          nodes: [{ id: 'newCaseId', name: 'New Case' }],
          pageInfo: { count: 1 },
        });
        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            partnerId: mockAdminToken.partnerId,
            partnerProgramIds: ['mockProgramId1', 'mockProgramId2'],
            ids: ['originalCaseId'],
          },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindAndCountIds).toHaveBeenCalledWith(
          {
            partnerId: mockAdminToken.partnerId,
            partnerProgramIds: ['mockProgramId1', 'mockProgramId2'],
            ids: ['newCaseId'],
          },
          { page: 0, take: 50 },
          undefined,
          mockAdminToken,
        );
        expect(mockFindByIds).toHaveBeenCalledWith({ id: In(['newCaseId']) });
      });
    });
  });

  describe('removeDocuments', () => {
    let findCasePreSave: Mock;
    let saveCase: Mock;
    let service: CaseService;

    beforeEach(() => {
      findCasePreSave = vi.fn();
      saveCase = vi.fn().mockResolvedValue({});

      service = new CaseService(
        {
          findOneOrFail: findCasePreSave,
          findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockCase' }]),
          save: saveCase,
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        {} as unknown as DocumentService,
        {} as unknown as ProgramService,
        {} as unknown as LinkingRepository,
        {} as unknown as WorkflowEventService,
      );
    });

    describe('when all of the documents are removed', () => {
      it('saves an empty list of documents', async () => {
        findCasePreSave.mockResolvedValueOnce({
          id: 'mockCase',
          documents: [{ id: 'doc2' }, { id: 'doc1' }],
        });
        const {
          metadata: { status },
        } = await service.removeDocuments({
          id: 'mockCase',
          documentIds: ['doc1', 'doc2'],
        });

        expect(status).toEqual(200);
        expect(saveCase).toHaveBeenCalledWith({
          id: 'mockCase',
          documents: [],
        });
      });
    });

    describe('when only a partial set of documents are removed', () => {
      it('saves the remaining documents', async () => {
        findCasePreSave.mockResolvedValueOnce({
          id: 'mockCase',
          documents: [{ id: 'doc2' }, { id: 'doc1' }, { id: 'doc3' }, { id: 'doc4' }],
        });
        const {
          metadata: { status },
        } = await service.removeDocuments({
          id: 'mockCase',
          documentIds: ['doc1', 'doc2'],
        });

        expect(status).toEqual(200);
        expect(saveCase).toHaveBeenCalledWith({
          id: 'mockCase',
          documents: [{ id: 'doc3' }, { id: 'doc4' }],
        });
      });
    });
  });

  describe('uploadDocuments', () => {
    let findCase: Mock;
    let uploadDocs: Mock;
    let saveCase: Mock;
    let service: CaseService;
    beforeEach(() => {
      findCase = vi.fn();
      uploadDocs = vi.fn().mockResolvedValueOnce([{ id: 'uploadedDoc1' }, { id: 'uploadedDoc2' }]);
      saveCase = vi.fn();
      service = new CaseService(
        {
          findOneOrFail: findCase,
          findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockCase' }]),
          save: saveCase,
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        { upload: uploadDocs } as unknown as DocumentService,
        {} as unknown as ProgramService,
        {} as unknown as LinkingRepository,
        {} as unknown as WorkflowEventService,
      );
    });

    describe('when the case has no documents', () => {
      it('saves the new documents', async () => {
        findCase.mockResolvedValueOnce({ id: 'mockCase', documents: [] });
        const {
          metadata: { status },
        } = await service.uploadDocuments(mockAdminToken, {
          id: 'mockCase',
          files: [{ filename: 'doc1.pdf' }, { filename: 'doc2.pdf' }],
        } as unknown as UploadEntityDocumentsInput);

        expect(status).toEqual(200);
        expect(uploadDocs).toHaveBeenCalledWith(mockAdminToken, {
          relation: { type: 'case', id: 'mockCase' },
          files: [{ filename: 'doc1.pdf' }, { filename: 'doc2.pdf' }],
        });
        expect(saveCase).toHaveBeenCalledWith({
          id: 'mockCase',
          documents: [{ id: 'uploadedDoc1' }, { id: 'uploadedDoc2' }],
        });
      });
    });

    describe('when the case has existing documents', () => {
      it('appends the new documents to the case', async () => {
        findCase.mockResolvedValueOnce({
          id: 'mockCase',
          documents: [{ id: 'existingDoc1' }, { id: 'existingDoc2' }],
        });
        const {
          metadata: { status },
        } = await service.uploadDocuments(mockAdminToken, {
          id: 'mockCase',
          files: [{ filename: 'doc1.pdf' }, { filename: 'doc2.pdf' }],
        } as unknown as UploadEntityDocumentsInput);

        expect(status).toEqual(200);
        expect(uploadDocs).toHaveBeenCalledWith(mockAdminToken, {
          relation: { type: 'case', id: 'mockCase' },
          files: [{ filename: 'doc1.pdf' }, { filename: 'doc2.pdf' }],
        });
        expect(saveCase).toHaveBeenCalledWith({
          id: 'mockCase',
          documents: [
            { id: 'existingDoc1' },
            { id: 'existingDoc2' },
            { id: 'uploadedDoc1' },
            { id: 'uploadedDoc2' },
          ],
        });
      });
    });
  });

  describe('create', () => {
    let service: CaseService;
    let mockSave: Mock;
    beforeEach(() => {
      mockSave = vi.fn();
      service = new CaseService(
        {
          save: mockSave,
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        {} as unknown as DocumentService,
        {} as unknown as ProgramService,
        {} as unknown as LinkingRepository,
        {} as unknown as WorkflowEventService,
      );
    });
    it('should return the saved case on success', async () => {
      mockSave.mockReturnValue({ id: 'mockCaseId' });
      const response = await service.create({
        name: 'mockCaseName',
        programId: 'mockProgramId',
      });
      expect(response.id).toBe('mockCaseId');
    });
    it('should throw on a failure', async () => {
      mockSave.mockRejectedValue(new Error("you've really done it this time"));
      await expect(
        service.create({ name: 'mockCaseName', programId: 'mockProgramId' }),
      ).rejects.toThrow("you've really done it this time");
    });
  });

  describe('inviteParticipant', () => {
    let linkFn: Mock;
    let createWorkflowFn: Mock;
    let service: CaseService;
    beforeEach(() => {
      linkFn = vi.fn();
      createWorkflowFn = vi.fn();
      service = new CaseService(
        {
          findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockCaseId' }]),
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        {} as unknown as DocumentService,
        {} as unknown as ProgramService,
        { inviteCaseParticipant: linkFn } as unknown as LinkingRepository,
        {
          create: createWorkflowFn,
        } as unknown as WorkflowEventService,
      );
    });

    it('call inviteCaseParticipant from linkRepository and create an event on success', async () => {
      linkFn.mockResolvedValueOnce({});
      createWorkflowFn.mockResolvedValueOnce({ id: 'createdEventId' });
      const input = {
        id: 'mockCaseId',
        participant: {
          applicantTypeId: 'mockTypeId',
          name: 'Landlord Name',
          email: '<EMAIL>',
        },
      };
      const {
        metadata: { status },
      } = await service.inviteParticipant(mockAdminToken, input);

      expect(status).toEqual(200);
      expect(linkFn).toHaveBeenCalledWith({
        caseId: 'mockCaseId',
        participant: {
          applicantTypeId: 'mockTypeId',
          name: 'Landlord Name',
          email: '<EMAIL>',
          autoLink: false,
        },
      });
      expect(createWorkflowFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockCaseId',
        entityType: 'case',
        action: WorkflowAction.InviteCaseParticipant,
        newValue: JSON.stringify({
          applicantTypeId: 'mockTypeId',
          name: 'Landlord Name',
          email: '<EMAIL>',
        }),
      });
    });
    it('log and return 500 on error', async () => {
      const linkError = new Error('baaad');
      linkFn.mockRejectedValueOnce(linkError);
      const input = {
        id: 'mockCaseId',
        participant: {
          applicantTypeId: 'mockTypeId',
          name: 'Landlord Name',
          email: '<EMAIL>',
        },
      };
      const {
        metadata: { status },
      } = await service.inviteParticipant(mockAdminToken, input);

      expect(status).toEqual(500);
      expect(linkFn).toHaveBeenCalledWith({
        caseId: 'mockCaseId',
        participant: {
          applicantTypeId: 'mockTypeId',
          name: 'Landlord Name',
          email: '<EMAIL>',
          autoLink: false,
        },
      });
      expect(createWorkflowFn).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        { error: linkError },
        'CaseService.inviteParticipant: unexpected error for case mockCaseId >',
      );
    });
  });

  describe('unlinkParticipant', () => {
    let unlinkFn: Mock;
    let createWorkflowFn: Mock;
    let service: CaseService;
    beforeEach(() => {
      unlinkFn = vi.fn();
      createWorkflowFn = vi.fn();
      service = new CaseService(
        {
          findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockCaseId' }]),
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        {} as unknown as DocumentService,
        {} as unknown as ProgramService,
        { unlinkCaseParticipant: unlinkFn } as unknown as LinkingRepository,
        {
          create: createWorkflowFn,
        } as unknown as WorkflowEventService,
      );
    });

    it('call unlinkCaseParticipant from linkRepository and create an event on success', async () => {
      unlinkFn.mockResolvedValueOnce({});
      createWorkflowFn.mockResolvedValueOnce({ id: 'createdEventId' });
      const input = {
        id: 'mockCaseId',
        caseParticipantId: 'mockParticipantId',
      };
      const {
        metadata: { status },
      } = await service.unlinkParticipant(mockAdminToken, input);

      expect(status).toEqual(200);
      expect(unlinkFn).toHaveBeenCalledWith({
        caseParticipantId: 'mockParticipantId',
      });
      expect(createWorkflowFn).toHaveBeenCalledWith(mockAdminToken, {
        entityId: 'mockCaseId',
        entityType: 'case',
        action: WorkflowAction.UnlinkCaseParticipant,
        previousValue: JSON.stringify({
          caseParticipantId: 'mockParticipantId',
        }),
      });
    });
    it('log and return 500 on error', async () => {
      const unlinkError = new Error('baaad');
      unlinkFn.mockRejectedValueOnce(unlinkError);
      const input = {
        id: 'mockCaseId',
        caseParticipantId: 'mockParticipantId',
      };
      const {
        metadata: { status },
      } = await service.unlinkParticipant(mockAdminToken, input);

      expect(status).toEqual(500);
      expect(unlinkFn).toHaveBeenCalledWith({
        caseParticipantId: 'mockParticipantId',
      });
      expect(createWorkflowFn).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        { error: unlinkError },
        'CaseService.unlinkParticipant: unexpected error for case mockCaseId >',
      );
    });
  });

  describe('tag management', () => {
    let createFn: Mock;
    let deleteFn: Mock;
    let service: CaseService;
    beforeEach(() => {
      createFn = vi.fn();
      deleteFn = vi.fn();
      service = new CaseService(
        {
          findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockCaseId' }]),
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        { create: createFn, delete: deleteFn } as unknown as CaseTagService,
        {} as unknown as DocumentService,
        {} as unknown as ProgramService,
        {} as unknown as LinkingRepository,
        {} as unknown as WorkflowEventService,
      );
    });

    it('calls create CaseTagRepository', async () => {
      createFn.mockResolvedValueOnce({ metadata: { status: 200 } });
      const input = {
        caseId: 'mockCaseId',
        tagId: 'mockTagId',
      } as CreateCaseTagInput;
      const {
        metadata: { status },
      } = await service.addTagToCase(mockAdminToken, input);

      expect(status).toEqual(200);
      expect(createFn).toHaveBeenCalledWith({
        caseId: 'mockCaseId',
        tagId: 'mockTagId',
        userId: mockAdminToken.userId,
      });
    });

    it('calls create delete CaseTagRepository', async () => {
      deleteFn.mockResolvedValueOnce({ metadata: { status: 200 } });
      const input = {
        caseTagId: 'mockCaseTagId',
      } as DeleteCaseTagInput;
      const {
        metadata: { status },
      } = await service.removeTagFromCase(mockAdminToken, input);

      expect(status).toEqual(200);
      expect(deleteFn).toHaveBeenCalledWith({
        caseTagId: 'mockCaseTagId',
        userId: mockAdminToken.userId,
      });
    });
  });

  describe('findWorkflowEvents', () => {
    let findFn: Mock;
    let findEventsFn: Mock;
    let service: CaseService;
    beforeEach(() => {
      findFn = vi.fn().mockResolvedValue({
        id: 'mockCaseId',
        fulfillments: [{ id: 'mockFulfillmentId' }],
      });
      findEventsFn = vi.fn().mockResolvedValue([{ id: 'mockEventId' }]);
      service = new CaseService(
        {
          findOne: findFn,
        } as unknown as CaseRepository,
        {} as unknown as CaseMetadataRepository,
        {} as unknown as CaseParticipantService,
        {} as unknown as CaseTagService,
        {} as unknown as DocumentService,
        {} as unknown as ProgramService,
        {} as unknown as LinkingRepository,
        { findManyWithOptions: findEventsFn } as unknown as WorkflowEventService,
      );
    });

    it('Should find events with entities related to case when an admin is authenticated', async () => {
      const result = await service.findWorkflowEvents(
        mockAdminToken,
        'mockCaseId',
        'mockApplicationId',
      );

      expect(result).toEqual([{ id: 'mockEventId' }]);
      expect(findFn).toHaveBeenCalledWith({
        where: { id: 'mockCaseId' },
        relations: ['fulfillments'],
        withDeleted: true,
      });
      expect(findEventsFn).toHaveBeenCalledWith({
        where: {
          entityId: In(['mockCaseId', 'mockApplicationId', 'mockFulfillmentId']),
          action: Not(WorkflowAction.PaymentUpdate),
        },
        order: { createdAt: 'ASC' },
      });
    });

    it('should find events with case id only when a normal user is authenticated', async () => {
      const result = await service.findWorkflowEvents(
        mockLoginToken,
        'mockCaseId',
        'mockApplicationId',
      );

      expect(result).toEqual([{ id: 'mockEventId' }]);
      expect(findFn).not.toHaveBeenCalled();
      expect(findEventsFn).toHaveBeenCalledWith({
        where: {
          entityId: In(['mockCaseId', 'mockApplicationId']),
          action: In([
            WorkflowAction.CaseComment,
            WorkflowAction.ChangeStatus,
            WorkflowAction.Submission,
          ]),
        },
        order: { createdAt: 'ASC' },
      });
    });
  });
});

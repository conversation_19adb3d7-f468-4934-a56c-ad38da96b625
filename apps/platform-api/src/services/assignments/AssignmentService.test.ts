import { Assignment } from '@bybeam/platform-types';
import { AdminRepository, AssignmentRepository } from '@platform-api/@types/repositories/index.js';
import { In, IsNull } from 'typeorm';
import AssignmentService from './AssignmentService.js';

describe('AssignmentService', () => {
  describe('upsert', () => {
    describe('when assignments are all new', () => {
      const repository = {
        findBy: vi.fn().mockResolvedValueOnce([]),
        softDelete: vi.fn(),
        save: vi.fn(),
      } as unknown as AssignmentRepository;
      const service = new AssignmentService(repository, {} as unknown as AdminRepository);

      it("inserts the assignments and doesn't delete anything", async () => {
        await service.upsert({
          ids: ['mockCase1', 'mockCase2'],
          assigneeId: 'mockAdmin1',
        });

        expect(repository.softDelete).not.toHaveBeenCalled();
        expect(repository.save).toHaveBeenCalledWith([
          { caseId: 'mockCase1', assigneeId: 'mockAdmin1' },
          { caseId: 'mockCase2', assigneeId: 'mockAdmin1' },
        ]);
      });
    });

    describe('when some of the cases are already assigned the same admin as the new one', () => {
      const repository = {
        findBy: vi.fn().mockResolvedValueOnce([{ caseId: 'mockCase1', assigneeId: 'mockAdmin1' }]),
        softDelete: vi.fn(),
        save: vi.fn(),
      } as unknown as AssignmentRepository;
      const service = new AssignmentService(repository, {} as unknown as AdminRepository);

      it('only inserts the new assignments', async () => {
        await service.upsert({
          ids: ['mockCase1', 'mockCase2'],
          assigneeId: 'mockAdmin1',
        });

        expect(repository.softDelete).not.toHaveBeenCalled();
        expect(repository.save).toHaveBeenCalledWith([
          { caseId: 'mockCase2', assigneeId: 'mockAdmin1' },
        ]);
      });
    });

    describe('when some of the cases have other assignments', () => {
      const repository = {
        findBy: vi.fn().mockResolvedValueOnce([
          { caseId: 'mockCase1', assigneeId: 'mockAdmin1' },
          { caseId: 'mockCase3', assigneeId: 'mockAdmin2' },
        ]),
        softDelete: vi.fn(),
        save: vi.fn(),
      } as unknown as AssignmentRepository;
      const service = new AssignmentService(repository, {} as unknown as AdminRepository);

      it('deletes the old assignments before inserting new ones', async () => {
        await service.upsert({
          ids: ['mockCase1', 'mockCase2', 'mockCase3'],
          assigneeId: 'mockAdmin1',
        });

        expect(repository.softDelete).toHaveBeenCalledWith({
          caseId: In(['mockCase3']),
          deactivatedAt: IsNull(),
        });
        expect(repository.save).toHaveBeenCalledWith([
          { caseId: 'mockCase2', assigneeId: 'mockAdmin1' },
          { caseId: 'mockCase3', assigneeId: 'mockAdmin1' },
        ]);
      });
    });

    describe('when the assigneeId is undefined and no cases have assignments', () => {
      const repository = {
        findBy: vi.fn().mockResolvedValueOnce([]),
        softDelete: vi.fn(),
        save: vi.fn(),
      } as unknown as AssignmentRepository;
      const service = new AssignmentService(repository, {} as unknown as AdminRepository);

      it('does nothing', async () => {
        await service.upsert({
          ids: ['mockCase1', 'mockCase2', 'mockCase3'],
          assigneeId: undefined,
        });

        expect(repository.softDelete).not.toHaveBeenCalled();
        expect(repository.save).not.toHaveBeenCalled();
      });
    });

    describe('when the assigneeId is undefined and some cases have assignments', () => {
      const repository = {
        findBy: vi.fn().mockResolvedValueOnce([
          { id: 'mockAssignment1', caseId: 'mockCase1', assigneeId: 'mockAdmin1' },
          { id: 'mockAssignment2', caseId: 'mockCase3', assigneeId: 'mockAdmin2' },
        ]),
        softDelete: vi.fn(),
        save: vi.fn(),
      } as unknown as AssignmentRepository;
      const service = new AssignmentService(repository, {} as unknown as AdminRepository);

      it('deletes the existing assignments', async () => {
        await service.upsert({
          ids: ['mockCase1', 'mockCase2', 'mockCase3'],
          assigneeId: undefined,
        });

        expect(repository.softDelete).toHaveBeenCalledWith(['mockAssignment1', 'mockAssignment2']);
        expect(repository.save).not.toHaveBeenCalled();
      });
    });
  });

  describe('autoAssign', () => {
    it('throws if any of the cases are already assigned to someone', async () => {
      const mockCountFn = vi.fn().mockResolvedValueOnce(1);
      const service = new AssignmentService(
        { count: mockCountFn } as unknown as AssignmentRepository,
        {} as AdminRepository,
      );

      await expect(
        service.autoAssign({ ids: ['mockCaseId'], partnerId: 'mockPartnerId' }),
      ).rejects.toThrow('Cannot auto-assign cases which are already assigned');
      expect(mockCountFn).toHaveBeenCalledWith({ where: { caseId: In(['mockCaseId']) } });
    });

    it('assigns the case to the admin with the lowest current workload', async () => {
      const mockSaveFn = vi.fn().mockResolvedValueOnce([{ id: 'mockAssignmentId' }]);
      const service = new AssignmentService(
        {
          count: vi.fn().mockResolvedValueOnce(0),
          save: mockSaveFn,
        } as unknown as AssignmentRepository,
        {
          countAdminAssignments: vi.fn().mockResolvedValueOnce([
            { id: 'mockAdmin1', count: 3 },
            { id: 'mockAdmin2', count: 1 },
            { id: 'mockAdmin3', count: 10 },
          ]),
        } as unknown as AdminRepository,
      );

      const result = await service.autoAssign({ ids: ['mockCaseId'], partnerId: 'mockPartnerId' });

      expect(result).toEqual([{ id: 'mockAssignmentId' }]);
      expect(mockSaveFn).toHaveBeenCalledWith([{ assigneeId: 'mockAdmin2', caseId: 'mockCaseId' }]);
    });

    it('distributes multiple cases among admins according to current workload', async () => {
      const mockSaveFn = vi.fn().mockResolvedValueOnce([]);
      const service = new AssignmentService(
        {
          count: vi.fn().mockResolvedValueOnce(0),
          save: mockSaveFn,
        } as unknown as AssignmentRepository,
        {
          countAdminAssignments: vi.fn().mockResolvedValueOnce([
            { id: 'mockAdmin1', count: 3 },
            { id: 'mockAdmin2', count: 1 },
            { id: 'mockAdmin3', count: 2 },
          ]),
        } as unknown as AdminRepository,
      );

      await service.autoAssign({
        ids: ['case1', 'case2', 'case3', 'case4', 'case5'],
        partnerId: 'mockPartnerId',
      });

      expect(mockSaveFn).toHaveBeenCalledWith([
        { assigneeId: 'mockAdmin2', caseId: 'case1' },
        { assigneeId: 'mockAdmin2', caseId: 'case2' },
        { assigneeId: 'mockAdmin3', caseId: 'case3' },
        { assigneeId: 'mockAdmin1', caseId: 'case4' },
        { assigneeId: 'mockAdmin2', caseId: 'case5' },
      ]);
    });

    it('does not assign above the assignment cap', async () => {
      const mockSaveFn = vi.fn().mockResolvedValueOnce([]);
      const service = new AssignmentService(
        {
          count: vi.fn().mockResolvedValueOnce(0),
          save: mockSaveFn,
        } as unknown as AssignmentRepository,
        {
          countAdminAssignments: vi.fn().mockResolvedValueOnce([
            { id: 'mockAdmin1', count: 30 },
            { id: 'mockAdmin2', count: 28 },
            { id: 'mockAdmin3', count: 29 },
          ]),
        } as unknown as AdminRepository,
      );

      await service.autoAssign({
        ids: ['case1', 'case2', 'case3', 'case4', 'case5'],
        partnerId: 'mockPartnerId',
      });

      expect(mockSaveFn).toHaveBeenCalledWith([
        { assigneeId: 'mockAdmin2', caseId: 'case1' },
        { assigneeId: 'mockAdmin2', caseId: 'case2' },
        { assigneeId: 'mockAdmin3', caseId: 'case3' },
      ]);
    });

    it('does nothing if no admins are available to be assigned', async () => {
      const mockSaveFn = vi.fn().mockResolvedValueOnce([]);
      const service = new AssignmentService(
        {
          count: vi.fn().mockResolvedValueOnce(0),
          save: mockSaveFn,
        } as unknown as AssignmentRepository,
        {
          countAdminAssignments: vi.fn().mockResolvedValueOnce([]),
        } as unknown as AdminRepository,
      );

      await service.autoAssign({
        ids: ['case1', 'case2', 'case3', 'case4', 'case5'],
        partnerId: 'mockPartnerId',
      });

      expect(mockSaveFn).not.toHaveBeenCalled();
    });
  });

  describe('unassignAllCasesFromAdmin', () => {
    describe('admin does not exist', () => {
      const adminId = 'some non existent admin id';
      const assignmentRepository = {
        findBy: vi.fn().mockResolvedValueOnce([]),
        softDelete: vi.fn(),
      } as unknown as AssignmentRepository;
      const adminRepository = {} as unknown as AdminRepository;
      const service = new AssignmentService(assignmentRepository, adminRepository);
      it('does nothing', async () => {
        const response = await service.unassignAllCasesFromAdmin(adminId);
        expect(response).toBeUndefined();
        expect(assignmentRepository.softDelete).not.toHaveBeenCalled();
      });
    });
    describe('admin does not have cases assigned', () => {
      const adminId = 'some admin id';
      const assignmentRepository = {
        findBy: vi.fn().mockResolvedValueOnce([]),
        softDelete: vi.fn(),
      } as unknown as AssignmentRepository;
      const adminRepository = {} as unknown as AdminRepository;
      const service = new AssignmentService(assignmentRepository, adminRepository);
      it('does nothing', async () => {
        const response = await service.unassignAllCasesFromAdmin(adminId);
        expect(response).toBeUndefined();
        expect(assignmentRepository.softDelete).not.toHaveBeenCalled();
      });
    });
    describe('admin has cases assigned', () => {
      const adminId = 'some admin id';
      const assignments: Partial<Assignment>[] = [
        {
          id: 'assignment id 1',
          caseId: 'case id 1',
          assigneeId: adminId,
        },
        {
          id: 'assignment id 2',
          caseId: 'case id 2',
          assigneeId: adminId,
        },
      ];
      const assignmentRepository = {
        findBy: vi.fn().mockResolvedValueOnce(assignments),
        softDelete: vi.fn().mockResolvedValueOnce(undefined),
      } as unknown as AssignmentRepository;
      const adminRepository = {} as unknown as AdminRepository;
      const service = new AssignmentService(assignmentRepository, adminRepository);
      const assignmentsIds = assignments.map(({ id }) => id);
      it('unassigns all of the cases from that admin', async () => {
        const response = await service.unassignAllCasesFromAdmin(adminId);
        expect(response).toBeUndefined();
        expect(assignmentRepository.softDelete).toHaveBeenCalledWith(assignmentsIds);
      });
    });
  });
});

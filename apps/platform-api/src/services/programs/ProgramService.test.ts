import {
  CaseStatus,
  DefaultNotificationType,
  FeatureName,
  PaymentStatus,
  ProgramStatus,
  UpdateProgramsInput,
  WORKFLOW_STAGE_NOTIFICATION_TYPES,
  WorkflowAction,
} from '@bybeam/platform-types';
import { mockAdminToken, mockLoginToken } from '@platform-api-test/mocks.js';
import { mockLogger } from '@platform-api-test/setup/globals.js';
import {
  LabelRepository,
  NotificationTemplateRepository,
  ProgramRepository,
} from '@platform-api/@types/repositories/index.js';
import { StatusCodes } from 'http-status-codes';
import { Mock } from 'vitest';
import ConfigurationService from '../config/ConfigurationService.js';
import WorkflowEventService from '../workflowEvent/WorkflowEventService.js';
import ProgramService from './ProgramService.js';

const mockProgram = {
  id: 'mockProgramUUID',
  status: ProgramStatus.Open,
};

const mockProgramSecondary = {
  id: 'mockProgramSecondaryUUID',
  status: ProgramStatus.Closed,
};

describe('ProgramService', () => {
  let saveProgramFn: Mock;
  let createWorkflowEventFn: Mock;
  let findOneFn: Mock;
  let findFn: Mock;
  let programService: ProgramService;

  beforeEach(() => {
    saveProgramFn = vi.fn();
    createWorkflowEventFn = vi.fn();
    findOneFn = vi.fn();
    findFn = vi.fn().mockReturnValue([mockProgram]);
    programService = new ProgramService({
      programRepository: {
        find: findFn,
        findBy: findFn,
        findOne: findOneFn,
        findById: vi.fn().mockReturnValue(mockProgram),
        count: vi.fn().mockResolvedValue(1),
        save: saveProgramFn,
      } as unknown as ProgramRepository,
      labelRepository: {} as unknown as LabelRepository,
      workflowEventService: { create: createWorkflowEventFn } as unknown as WorkflowEventService,
      notificationTemplateRepository: {} as unknown as NotificationTemplateRepository,
      configService: {} as unknown as ConfigurationService,
    });
  });

  describe('QueryServiceImplementation', () => {
    describe('implemented methods', () => {
      it('should find a program by id', async () => {
        const result = await programService.findById(mockProgram.id);
        expect(result).toEqual(mockProgram);
      });

      it('should find a list of programs and count the length', async () => {
        findFn.mockResolvedValueOnce([mockProgram]);
        const result = await programService.findAndCount(mockLoginToken);
        expect(result.nodes).toContain(mockProgram);
        expect(result.pageInfo.count).toEqual(1);
      });
    });
  });
  describe('update', () => {
    it('should update the program status and create a workflow event if status is changed', async () => {
      const updateInput = [
        {
          id: mockProgram.id,
          status: ProgramStatus.Closed,
        },
      ] as UpdateProgramsInput;
      findFn.mockResolvedValueOnce([mockProgram]);

      const { metadata } = await programService.update(mockAdminToken, updateInput);

      expect(saveProgramFn).toHaveBeenCalledWith(updateInput);
      expect(createWorkflowEventFn).toHaveBeenCalledWith(mockAdminToken, [
        {
          entityId: mockProgram.id,
          entityType: 'program',
          action: WorkflowAction.ProgramUpdate,
          previousValue: JSON.stringify(mockProgram),
          newValue: JSON.stringify({ status: ProgramStatus.Closed }),
        },
      ]);
      expect(metadata).toEqual({ status: StatusCodes.OK, message: 'OK' });
    });
    it('should update multiple program statuses and create workflow events for each if status is changed', async () => {
      const updateInput = [
        {
          id: mockProgram.id,
          status: ProgramStatus.Closed,
        },
        {
          id: mockProgramSecondary.id,
          status: ProgramStatus.Open,
        },
      ] as UpdateProgramsInput;
      findFn.mockResolvedValueOnce([mockProgram, mockProgramSecondary]);

      const { metadata } = await programService.update(mockAdminToken, updateInput);

      expect(saveProgramFn).toHaveBeenCalledWith(updateInput);
      expect(createWorkflowEventFn).toHaveBeenCalledWith(mockAdminToken, [
        {
          entityId: mockProgram.id,
          entityType: 'program',
          action: WorkflowAction.ProgramUpdate,
          previousValue: JSON.stringify(mockProgram),
          newValue: JSON.stringify({ status: ProgramStatus.Closed }),
        },
        {
          entityId: mockProgramSecondary.id,
          entityType: 'program',
          action: WorkflowAction.ProgramUpdate,
          previousValue: JSON.stringify(mockProgramSecondary),
          newValue: JSON.stringify({ status: ProgramStatus.Open }),
        },
      ]);
      expect(metadata).toEqual({ status: StatusCodes.OK, message: 'OK' });
    });
    it('should return response error on a failure', async () => {
      findFn.mockResolvedValueOnce([mockProgram]);
      const error = new Error('can not do save');
      saveProgramFn.mockRejectedValue(error);
      await expect(
        programService.update(mockAdminToken, [
          {
            id: mockProgram.id,
            status: ProgramStatus.Closed,
          },
        ] as UpdateProgramsInput),
      ).resolves.toStrictEqual(
        expect.objectContaining({
          metadata: {
            errors: ['Internal Server Error'],
            message: 'Internal Server Error',
            status: 500,
          },
        }),
      );
      expect(mockLogger.error).toBeCalledWith(
        { error },
        'ProgramService.update: unexpected error during update >',
      );
    });
    it('should return response error of 404 if a program does not exist', async () => {
      findFn.mockResolvedValueOnce([]);
      await expect(
        programService.update(mockAdminToken, [
          {
            id: mockProgram.id,
            status: ProgramStatus.Closed,
          },
        ] as UpdateProgramsInput),
      ).resolves.toStrictEqual(
        expect.objectContaining({
          metadata: {
            errors: ['One or more programs does not exist'],
            message: 'Not Found',
            status: 404,
          },
        }),
      );
    });

    it('should sanitize programContext description and reject malicious input', async () => {
      const mockModelArmorClient = {
        sanitizeUserPrompt: vi.fn(),
      };

      // Mock the ModelArmorClient to simulate a failed sanitization
      mockModelArmorClient.sanitizeUserPrompt.mockResolvedValueOnce([
        {
          sanitizationResult: {
            filterMatchState: 'MATCH_FOUND', // This indicates malicious content was detected
          },
        },
      ]);

      // Create a new service instance with the mocked ModelArmorClient
      const serviceWithMockedClient = new ProgramService({
        programRepository: {
          find: findFn,
          findBy: findFn,
          findOne: findOneFn,
          findById: vi.fn().mockReturnValue(mockProgram),
          count: vi.fn().mockResolvedValue(1),
          save: saveProgramFn,
        } as unknown as ProgramRepository,
        labelRepository: {} as unknown as LabelRepository,
        workflowEventService: { create: createWorkflowEventFn } as unknown as WorkflowEventService,
        notificationTemplateRepository: {} as unknown as NotificationTemplateRepository,
        configService: {} as unknown as ConfigurationService,
      });

      // Replace the modelArmorClient with our mock
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      (serviceWithMockedClient as any).modelArmorClient = mockModelArmorClient;

      findFn.mockResolvedValueOnce([mockProgram]);

      const updateInput = [
        {
          id: mockProgram.id,
          programContext: {
            description: 'This is a malicious prompt injection attempt',
          },
        },
      ] as UpdateProgramsInput;

      const result = await serviceWithMockedClient.update(mockAdminToken, updateInput);

      expect(mockModelArmorClient.sanitizeUserPrompt).toHaveBeenCalledWith({
        name: `projects/${process.env.GOOGLE_PROJECT_ID}/locations/${process.env.REGION}/templates/${process.env.MODEL_ARMOR_TEMPLATE_ID}`,
        userPromptData: {
          text: 'This is a malicious prompt injection attempt',
        },
      });

      expect(result).toStrictEqual(
        expect.objectContaining({
          metadata: {
            errors: ['Sanitization failed'],
            message: 'Bad Request',
            status: 400,
          },
        }),
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        { error: expect.any(Error) },
        'ProgramService.update: unexpected error during update >',
      );
    });

    it('should successfully update programContext description when sanitization passes', async () => {
      const mockModelArmorClient = {
        sanitizeUserPrompt: vi.fn(),
      };

      // Mock the ModelArmorClient to simulate successful sanitization
      mockModelArmorClient.sanitizeUserPrompt.mockResolvedValueOnce([
        {
          sanitizationResult: {
            filterMatchState: 'NO_MATCH_FOUND', // This indicates content is safe
          },
        },
      ]);

      // Create a new service instance with the mocked ModelArmorClient
      const serviceWithMockedClient = new ProgramService({
        programRepository: {
          find: findFn,
          findBy: findFn,
          findOne: findOneFn,
          findById: vi.fn().mockReturnValue(mockProgram),
          count: vi.fn().mockResolvedValue(1),
          save: saveProgramFn,
        } as unknown as ProgramRepository,
        labelRepository: {} as unknown as LabelRepository,
        workflowEventService: { create: createWorkflowEventFn } as unknown as WorkflowEventService,
        notificationTemplateRepository: {} as unknown as NotificationTemplateRepository,
        configService: {} as unknown as ConfigurationService,
      });

      // Replace the modelArmorClient with our mock
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      (serviceWithMockedClient as any).modelArmorClient = mockModelArmorClient;

      findFn.mockResolvedValueOnce([mockProgram]);

      const updateInput = [
        {
          id: mockProgram.id,
          programContext: {
            description: 'This is a safe program description',
          },
        },
      ] as UpdateProgramsInput;

      const { metadata } = await serviceWithMockedClient.update(mockAdminToken, updateInput);

      expect(mockModelArmorClient.sanitizeUserPrompt).toHaveBeenCalledWith({
        name: `projects/${process.env.GOOGLE_PROJECT_ID}/locations/${process.env.REGION}/templates/${process.env.MODEL_ARMOR_TEMPLATE_ID}`,
        userPromptData: {
          text: 'This is a safe program description',
        },
      });

      expect(saveProgramFn).toHaveBeenCalledWith([
        expect.objectContaining({
          id: mockProgram.id,
          config: expect.objectContaining({
            programContext: expect.objectContaining({
              description: 'This is a safe program description',
            }),
          }),
        }),
      ]);

      expect(metadata).toEqual({ status: StatusCodes.OK, message: 'OK' });
    });
  });

  describe('calculateStats', () => {
    let getStatsByFundFn: Mock;
    let getCaseCountsFn: Mock;
    let mockService: ProgramService;
    beforeEach(() => {
      getStatsByFundFn = vi.fn();
      getCaseCountsFn = vi.fn();
      mockService = new ProgramService({
        programRepository: {
          getStatsByFund: getStatsByFundFn,
          getCaseCounts: getCaseCountsFn,
        } as unknown as ProgramRepository,
        labelRepository: {} as unknown as LabelRepository,
        workflowEventService: {} as unknown as WorkflowEventService,
        notificationTemplateRepository: {} as unknown as NotificationTemplateRepository,
        configService: {} as unknown as ConfigurationService,
      });
    });
    describe('programFundStats', () => {
      it('should call the repository calculation method and return the transformed and summed value', async () => {
        getStatsByFundFn.mockResolvedValueOnce([
          {
            id: 'mockProgramId',
            fundId: 'mockFundId1',
            status: PaymentStatus.Success,
            total: 20000,
            count: 1,
          },
          {
            id: 'mockProgramId',
            fundId: 'mockFundId1',
            status: PaymentStatus.Initiated,
            total: 20000,
            count: 1,
          },
          {
            id: 'mockProgramId',
            fundId: 'mockFundId1',
            status: PaymentStatus.Failed,
            total: 20000,
            count: 1,
          },
          {
            id: 'mockProgramId',
            fundId: 'mockFundId2',
            status: PaymentStatus.Success,
            total: 15000,
            count: 1,
          },
        ]);
        getCaseCountsFn.mockResolvedValueOnce([]);
        const result = await mockService.calculateStats('mockProgramId');
        expect(getStatsByFundFn).toBeCalledWith(
          ['mockProgramId'],
          expect.arrayContaining(['initiated', 'success']),
        );
        expect(result).toStrictEqual({
          id: 'mockProgramId',
          awardedBalance: 55000,
          caseCounts: expect.any(Object),
          programFundStats: [
            {
              programId: 'mockProgramId',
              fundId: 'mockFundId1',
              awardedBalance: 40000,
              obligatedBalance: 20000,
              paymentCount: 3,
            },
            {
              programId: 'mockProgramId',
              fundId: 'mockFundId2',
              awardedBalance: 15000,
              obligatedBalance: 0,
              paymentCount: 1,
            },
          ],
        });
      });
      it('should default to 0 when balances are nullish', async () => {
        getStatsByFundFn.mockResolvedValueOnce([
          {
            id: 'mockProgramId',
            fundId: 'mockFundId1',
            status: PaymentStatus.Success,
            total: undefined,
            count: 0,
          },
          {
            id: 'mockProgramId',
            fundId: 'mockFundId2',
            status: PaymentStatus.Success,
            total: null,
            count: 0,
          },
        ]);
        getCaseCountsFn.mockResolvedValueOnce([]);
        const result = await mockService.calculateStats('mockProgramId');
        expect(getStatsByFundFn).toBeCalledWith(
          ['mockProgramId'],
          expect.arrayContaining(['initiated', 'success']),
        );
        expect(result).toStrictEqual({
          id: 'mockProgramId',
          awardedBalance: 0,
          caseCounts: expect.any(Object),
          programFundStats: [
            {
              programId: 'mockProgramId',
              fundId: 'mockFundId1',
              awardedBalance: 0,
              obligatedBalance: 0,
              paymentCount: 0,
            },
            {
              programId: 'mockProgramId',
              fundId: 'mockFundId2',
              awardedBalance: 0,
              obligatedBalance: 0,
              paymentCount: 0,
            },
          ],
        });
      });
      it('should use the dataloader to filter the results', async () => {
        getStatsByFundFn.mockResolvedValueOnce([
          {
            id: 'mockProgramId',
            fundId: 'mockFundId1',
            status: PaymentStatus.Success,
            total: 20000,
            count: 1,
          },
          {
            id: 'mockProgramId',
            fundId: 'mockFundId2',
            status: PaymentStatus.Success,
            total: 15000,
            count: 1,
          },
          {
            id: 'mockProgramId2',
            fundId: 'mockFundId2',
            status: PaymentStatus.Success,
            total: 15000,
            count: 1,
          },
        ]);
        getCaseCountsFn.mockResolvedValueOnce([]);
        const result = await mockService.calculateStats('mockProgramId');
        expect(getStatsByFundFn).toBeCalledWith(
          ['mockProgramId'],
          expect.arrayContaining(['initiated', 'success']),
        );
        expect(result).toStrictEqual({
          id: 'mockProgramId',
          awardedBalance: 35000,
          caseCounts: expect.any(Object),
          programFundStats: [
            {
              programId: 'mockProgramId',
              fundId: 'mockFundId1',
              awardedBalance: 20000,
              obligatedBalance: 0,
              paymentCount: 1,
            },
            {
              programId: 'mockProgramId',
              fundId: 'mockFundId2',
              awardedBalance: 15000,
              obligatedBalance: 0,
              paymentCount: 1,
            },
          ],
        });
      });
    });
    describe('caseCounts', () => {
      it('should call the repository group method and return a count for all CaseStatuses', async () => {
        getStatsByFundFn.mockResolvedValueOnce([]);
        getCaseCountsFn.mockResolvedValueOnce([
          { id: 'mockProgramId', status: CaseStatus.ReadyForReview, count: 14 },
          { id: 'mockProgramId', status: CaseStatus.InReview, count: 6 },
          { id: 'mockProgramId', status: CaseStatus.Approved, count: 10 },
        ]);
        const result = await mockService.calculateStats('mockProgramId');
        expect(getCaseCountsFn).toBeCalledWith(['mockProgramId']);
        expect(result).toStrictEqual({
          id: 'mockProgramId',
          awardedBalance: 0,
          caseCounts: {
            All: 30,
            Approved: 10,
            Archived: 0,
            Denied: 0,
            FiscalReview: 0,
            InReview: 6,
            Incomplete: 0,
            InProgress: 0,
            PaymentSent: 0,
            PendingCertification: 0,
            ReadyForReview: 14,
            Withdrawn: 0,
          },
          programFundStats: expect.any(Array),
        });
      });
      it('should use the dataloader to filter the results', async () => {
        getStatsByFundFn.mockResolvedValueOnce([]);
        getCaseCountsFn.mockResolvedValueOnce([
          { id: 'mockProgramId', status: CaseStatus.ReadyForReview, count: 14 },
          { id: 'mockProgramId', status: CaseStatus.InReview, count: 6 },
          { id: 'mockProgramId', status: CaseStatus.Approved, count: 10 },
          { id: 'mockProgramId2', status: CaseStatus.Approved, count: 10 },
        ]);
        const result = await mockService.calculateStats('mockProgramId');
        expect(getCaseCountsFn).toBeCalledWith(['mockProgramId']);
        expect(result).toStrictEqual({
          id: 'mockProgramId',
          awardedBalance: 0,
          caseCounts: {
            All: 30,
            Approved: 10,
            Archived: 0,
            Denied: 0,
            FiscalReview: 0,
            InReview: 6,
            Incomplete: 0,
            InProgress: 0,
            PaymentSent: 0,
            PendingCertification: 0,
            ReadyForReview: 14,
            Withdrawn: 0,
          },
          programFundStats: expect.any(Array),
        });
      });
    });
  });

  describe('getProgramWorkflow', () => {
    let findTemplatesFn: Mock;
    let mockService: ProgramService;

    beforeEach(() => {
      findTemplatesFn = vi.fn();
      mockService = new ProgramService({
        programRepository: {} as unknown as ProgramRepository,
        labelRepository: {} as unknown as LabelRepository,
        workflowEventService: {} as unknown as WorkflowEventService,
        notificationTemplateRepository: {
          findByProgramId: findTemplatesFn,
        } as unknown as NotificationTemplateRepository,
        configService: {} as unknown as ConfigurationService,
      });
    });

    it('should return workflow stages with email templates', async () => {
      const mockGlobalTemplates = [
        { id: 'globalTemplate1', type: 'ReadyForReview' },
        { id: 'globalTemplate2', type: 'InReview' },
      ];
      const mockPartnerTemplates = [{ id: 'partnerTemplate1', type: 'Approved' }];
      const mockProgramTemplates = [{ id: 'programTemplate1', type: 'ReadyForReview' }];

      findTemplatesFn.mockResolvedValueOnce([
        ...mockGlobalTemplates,
        ...mockPartnerTemplates,
        ...mockProgramTemplates,
      ]);

      const result = await mockService.getProgramWorkflow('mockProgramId', 'mockPartnerId');

      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
      });

      expect(result).toStrictEqual({
        workflowStages: Object.values(CaseStatus).map((status) => {
          const notificationType = WORKFLOW_STAGE_NOTIFICATION_TYPES[status];
          const templatesForNotificationType = [
            ...mockGlobalTemplates,
            ...mockPartnerTemplates,
            ...mockProgramTemplates,
          ].filter(({ type }) => type === notificationType);
          return {
            status: status,
            workflowNotifications: templatesForNotificationType.map((template) => ({
              template,
            })),
          };
        }),
      });
    });

    it('should return empty workflow notifications if no templates are found', async () => {
      findTemplatesFn.mockResolvedValueOnce([]);

      const result = await mockService.getProgramWorkflow('mockProgramId', 'mockPartnerId');

      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
      });

      expect(result).toStrictEqual({
        workflowStages: Object.values(CaseStatus).map((status) => ({
          status: status,
          workflowNotifications: [],
        })),
      });
    });
  });

  describe('getProgramNotifications', () => {
    let findTemplatesFn: Mock;
    let findProgramFn: Mock;
    let findCommunicationChannelsConfigByPartnerIdFn: Mock;
    let mockService: ProgramService;

    beforeEach(() => {
      findProgramFn = vi.fn();
      findTemplatesFn = vi.fn();
      findCommunicationChannelsConfigByPartnerIdFn = vi.fn();
      mockService = new ProgramService({
        programRepository: {
          findOne: findProgramFn,
        } as unknown as ProgramRepository,
        labelRepository: {} as unknown as LabelRepository,
        workflowEventService: {} as unknown as WorkflowEventService,
        notificationTemplateRepository: {
          findByProgramId: findTemplatesFn,
        } as unknown as NotificationTemplateRepository,
        configService: {
          findCommunicationChannelsConfigByPartnerId: findCommunicationChannelsConfigByPartnerIdFn,
        } as unknown as ConfigurationService,
      });
    });
    it('should request notification based on program features', async () => {
      const notificationTypes = [
        'ApplicationDenied',
        'ApplicationIncomplete',
        'ApplicationSubmitted',
        'ApplicationWithdrawn',
        'CaseComment',
        'PaymentsClaimFunds',
        'PaymentsFundsClaimed',
        'PaymentsApplicantPaymentReset',
      ];
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: { channels: { email: true, sms: true } },
      });
      findTemplatesFn.mockResolvedValueOnce(
        notificationTypes.flatMap((type) => [
          { id: type, type, channel: 'email', content: {} },
          { id: type, type, channel: 'sms', content: {} },
        ]),
      );
      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: notificationTypes,
        channels: ['email', 'sms'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'ApplicationDenied',
            channelConfigurations: [
              { enabled: true, channel: 'email', template: expect.any(Object) },
              { enabled: true, channel: 'sms', template: expect.any(Object) },
            ],
          },
        ]),
      );
    });
    it('should request notification based on partner features', async () => {
      const notificationTypes = [
        'ApplicationDenied',
        'ApplicationIncomplete',
        'ApplicationSubmitted',
        'ApplicationWithdrawn',
        'CaseComment',
        'ParticipantCaseCommentAssignee',
        'ParticipantCaseCommentSupport',
      ];
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [],
        partner: {
          features: [{ enabled: true, feature: { name: FeatureName.ApplicantComments } }],
        },
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: { channels: { email: true, sms: true } },
      });
      findTemplatesFn.mockResolvedValueOnce(
        notificationTypes.flatMap((type) => [
          { id: type, type, channel: 'email', content: {} },
          { id: type, type, channel: 'sms', content: {} },
        ]),
      );
      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: notificationTypes,
        channels: ['email', 'sms'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'ParticipantCaseCommentAssignee',
            channelConfigurations: [
              { enabled: true, channel: 'email', template: expect.any(Object) },
              { enabled: true, channel: 'sms', template: expect.any(Object) },
            ],
          },
          {
            type: 'ParticipantCaseCommentSupport',
            channelConfigurations: [
              { enabled: true, channel: 'email', template: expect.any(Object) },
              { enabled: true, channel: 'sms', template: expect.any(Object) },
            ],
          },
        ]),
      );
    });
    it('should include multi party notifications if programs has multiple parties', async () => {
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [],
        applicantTypes: [
          { applicantTypeId: 'mockFirstPartyApplicantTypeId' },
          { applicantTypeId: 'mockThirdPartyApplicantTypeId' },
        ],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: { channels: { email: true, sms: true } },
      });
      findTemplatesFn.mockResolvedValueOnce(
        DefaultNotificationType.flatMap((type) => [
          { id: type, type, channel: 'email', content: {} },
          { id: type, type, channel: 'sms', content: {} },
        ]),
      );
      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: DefaultNotificationType,
        channels: ['email', 'sms'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'MultipartyInvitation',
            channelConfigurations: [
              { enabled: true, channel: 'email', template: expect.any(Object) },
              { enabled: true, channel: 'sms', template: expect.any(Object) },
            ],
          },
        ]),
      );
    });
    it('should only request default notifications if no program features are enabled', async () => {
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [{ enabled: false, feature: { name: FeatureName.PaymentsClaimFunds } }],
        applicantTypes: [
          { applicantTypeId: 'mockFirstPartyApplicantTypeId' },
          { applicantTypeId: 'mockThirdPartyApplicantTypeId' },
        ],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce(undefined);
      await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: DefaultNotificationType,
        channels: ['email'],
      });
    });
    it('should default to email if no communication channels are configured', async () => {
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [],
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce(undefined);
      findTemplatesFn.mockResolvedValueOnce(
        DefaultNotificationType.map((type) => ({
          id: type,
          type,
          channel: 'email',
          content: {},
        })),
      );
      const notifications = await mockService.getProgramNotifications('mockProgramId');

      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: [
          'ApplicationDenied',
          'ApplicationIncomplete',
          'ApplicationSubmitted',
          'ApplicationWithdrawn',
          'CaseComment',
        ],
        channels: ['email'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'ApplicationDenied',
            channelConfigurations: [
              { enabled: true, channel: 'email', template: expect.any(Object) },
            ],
          },
        ]),
      );
    });
    it('should return an empty array if both communication channels are disabled', async () => {
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: { channels: { email: false, sms: false } },
      });
      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).not.toHaveBeenCalled();
      expect(notifications).toEqual([]);
    });
    it('should flag templates as enabled based on partner communication config', async () => {
      const notificationTypes = [
        'ApplicationDenied',
        'ApplicationIncomplete',
        'ApplicationSubmitted',
        'ApplicationWithdrawn',
        'CaseComment',
        'PaymentsClaimFunds',
        'PaymentsFundsClaimed',
        'PaymentsApplicantPaymentReset',
      ];
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: {
          channels: { email: true, sms: false },
          email: {
            ApplicationDenied: false,
          },
        },
      });
      findTemplatesFn.mockResolvedValueOnce(
        notificationTypes.map((type) => ({ id: type, type, channel: 'email', content: {} })),
      );

      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: notificationTypes,
        channels: ['email'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'ApplicationDenied',
            channelConfigurations: [
              { enabled: false, channel: 'email', template: expect.any(Object) },
            ],
          },
        ]),
      );
    });
    it('should flag templates as enabled true if there is no customization', async () => {
      const notificationTypes = [
        'ApplicationDenied',
        'ApplicationIncomplete',
        'ApplicationSubmitted',
        'ApplicationWithdrawn',
        'CaseComment',
        'PaymentsClaimFunds',
        'PaymentsFundsClaimed',
        'PaymentsApplicantPaymentReset',
      ];
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: {
          channels: { email: true, sms: false },
        },
      });
      findTemplatesFn.mockResolvedValueOnce(
        notificationTypes.map((type) => ({ id: type, type, channel: 'email', content: {} })),
      );

      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: notificationTypes,
        channels: ['email'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'ApplicationDenied',
            channelConfigurations: [
              { enabled: true, channel: 'email', template: expect.any(Object) },
            ],
          },
        ]),
      );
    });
    it('should return an empty configuration array if no templates are found', async () => {
      findProgramFn.mockResolvedValueOnce({
        partnerId: 'mockPartnerId',
        features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        applicantTypes: [{ applicantTypeId: 'mockFirstPartyApplicantTypeId' }],
      });
      findCommunicationChannelsConfigByPartnerIdFn.mockResolvedValueOnce({
        config: { channels: { email: true, sms: true } },
      });
      findTemplatesFn.mockResolvedValueOnce([]);
      const notifications = await mockService.getProgramNotifications('mockProgramId');
      expect(findProgramFn).toHaveBeenCalledWith({
        where: { id: 'mockProgramId' },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      expect(findTemplatesFn).toHaveBeenCalledWith({
        partnerId: 'mockPartnerId',
        programId: 'mockProgramId',
        types: [
          'ApplicationDenied',
          'ApplicationIncomplete',
          'ApplicationSubmitted',
          'ApplicationWithdrawn',
          'CaseComment',
          'PaymentsClaimFunds',
          'PaymentsFundsClaimed',
          'PaymentsApplicantPaymentReset',
        ],
        channels: ['email', 'sms'],
      });
      expect(notifications).toEqual(
        expect.arrayContaining([
          {
            type: 'ApplicationDenied',
            channelConfigurations: [],
          },
        ]),
      );
    });
  });
});

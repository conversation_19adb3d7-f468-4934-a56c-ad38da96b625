import { ActiveLabel } from '@bybeam/doctopus-types';
import {
  CaseCounts,
  CaseStatus,
  CaseStatusCounts,
  CommunicationChannels,
  CommunicationChannelsConfig,
  CursorPagination,
  DefaultNotificationType,
  MultiPartyNotificationType,
  MutationResponse,
  NotificationConfig,
  NotificationTemplate,
  NotificationType,
  NotificationTypeFeature,
  Page,
  PaymentStatus,
  PlatformNotificationType,
  Program,
  ProgramFunds,
  ProgramStats,
  UpdateProgramsInput,
  WORKFLOW_STAGE_NOTIFICATION_TYPES,
  Workflow,
  WorkflowAction,
  obligatedPaymentStatuses,
  successfulPaymentStatuses,
} from '@bybeam/platform-types';
import { AdminToken, LoginToken } from '@platform-api/@types/authentication.js';
import {
  LabelRepository,
  NotificationTemplateRepository,
  ProgramRepository,
} from '@platform-api/@types/repositories/index.js';
import { StatusCodes } from 'http-status-codes';
import mnemonist from 'mnemonist';
import { In } from 'typeorm';
import { ProgramService as IProgramService } from '../../@types/services.js';
import { sum } from '../../utils/array.js';
import { logger } from '../../utils/logger.js';
import * as response from '../../utils/response.js';
import { omit } from '../../utils/set.js';
import QueryServiceImplementation from '../QueryService.js';
import ConfigurationService from '../config/ConfigurationService.js';
import ManyToOneDataLoader from '../utils/ManyToOneDataLoader.js';
import WorkflowEventService from '../workflowEvent/WorkflowEventService.js';
import { ModelArmorClient } from '@google-cloud/modelarmor';

const { MultiMap } = mnemonist;
export default class ProgramService
  extends QueryServiceImplementation<Program, ProgramRepository>
  implements IProgramService
{
  private labelRepository: LabelRepository;
  private workflowEventService: WorkflowEventService;
  private notificationTemplateRepository: NotificationTemplateRepository;
  private configService: ConfigurationService;
  private modelArmorClient: ModelArmorClient;

  constructor({
    programRepository,
    labelRepository,
    notificationTemplateRepository,
    workflowEventService,
    configService,
  }: {
    programRepository: ProgramRepository;
    labelRepository: LabelRepository;
    notificationTemplateRepository: NotificationTemplateRepository;
    workflowEventService: WorkflowEventService;
    configService: ConfigurationService;
  }) {
    super(programRepository);
    this.labelRepository = labelRepository;
    this.workflowEventService = workflowEventService;
    this.notificationTemplateRepository = notificationTemplateRepository;
    this.configService = configService;
    this.modelArmorClient = new ModelArmorClient({
      apiEndpoint: `modelarmor.${process.env.REGION}.rep.googleapis.com`,
    });
  }

  private readonly partnerToProgramDataloader = new ManyToOneDataLoader(
    (program: Program) => program.partnerId ?? '',
    async (ids: string[]) => this.repository.findBy({ partnerId: In(ids) }),
  );

  public async findAndCount(
    token: LoginToken,
    filter?: Pick<Program, 'id'>,
    pagination?: CursorPagination,
  ): Promise<Page<Program>> {
    return this.findAndCountInternal({ ...filter, partnerId: token.partnerId }, pagination);
  }

  public async findByPartnerId(partnerId: string): Promise<Program[]> {
    return this.partnerToProgramDataloader.load(partnerId);
  }

  public async update(
    token: AdminToken,
    inputPrograms: UpdateProgramsInput,
  ): Promise<MutationResponse<Program[]>> {
    try {
      const programs = await this.findByIds(inputPrograms.map((program) => program.id));

      if (programs.length !== inputPrograms.length) {
        logger.error(
          { input: inputPrograms },
          `${this.constructor.name}.update: One or more programs does not exist`,
        );

        return response.error({
          errors: ['One or more programs does not exist'],
          statusCode: StatusCodes.NOT_FOUND,
        });
      }

      // Map rulesEvaluationEnabled -> programs.config.rulesEvaluationEnabled so we don't
      // require a DB column. Merge with existing config per-program.
      const mappedInputs = await Promise.all(
        inputPrograms.map(async (inputProgram) => {
          const existing = programs.find((ex) => ex.id === inputProgram.id);
          const updatedProgram = omit(inputProgram, [
            'rulesEvaluationEnabled',
            'applicationReviewFields',
          ]) as Partial<Program>;

          // Build up config object with any updates
          const hasRulesToggle = Object.prototype.hasOwnProperty.call(
            inputProgram,
            'rulesEvaluationEnabled',
          );
          const hasReviewFields = Object.prototype.hasOwnProperty.call(
            inputProgram,
            'applicationReviewFields',
          );
          const hasProgramContext = Object.prototype.hasOwnProperty.call(
            inputProgram,
            'programContext',
          );

          if (existing?.config || hasRulesToggle || hasReviewFields || hasProgramContext) {
            updatedProgram.config = {
              ...(existing?.config ?? {}),
              ...(hasRulesToggle && {
                rulesEvaluationEnabled: inputProgram.rulesEvaluationEnabled === true,
              }),
              ...(hasReviewFields && {
                applicationReviewFields: inputProgram.applicationReviewFields,
              }),
              ...(inputProgram.programContext && {
                programContext: {
                  ...existing?.config?.programContext,
                  ...inputProgram.programContext,
                  description: await this.sanitizeUserPromptInput(
                    inputProgram.programContext.description,
                    token,
                  ),
                },
              }),
            } as Program['config'];
          }
          return updatedProgram;
        }),
      );

      await this.repository.save(mappedInputs);

      for (const program of inputPrograms) this.resetCache(program.id);

      await this.workflowEventService.create(
        token,
        inputPrograms.map((program) => ({
          entityId: program.id,
          entityType: 'program',
          action: WorkflowAction.ProgramUpdate,
          previousValue: JSON.stringify(programs.find((p) => p.id === program.id)),
          newValue: JSON.stringify(omit({ ...program }, ['id'])),
        })),
      );

      return response.success({
        record: await this.findByIds(inputPrograms.map((program) => program.id)),
      });
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}.update: unexpected error during update >`);
      return response.error();
    }
  }

  private readonly fundStatsToProgramDataloader = new ManyToOneDataLoader(
    (programFund: ProgramFunds) => programFund.id,
    (ids: string[]) => this.repository.getStatsByFund(ids, Object.values(PaymentStatus)),
  );

  private readonly caseCountsToProgramDataloader = new ManyToOneDataLoader(
    (caseCount: CaseStatusCounts) => caseCount.id,
    (ids: string[]) => this.repository.getCaseCounts(ids),
  );

  public resetProgramFundsStatsCache(programId: string): void {
    this.fundStatsToProgramDataloader.clear(programId);
  }

  public resetCaseCountsCache(programId: string): void {
    this.caseCountsToProgramDataloader.clear(programId);
  }

  public async calculateStats(programId: string): Promise<ProgramStats> {
    const programFundsResult = await this.fundStatsToProgramDataloader.load(programId);
    const caseCounts = await this.caseCountsToProgramDataloader.load(programId);

    const statsByFundId = new MultiMap<string, ProgramFunds>();
    for (const programFund of programFundsResult)
      statsByFundId.set(programFund.fundId, programFund);
    const fundIds = [...(statsByFundId.keys() ?? [])];

    return {
      id: programId,
      awardedBalance: sum(
        programFundsResult
          .filter(({ status }) => successfulPaymentStatuses.includes(status))
          .map(({ total }) => Number(total ?? 0)),
      ),
      caseCounts: {
        ...Object.fromEntries(
          Object.values(CaseStatus).map((caseStatus) => [
            caseStatus,
            Number(caseCounts.find(({ status }) => caseStatus === status)?.count ?? 0),
          ]),
        ),
        All: sum(caseCounts.map(({ count }) => Number(count ?? 0))),
      } as CaseCounts,
      programFundStats: fundIds.map((fundId) => {
        const fundStats = statsByFundId.get(fundId) ?? [];
        return {
          programId,
          fundId,
          awardedBalance: sum(
            fundStats
              .filter(({ status }) => successfulPaymentStatuses.includes(status))
              .map(({ total }) => Number(total ?? 0)),
          ),
          obligatedBalance: sum(
            fundStats
              .filter(({ status }) => obligatedPaymentStatuses.includes(status))
              .map(({ total }) => Number(total ?? 0)),
          ),
          paymentCount: sum(fundStats.map(({ count }) => Number(count ?? 0))),
        };
      }),
    };
  }

  public async save(program: Partial<Program>): Promise<Program> {
    return this.repository.save(program);
  }

  // TODO override with program_label_category values if present
  public async getDocumentLabels(programId: string): Promise<ActiveLabel[]> {
    return this.labelRepository.find({ relations: ['modelVersion'] });
  }

  /**
   * Returns a collection synthetic "WorkflowStage" entities for the program.
   * This is based on the workflow statuses defined in the CaseStatus enum.
   * Each stage contains a collection of "WorkflowNotification" entities, which contain the email templates defined for that stage.
   * Program-specific templates take precedence over partner-specific templates, which take precedence over global templates.
   */
  public async getProgramWorkflow(programId: string, partnerId: string): Promise<Workflow> {
    const templates = await this.notificationTemplateRepository.findByProgramId({
      partnerId,
      programId,
    });

    return {
      workflowStages: Object.values(CaseStatus).map((status) => {
        const notificationType = WORKFLOW_STAGE_NOTIFICATION_TYPES[status];
        const templatesForNotificationType = templates?.filter(
          ({ type }) => type === notificationType,
        );
        return {
          status: status,
          workflowNotifications: templatesForNotificationType.map(
            (template: NotificationTemplate) => ({
              template,
            }),
          ),
        };
      }),
    };
  }

  private getProgramNotificationTypes(program: Program): NotificationType[] {
    let notificationTypes = [...DefaultNotificationType];

    if (program.features?.length) {
      const programFeatures = program.features
        .filter((programFeature) => programFeature?.enabled)
        .map((ProgramFeature) => ProgramFeature?.feature?.name);

      if (programFeatures?.length) {
        const featureSpecificTypes = programFeatures.flatMap(
          (feature) => NotificationTypeFeature[feature] ?? [],
        );
        notificationTypes.push(...(featureSpecificTypes ?? []));
      }
    }

    if (program.partner?.features?.length) {
      const partnerFeatures = program.partner.features
        .filter((partnerFeature) => partnerFeature?.enabled)
        .map((partnerFeature) => partnerFeature?.feature?.name);

      if (partnerFeatures?.length) {
        const featureSpecificTypes = partnerFeatures.flatMap(
          (feature) => NotificationTypeFeature[feature] ?? [],
        );
        notificationTypes.push(...(featureSpecificTypes ?? []));
      }
    }

    // If the program is a single applicant type, remove the multiparty types
    if ((program.applicantTypes ?? []).length <= 1) {
      notificationTypes = notificationTypes.filter(
        (type: NotificationType) => !MultiPartyNotificationType.includes(type),
      );
    }

    return [...new Set(notificationTypes)];
  }

  private getOrThrowCommunicationChannels(
    config: CommunicationChannelsConfig | undefined,
  ): CommunicationChannels[] {
    // Get all channels that are enabled
    // Special case: EMAIL is enabled by default unless explicitly disabled
    const enabledChannels = Object.values(CommunicationChannels).filter((channel) =>
      channel === CommunicationChannels.EMAIL
        ? config?.channels?.[channel] !== false
        : config?.channels?.[channel] === true,
    );
    if (enabledChannels.length === 0) {
      throw new Error('No communication channels found');
    }
    return enabledChannels;
  }

  private async sanitizeUserPromptInput(text: string, token: LoginToken): Promise<string> {
    const request = {
      name: `projects/${process.env.GOOGLE_PROJECT_ID}/locations/${process.env.REGION}/templates/${process.env.MODEL_ARMOR_TEMPLATE_ID}`,
      userPromptData: {
        text,
      },
    };
    const [response] = await this.modelArmorClient.sanitizeUserPrompt(request);

    if (response.sanitizationResult?.filterMatchState?.toString() !== 'NO_MATCH_FOUND') {
      throw new Error(`Sanitization failed for user: ${token.userId}`);
    }
    return text;
  }

  public async getProgramNotifications(programId: string): Promise<NotificationConfig[]> {
    try {
      const program = await this.findWithOptions({
        where: { id: programId },
        relations: ['features', 'applicantTypes', 'partner', 'partner.features'],
      });
      if (!program) throw new Error('Program not found');

      const communicationConfig = (
        await this.configService.findCommunicationChannelsConfigByPartnerId({
          partnerId: program.partnerId,
        })
      )?.config;

      const channels = this.getOrThrowCommunicationChannels(communicationConfig);
      const types = this.getProgramNotificationTypes(program);

      const templates = await this.notificationTemplateRepository.findByProgramId({
        partnerId: program.partnerId,
        programId,
        channels,
        types,
      });

      // Enhancement: this should be done in a util function
      return types.map((type) => ({
        type: type as PlatformNotificationType,
        channelConfigurations: templates
          .filter((template) => template.type === type)
          .map((template) => ({
            channel: template.channel,
            enabled: communicationConfig?.[template.channel]?.[template.type] ?? true,
            template,
          })),
      }));
    } catch (error) {
      logger.warn(
        { error },
        `${this.constructor.name}.getProgramNotifications: unexpected error >`,
      );
      return [];
    }
  }

  public resetPartnerProgramsCache(partnerId: string): void {
    this.partnerToProgramDataloader.clear(partnerId);
  }
}

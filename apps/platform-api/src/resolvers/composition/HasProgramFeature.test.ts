import { FeatureName } from '@bybeam/platform-types';
import { programFeature } from '@platform-api-test/mocks.js';
import { AuthenticationError } from '@platform-api/@types/errors.js';
import { Context } from '@platform-api/@types/graphql.js';
import { Services } from '@platform-api/@types/services.js';
import { GraphQLResolveInfo } from 'graphql';
import HasProgramFeature from './HasProgramFeature.js';

describe('HasProgramFeature', () => {
  const nextMock = vi.fn();

  it('proceeds if feature enabled for the program', async () => {
    const { service: programFeatureService } = programFeature({ enabled: true });
    await HasProgramFeature(
      vi.fn().mockResolvedValueOnce('mockProgramId'),
      FeatureName.PaymentsClaimFunds,
    )(nextMock)(
      {},
      {},
      {
        services: {
          programFeatures: programFeatureService,
        } as unknown as Services,
      } as Context,
      {} as GraphQLResolveInfo,
    );
    expect(nextMock).toHaveBeenCalled();
  });

  it('errors if feature is not enabled', async () => {
    const { service: programFeatureService } = programFeature({ enabled: false });
    try {
      await HasProgramFeature(
        vi.fn().mockResolvedValueOnce('mockProgramId'),
        FeatureName.PaymentsClaimFunds,
      )(nextMock)(
        {},
        {},
        {
          services: {
            programFeatures: programFeatureService,
          } as unknown as Services,
        } as Context,
        {} as GraphQLResolveInfo,
      );
      expect.fail('Should have thrown');
    } catch (error) {
      expect(error).toBeInstanceOf(AuthenticationError);
      expect((error as Error).message).toBe('Feature Payments: Claim Funds is not enabled');
    }
    expect(nextMock).not.toHaveBeenCalled();
  });
});

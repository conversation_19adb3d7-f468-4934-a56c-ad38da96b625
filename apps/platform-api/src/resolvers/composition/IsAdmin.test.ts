import { Permission, Role } from '@bybeam/identity-client/types';
import { CustomHeaders } from '@bybeam/platform-types';
import { AuthorizationError } from '@platform-api/@types/errors.js';
import { Context } from '@platform-api/@types/graphql.js';
import { Services } from '@platform-api/@types/services.js';
import { Response } from 'express';
import { GraphQLResolveInfo } from 'graphql';
import IsAdmin from './IsAdmin.js';

describe('IsAdmin', () => {
  const nextMock = vi.fn();

  describe('when the context has a valid admin token', () => {
    describe('when the admin is active', () => {
      it('proceeds to next step', async () => {
        await IsAdmin()(nextMock)(
          {},
          {},
          {
            token: { userId: 'mockUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            } as unknown as Services,
          } as Context,
          {} as GraphQLResolveInfo,
        );
        expect(nextMock).toHaveBeenCalled();
      });
    });

    describe('when the permission check is valid', () => {
      it('proceeds to next step', async () => {
        await IsAdmin()(nextMock)(
          {},
          {},
          {
            token: { userId: 'mockUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            } as unknown as Services,
            response: { locals: { headers: { [CustomHeaders.GCIPToken]: 'mockGCIPToken' } } },
          } as unknown as Context,
          {} as GraphQLResolveInfo,
        );
        expect(nextMock).toHaveBeenCalled();
      });
    });
    describe('when the permission check is invalid', () => {
      it('throws an informative error', async () => {
        try {
          await IsAdmin()(nextMock)(
            {},
            {},
            {
              token: { userId: 'mockUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: {
                  checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }),
                },
              } as unknown as Services,
              response: { locals: { headers: { [CustomHeaders.GCIPToken]: 'mockGCIPToken' } } },
            } as unknown as Context,
            {} as GraphQLResolveInfo,
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as Error).message).toBe('User does not have partner portal access for mockPartnerId');
        }
      });
    });
    describe('when the resolver passes a permission argument', () => {
      it('uses the argument as the relation', async () => {
        const checkPermissionFn = vi.fn();
        await IsAdmin(Role.ViewOnly)(nextMock)(
          {},
          {},
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              partnerId: 'mockPartnerId',
              adminId: 'mockAdminId',
            },
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: {
                checkPermission: checkPermissionFn.mockResolvedValueOnce({ canAccess: true }),
              },
            } as unknown as Services,
            response: { locals: { headers: { [CustomHeaders.GCIPToken]: 'mockGCIPToken' } } },
          } as unknown as Context,
          {} as GraphQLResolveInfo,
        );
        expect(checkPermissionFn).toHaveBeenCalledWith({
          action: Role.ViewOnly,
          resource: { objectId: 'mockPartnerId', objectType: 'ORGANIZATION' },
          subject: { objectId: 'mockIdentityUserId', objectType: 'USER' },
        });
        expect(nextMock).toHaveBeenCalled();
      });
    });
    describe('when the resolver does not pass a permission argument', () => {
      it('uses PartnerPortal as the permission', async () => {
        const checkPermissionFn = vi.fn();
        await IsAdmin()(nextMock)(
          {},
          {},
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              partnerId: 'mockPartnerId',
              adminId: 'mockAdminId',
            },
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: {
                checkPermission: checkPermissionFn.mockResolvedValueOnce({ canAccess: true }),
              },
            } as unknown as Services,
            response: { locals: { headers: { [CustomHeaders.GCIPToken]: 'mockGCIPToken' } } },
          } as unknown as Context,
          {} as GraphQLResolveInfo,
        );
        expect(checkPermissionFn).toHaveBeenCalledWith({
          action: Permission.PartnerPortal,
          resource: { objectId: 'mockPartnerId', objectType: 'ORGANIZATION' },
          subject: { objectId: 'mockIdentityUserId', objectType: 'USER' },
        });
        expect(nextMock).toHaveBeenCalled();
      });
    });

    describe('when the admin was deleted', () => {
      it('logs the user out and throws an exception', async () => {
        const mockLogout = vi.fn();
        const mockResponse = 'mockResponse' as unknown as Response;
        try {
          await IsAdmin()(nextMock)(
            {},
            {},
            {
              token: { userId: 'mockUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
              services: {
                admins: {
                  findById: vi
                    .fn()
                    .mockReturnValueOnce({ id: 'mockAdminId', archivedAt: new Date() }),
                },
                identity: {
                  endSession: mockLogout,
                  checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }),
                },
              } as unknown as Services,
              response: mockResponse,
            } as Context,
            {} as GraphQLResolveInfo,
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as Error).message).toBe('Admin was deactivated');
        }
        expect(nextMock).not.toHaveBeenCalled();
        expect(mockLogout).toHaveBeenCalledWith({
          res: mockResponse,
        });
      });
    });
  });

  describe('when context has an admin token but it is malformed (empty)', () => {
    it('throws an exception', async () => {
      await expect(
        IsAdmin()(nextMock)(
          {},
          {},
          {
            token: { userId: 'mockUserId', partnerId: 'mockPartnerId', adminId: '' },
          } as Context,
          {} as GraphQLResolveInfo,
        ),
      ).rejects.toThrow(AuthorizationError);
      expect(nextMock).not.toHaveBeenCalled();
    });
  });

  describe('when context has a token but it does not belong to an admin', () => {
    it('throws an exception', async () => {
      await expect(
        IsAdmin()(nextMock)(
          {},
          {},
          {
            token: { userId: 'mockUserId', partnerId: 'mockPartnerId' },
          } as Context,
          {} as GraphQLResolveInfo,
        ),
      ).rejects.toThrow(AuthorizationError);
      expect(nextMock).not.toHaveBeenCalled();
    });
  });

  describe('when context has no token', () => {
    it('throws an exception', async () => {
      await expect(
        IsAdmin()(nextMock)({}, {}, {} as Context, {} as GraphQLResolveInfo),
      ).rejects.toThrow(AuthorizationError);
      expect(nextMock).not.toHaveBeenCalled();
    });
  });
});

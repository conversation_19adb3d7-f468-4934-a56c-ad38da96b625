import { AuthenticationError } from '@platform-api/@types/errors.js';
import { Context } from '@platform-api/@types/graphql.js';
import { Services } from '@platform-api/@types/services.js';
import { GraphQLResolveInfo } from 'graphql';
import RecaptchaProtected from './RecaptchaProtected.js';

describe('RecaptchaProtected', () => {
  const nextMock = vi.fn();

  describe('when the recaptcha service returns a score', () => {
    it('proceeds to next step', async () => {
      await RecaptchaProtected('LOGIN')(nextMock)(
        {},
        {},
        {
          services: {
            identity: {
              recaptcha: vi.fn().mockReturnValueOnce(1),
            },
          } as unknown as Services,
        } as Context,
        {} as GraphQLResolveInfo,
      );
      expect(nextMock).toHaveBeenCalled();
    });
  });
  describe('when the recaptcha service returns undefined', () => {
    it('throws an authentication error', async () => {
      try {
        await RecaptchaProtected('LOGIN')(nextMock)(
          {},
          {},
          {
            services: {
              identity: {
                recaptcha: vi.fn().mockReturnValueOnce(undefined),
              },
            } as unknown as Services,
          } as Context,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthenticationError);
        expect((error as Error).message).toBe('Does not pass recaptcha test');
      }
      expect(nextMock).not.toHaveBeenCalled();
    });
  });
});

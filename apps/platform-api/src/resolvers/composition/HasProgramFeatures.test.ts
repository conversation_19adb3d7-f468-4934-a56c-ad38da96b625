import { FeatureName } from '@bybeam/platform-types';
import { programFeature } from '@platform-api-test/mocks.js';
import { AuthorizationError } from '@platform-api/@types/errors.js';
import { Context } from '@platform-api/@types/graphql.js';
import { Services } from '@platform-api/@types/services.js';
import { GraphQLResolveInfo } from 'graphql';
import HasProgramFeatures from './HasProgramFeatures.js';

describe('HasProgramFeatures', () => {
  const nextMock = vi.fn();

  it('proceeds if features enabled for the program', async () => {
    const { service: programFeatureService } = programFeature({ enabled: true });
    await HasProgramFeatures(vi.fn().mockResolvedValueOnce('mockProgramId'), [
      FeatureName.PaymentsClaimFunds,
    ])(nextMock)(
      {},
      {},
      {
        services: {
          programFeatures: programFeatureService,
        } as unknown as Services,
      } as Context,
      {} as GraphQLResolveInfo,
    );
    expect(nextMock).toHaveBeenCalled();
  });

  it('errors if features are not enabled', async () => {
    const { service: programFeatureService } = programFeature({ enabled: false });
    try {
      await HasProgramFeatures(vi.fn().mockResolvedValueOnce('mockProgramId'), [
        FeatureName.PaymentsClaimFunds,
      ])(nextMock)(
        {},
        {},
        {
          services: {
            programFeatures: programFeatureService,
          } as unknown as Services,
        } as Context,
        {} as GraphQLResolveInfo,
      );
      expect.fail('Should have thrown');
    } catch (error) {
      expect(error).toBeInstanceOf(AuthorizationError);
      expect((error as Error).message).toBe('All features Payments: Claim Funds are not enabled');
    }
    expect(nextMock).not.toHaveBeenCalled();
  });
});

import { ReadOnlyError } from '@platform-api/@types/errors.js';
import { Context } from '@platform-api/@types/graphql.js';
import { GraphQLResolveInfo } from 'graphql';
import Freeze from './Freeze.js';

describe('Freeze', () => {
  const nextMock = vi.fn();

  describe('when READ_ONLY is not set', () => {
    it('proceeds to next step', () => {
      Freeze()(nextMock)({}, {}, {} as Context, {} as GraphQLResolveInfo);
      expect(nextMock).toHaveBeenCalled();
    });
  });

  describe('when READ_ONLY is true', () => {
    it('throws a ReadOnly error', async () => {
      process.env.READ_ONLY = 'true';
      await expect(
        Freeze()(nextMock)({}, {}, {} as Context, {} as GraphQLResolveInfo),
      ).rejects.toThrow(ReadOnlyError);
      expect(nextMock).not.toHaveBeenCalled();
    });
  });
});

import { PredictionFeedback } from '@bybeam/platform-types';
import { composeResolvers } from '@graphql-tools/resolvers-composition';
import { AuthorizationError } from '@platform-api/@types/errors.js';
import { AdminContext } from '@platform-api/@types/graphql.js';
import { GraphQLResolveInfo } from 'graphql';
import { composition, resolvers } from './feedback.js';

describe('feedback', () => {
  describe('resolvers', () => {
    describe('admin', () => {
      const {
        PredictionFeedback: { admin: composedResolver },
      } = composeResolvers(
        { PredictionFeedback: { admin: resolvers.admin } },
        { PredictionFeedback: { admin: composition.admin } },
      );

      it('should throw if there is no authenticated user', async () => {
        const getAdminFn = vi.fn();
        try {
          await composedResolver(
            { id: 'mockFeedbackId' } as PredictionFeedback,
            {},
            {
              services: {
                admins: { findById: getAdminFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as Error).message).toBe('User is not an admin');
        }
      });

      it('should throw if the authenticated is not an admin', async () => {
        const getAdminFn = vi.fn();
        try {
          await composedResolver(
            { id: 'mockFeedbackId' } as PredictionFeedback,
            {},
            {
              token: { userId: 'mockUserId' },
              services: {
                admins: { findById: getAdminFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as Error).message).toBe('User is not an admin');
        }
      });

      it('should return the existing admin if it is present', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId', archivedAt: null });
        const result = await composedResolver(
          { id: 'mockFeedbackId', admin: { id: 'mockExistingAdminId' } } as PredictionFeedback,
          {},
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockExistingAdminId' });
      });

      it('should fetch the admin if it is not present', async () => {
        const getAdminFn = vi
          .fn()
          .mockResolvedValueOnce({ id: 'mockAdminId', archivedAt: null }) // Once for token check
          .mockResolvedValueOnce({ id: 'mockExistingAdminId' }); // Once for feedback admin retrieval
        const result = await composedResolver(
          { id: 'mockFeedbackId' } as PredictionFeedback,
          {},
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockExistingAdminId' });
      });
    });
    describe('preferredLabel', () => {
      const {
        PredictionFeedback: { preferredLabel: composedResolver },
      } = composeResolvers(
        { PredictionFeedback: { preferredLabel: resolvers.preferredLabel } },
        { PredictionFeedback: { preferredLabel: composition.preferredLabel } },
      );

      it('should throw if there is no authenticated user', async () => {
        const getAdminFn = vi.fn();
        try {
          await composedResolver(
            { id: 'mockFeedbackId' } as PredictionFeedback,
            {},
            {
              services: {
                admins: { findById: getAdminFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as Error).message).toBe('User is not an admin');
        }
      });

      it('should throw if the authenticated is not an admin', async () => {
        const getAdminFn = vi.fn();
        try {
          await composedResolver(
            { id: 'mockFeedbackId' } as PredictionFeedback,
            {},
            {
              token: { userId: 'mockUserId' },
              services: {
                admins: { findById: getAdminFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          );
          expect.fail('Should have thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(AuthorizationError);
          expect((error as Error).message).toBe('User is not an admin');
        }
      });

      it('should return the existing preferredLabel if it is present', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
        const result = await composedResolver(
          { id: 'mockFeedbackId', preferredLabel: { id: 'mockLabelId' } } as PredictionFeedback,
          {},
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockLabelId' });
      });

      it('should fetch the preferredLabel if it is not present and there is a preferredLabelId', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
        const getPreferredLabelFn = vi.fn().mockResolvedValueOnce({ id: 'mockLabelId' });
        const result = await composedResolver(
          { id: 'mockFeedbackId', preferredLabelId: 'mockLabelId' } as PredictionFeedback,
          {},
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              activeLabels: { findById: getPreferredLabelFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockLabelId' });
      });

      it('should return nothing if there is no preferredLabelId', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
        const getPreferredLabelFn = vi.fn().mockResolvedValueOnce({ id: 'mockLabelId' });
        const result = await composedResolver(
          { id: 'mockFeedbackId' } as PredictionFeedback,
          {},
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              activeLabels: { findById: getPreferredLabelFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(result).toBeUndefined();
        expect(getPreferredLabelFn).not.toBeCalled();
      });
    });
  });
});

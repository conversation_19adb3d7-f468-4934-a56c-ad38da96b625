import {
  CreateApplicationInput,
  ProgramStatus,
  UpdateApplicationInput,
} from '@bybeam/platform-types';
import { composeResolvers } from '@graphql-tools/resolvers-composition';
import { mockAdminToken, mockLoginToken } from '@platform-api-test/mocks.js';
import { AuthenticationError, AuthorizationError } from '@platform-api/@types/errors.js';
import { AdminContext, AuthenticatedContext } from '@platform-api/@types/graphql.js';
import { GraphQLResolveInfo } from 'graphql';
import { composition, mutations } from './applications.js';

describe('Application', () => {
  describe('addVersion', () => {
    const {
      Applications: { addVersion: composedResolver },
    } = composeResolvers(
      { Applications: { addVersion: mutations.addVersion } },
      { Applications: { addVersion: composition.addVersion } },
    );

    const input = { id: 'mockApplicationId' };

    it('throws if the user is not an admin', async () => {
      try {
        await composedResolver(
          undefined,
          { input },
          {} as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('User is not an admin');
      }
    });
    it('throws if the application is not in the same partner', async () => {
      try {
        await composedResolver(
          undefined,
          { input },
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              adminId: 'mockAdminId',
              partnerId: 'mockPartnerId',
            },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              applications: {
                findWithOptions: vi.fn().mockResolvedValueOnce({ partnerId: 'wrongPartnerId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe(
          'User is trying to take action on an entity in a different partner',
        );
      }
    });
    it('throws if the user does not have EDIT permission on the application', async () => {
      try {
        await composedResolver(
          undefined,
          { input },
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              adminId: 'mockAdminId',
              partnerId: 'mockPartnerId',
            },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              applications: {
                findWithOptions: vi
                  .fn()
                  .mockResolvedValueOnce({ case: { program: { partnerId: 'mockPartnerId' } } }),
              },
              identity: {
                checkPermission: vi
                  .fn()
                  .mockResolvedValueOnce({ canAccess: true })
                  .mockResolvedValueOnce({ canAccess: false }),
              },
              partners: {
                hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
              },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('Does not have access');
      }
    });
    it('sends the request to the operation', async () => {
      const runOperationFn = vi.fn().mockResolvedValueOnce({ status: 200 });
      const result = await composedResolver(
        undefined,
        { input },
        {
          token: {
            userId: 'mockUserId',
            identityUserId: 'mockIdentityUserId',
            adminId: 'mockAdminId',
            partnerId: 'mockPartnerId',
          },
          operations: { applications: { addVersion: { run: runOperationFn } } },
          services: {
            admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
            applications: {
              findWithOptions: vi
                .fn()
                .mockResolvedValueOnce({ case: { program: { partnerId: 'mockPartnerId' } } }),
            },
            identity: {
              checkPermission: vi
                .fn()
                .mockResolvedValueOnce({ canAccess: true })
                .mockResolvedValueOnce({ canAccess: true }),
            },
            partners: {
              hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
            },
          },
        } as unknown as AdminContext,
        {} as GraphQLResolveInfo,
      );
      expect(result).toEqual({ status: 200 });
      expect(runOperationFn).toHaveBeenCalledWith(
        {
          userId: 'mockUserId',
          identityUserId: 'mockIdentityUserId',
          adminId: 'mockAdminId',
          partnerId: 'mockPartnerId',
        },
        input,
      );
    });
  });
  describe('create', () => {
    const {
      Applications: { create: composedResolver },
    } = composeResolvers(
      { Applications: { create: mutations.create } },
      { Applications: { create: composition.create } },
    );

    it('throws if no user is authenticated', async () => {
      expect(() =>
        composedResolver(
          undefined,
          { input: { programId: 'mockProgramId' } as CreateApplicationInput },
          {} as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        ),
      ).toThrow(new AuthenticationError('No access token provided'));
    });

    it('throws if program is in a different partner', async () => {
      await expect(
        composedResolver(
          undefined,
          { input: { programId: 'mockProgramId' } as CreateApplicationInput },
          {
            token: mockLoginToken,
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: { findById: vi.fn().mockResolvedValue({ partnerId: 'otherPartnerId' }) },
            },
          } as unknown as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        ),
      ).rejects.toThrow(AuthorizationError);
    });

    it('throws if the program is not related to the partner', async () => {
      try {
        await composedResolver(
          undefined,
          { input: { programId: 'mockProgramId' } as CreateApplicationInput },
          {
            token: mockLoginToken,
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
              programs: { findById: vi.fn().mockResolvedValue({ partnerId: 'mockPartnerId' }) },
            },
          } as unknown as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('Does not have access');
      }
    });

    it('throws if program is Closed', async () => {
      try {
        await composedResolver(
          undefined,
          { input: { programId: 'mockProgramId' } as CreateApplicationInput },
          {
            token: mockLoginToken,
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: {
                findById: vi.fn().mockResolvedValue({
                  status: ProgramStatus.Closed,
                  partnerId: mockLoginToken.partnerId,
                }),
              },
            },
          } as unknown as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('Program is not open');
      }
    });

    it('throws if program is Referral-Only and user has no referral', async () => {
      try {
        await composedResolver(
          undefined,
          { input: { programId: 'mockProgramId' } as CreateApplicationInput },
          {
            token: mockLoginToken,
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: {
                findById: vi.fn().mockResolvedValue({
                  status: ProgramStatus.ReferralOnly,
                  partnerId: mockLoginToken.partnerId,
                }),
              },
              programReferrals: {
                findByUserId: vi.fn().mockResolvedValueOnce([{ programId: 'otherProgramId' }]),
              },
            },
          } as unknown as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('Program is not open');
      }
    });

    it('allows admins to create applications in Closed programs', async () => {
      const createFn = vi.fn();

      await composedResolver(
        undefined,
        { input: { programId: 'mockProgramId', userId: 'applicantUserId' } },
        {
          token: mockAdminToken,
          operations: { applications: { create: { run: createFn } } },
          services: {
            identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            programs: {
              findById: vi.fn().mockResolvedValue({
                status: ProgramStatus.Closed,
                partnerId: mockLoginToken.partnerId,
              }),
            },
          },
        } as unknown as AuthenticatedContext,
        {} as GraphQLResolveInfo,
      );

      expect(createFn).toHaveBeenCalledWith(mockAdminToken, {
        programId: 'mockProgramId',
        userId: 'applicantUserId',
      });
    });

    it('allows creating an application on an Open program', async () => {
      const createFn = vi.fn();

      await composedResolver(
        undefined,
        { input: { programId: 'mockProgramId' } as CreateApplicationInput },
        {
          token: mockLoginToken,
          operations: { applications: { create: { run: createFn } } },
          services: {
            applications: { create: createFn },
            identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            programs: {
              findById: vi.fn().mockResolvedValue({
                status: ProgramStatus.Open,
                partnerId: mockLoginToken.partnerId,
              }),
            },
          },
        } as unknown as AuthenticatedContext,
        {} as GraphQLResolveInfo,
      );

      expect(createFn).toHaveBeenCalledWith(mockLoginToken, {
        programId: 'mockProgramId',
        userId: mockLoginToken.userId,
      });
    });

    it('allows creating an application on a Referral-Only program if user has an application', async () => {
      const createFn = vi.fn();

      await composedResolver(
        undefined,
        { input: { programId: 'mockProgramId' } as CreateApplicationInput },
        {
          token: mockLoginToken,
          operations: { applications: { create: { run: createFn } } },
          services: {
            applications: { create: createFn },
            identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            programs: {
              findById: vi.fn().mockResolvedValue({
                status: ProgramStatus.ReferralOnly,
                partnerId: mockLoginToken.partnerId,
              }),
            },
            programReferrals: {
              findByUserId: vi.fn().mockResolvedValueOnce([{ programId: 'mockProgramId' }]),
            },
          },
        } as unknown as AuthenticatedContext,
        {} as GraphQLResolveInfo,
      );

      expect(createFn).toHaveBeenCalledWith(mockLoginToken, {
        programId: 'mockProgramId',
        userId: mockLoginToken.userId,
      });
    });
  });
  describe('submit', () => {
    const {
      Applications: { submit: composedResolver },
    } = composeResolvers(
      { Applications: { submit: mutations.submit } },
      { Applications: { submit: composition.submit } },
    );

    const input = { id: 'mockApplicationId' };

    it('throws if the user is not authenticated', async () => {
      expect(() =>
        composedResolver(
          undefined,
          { input },
          {} as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        ),
      ).toThrow(new AuthenticationError('No access token provided'));
    });
    it('throws if the user is not an admin or the owner', async () => {
      await expect(
        composedResolver(
          undefined,
          { input },
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              partnerId: 'mockPartnerId',
            },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              applications: {
                exists: vi.fn().mockResolvedValueOnce(false),
                findWithOptions: vi.fn().mockResolvedValueOnce({ partnerId: 'wrongPartnerId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        ),
      ).rejects.toThrow(
        new AuthenticationError('Only an admin or the entity owner can access this field'),
      );
    });
    it('throws if user is an applicant and the program is not open', async () => {
      try {
        await composedResolver(
          undefined,
          { input },
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              partnerId: 'mockPartnerId',
            },
            services: {
              applications: {
                exists: vi.fn().mockResolvedValueOnce(true),
                findWithOptions: vi
                  .fn()
                  .mockResolvedValueOnce({ cases: { program: { partnerId: 'wrongPartnerId' } } }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe(
          'Program is closed and application has not been submitted before',
        );
      }
    });
    it('sends the request to the operation', async () => {
      const runOperationFn = vi.fn().mockResolvedValueOnce({ status: 200 });
      const result = await composedResolver(
        undefined,
        { input },
        {
          token: {
            userId: 'mockUserId',
            identityUserId: 'mockIdentityUserId',
            partnerId: 'mockPartnerId',
          },
          operations: { applications: { submit: { run: runOperationFn } } },
          services: {
            applications: {
              exists: vi.fn().mockResolvedValueOnce(true),
              findWithOptions: vi.fn().mockResolvedValueOnce({
                case: { program: { status: ProgramStatus.Open, partnerId: 'mockPartnerId' } },
              }),
            },
            identity: {
              checkPermission: vi
                .fn()
                .mockResolvedValueOnce({ canAccess: true })
                .mockResolvedValueOnce({ canAccess: true }),
            },
            partners: {
              hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
            },
          },
        } as unknown as AdminContext,
        {} as GraphQLResolveInfo,
      );
      expect(result).toEqual({ status: 200 });
      expect(runOperationFn).toHaveBeenCalledWith(
        {
          userId: 'mockUserId',
          identityUserId: 'mockIdentityUserId',
          partnerId: 'mockPartnerId',
        },
        input,
      );
    });
  });
  describe('update', () => {
    const {
      Applications: { update: composedResolver },
    } = composeResolvers(
      { Applications: { update: mutations.update } },
      { Applications: { update: composition.update } },
    );

    const input = { id: 'mockApplicationId' } as unknown as UpdateApplicationInput;

    it('throws if the user is not authenticated', async () => {
      expect(() =>
        composedResolver(
          undefined,
          { input },
          {} as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        ),
      ).toThrow(new AuthenticationError('No access token provided'));
    });
    it('throws if not the owner', async () => {
      try {
        await composedResolver(
          undefined,
          { input },
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              partnerId: 'mockPartnerId',
            },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              applications: {
                exists: vi.fn().mockResolvedValueOnce(false),
                findWithOptions: vi.fn().mockResolvedValueOnce({ partnerId: 'wrongPartnerId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthenticationError);
        expect((error as Error).message).toBe('User is not the owner of this entity');
      }
    });
    it('throws if the user does not have EDIT permission on the application', async () => {
      try {
        await composedResolver(
          undefined,
          { input },
          {
            token: {
              userId: 'mockUserId',
              identityUserId: 'mockIdentityUserId',
              partnerId: 'mockPartnerId',
            },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              applications: {
                exists: vi.fn().mockResolvedValueOnce(true),
                findWithOptions: vi.fn().mockResolvedValueOnce({
                  case: { program: { status: ProgramStatus.Open, partnerId: 'mockPartnerId' } },
                }),
              },
              identity: {
                checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }),
              },
              partners: {
                hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
              },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('Does not have access');
      }
    });
    it('sends the request to the operation', async () => {
      const runOperationFn = vi.fn().mockResolvedValueOnce({ status: 200 });
      const result = await composedResolver(
        undefined,
        { input },
        {
          token: {
            userId: 'mockUserId',
            identityUserId: 'mockIdentityUserId',
            adminId: 'mockAdminId',
            partnerId: 'mockPartnerId',
          },
          operations: { applications: { update: { run: runOperationFn } } },
          services: {
            admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
            applications: {
              exists: vi.fn().mockResolvedValueOnce(true),
              findWithOptions: vi.fn().mockResolvedValueOnce({
                case: { program: { status: ProgramStatus.Open, partnerId: 'mockPartnerId' } },
              }),
            },
            identity: {
              checkPermission: vi
                .fn()
                .mockResolvedValueOnce({ canAccess: true })
                .mockResolvedValueOnce({ canAccess: true }),
            },
            partners: {
              hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
            },
          },
        } as unknown as AdminContext,
        {} as GraphQLResolveInfo,
      );
      expect(result).toEqual({ status: 200 });
      expect(runOperationFn).toHaveBeenCalledWith(
        {
          userId: 'mockUserId',
          identityUserId: 'mockIdentityUserId',
          adminId: 'mockAdminId',
          partnerId: 'mockPartnerId',
        },
        input,
      );
    });
  });
});

import { composeResolvers } from '@graphql-tools/resolvers-composition';
import { AuthorizationError } from '@platform-api/@types/errors.js';
import { AdminContext } from '@platform-api/@types/graphql.js';
import { GraphQLResolveInfo } from 'graphql';
import { composition, mutations } from './accessRequests.js';

describe('AccessRequests', () => {
  describe('createAccessRequest', () => {
    const {
      CreateAccessRequest: { createAccessRequest: composedResolver },
    } = composeResolvers(
      { CreateAccessRequest: { createAccessRequest: mutations.createAccessRequest } },
      { CreateAccessRequest: { createAccessRequest: composition.createAccessRequest } },
    );

    it('should throw if there is no authenticated user', async () => {
      const getAdminFn = vi.fn();
      const pinFn = vi.fn();
      try {
        await composedResolver(
          undefined,
          {
            input: {
              resource: { objectId: 'mockCaseId', objectType: 'CASE' },
              relation: 'editor',
              partnerId: 'mockPartnerId',
            },
          },
          {
            services: {
              admins: { findById: getAdminFn },
              documents: { pin: pinFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('User is not an admin');
      }
    });
    it('should call the service with the input if the authenticated user is an admin', async () => {
      const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
      const createAccessRequestFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });

      const result = await composedResolver(
        undefined,
        {
          input: {
            resource: { objectId: 'mockCaseId', objectType: 'CASE' },
            relation: 'editor',
            partnerId: 'mockPartnerId',
          },
        },
        {
          token: {
            userId: 'mockUserId',
            identityUserId: 'mockIdentityUserId',
            adminId: 'mockAdminId',
          },
          services: {
            admins: { findById: getAdminFn },
            identity: {
              createAccessRequest: createAccessRequestFn,
              checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }),
            },
          },
        } as unknown as AdminContext,
        {} as GraphQLResolveInfo,
      );

      expect(result).toEqual({ metadata: { status: 200 } });
      expect(createAccessRequestFn).toHaveBeenCalledWith(
        {
          userId: 'mockUserId',
          identityUserId: 'mockIdentityUserId',
          adminId: 'mockAdminId',
        },
        {
          partnerId: 'mockPartnerId',
          resource: { objectId: 'mockCaseId', objectType: 'CASE' },
          relation: 'editor',
        },
      );
    });
  });
  describe('reviewAccessRequest', () => {
    const {
      ReviewAccessRequest: { reviewAccessRequest: composedResolver },
    } = composeResolvers(
      { ReviewAccessRequest: { reviewAccessRequest: mutations.reviewAccessRequest } },
      { ReviewAccessRequest: { reviewAccessRequest: composition.reviewAccessRequest } },
    );

    it('should throw if there is no authenticated user', async () => {
      const getAdminFn = vi.fn();
      const pinFn = vi.fn();
      try {
        await composedResolver(
          undefined,
          {
            input: {
              requestId: 'mockRequestId',
              reviewerId: '',
              approved: true,
              detail: 'Go for it my man',
            },
          },
          {
            services: {
              admins: { findById: getAdminFn },
              documents: { pin: pinFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect.fail('Should have thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AuthorizationError);
        expect((error as Error).message).toBe('User is not an admin');
      }
    });
    it('should call the service with the input if the authenticated user is an admin', async () => {
      const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
      const reviewAccessRequestFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });

      const result = await composedResolver(
        undefined,
        {
          input: {
            requestId: 'mockRequestId',
            reviewerId: '',
            approved: true,
            detail: 'Go for it my man',
          },
        },
        {
          token: {
            userId: 'mockUserId',
            identityUserId: 'mockIdentityUserId',
            adminId: 'mockAdminId',
          },
          services: {
            admins: { findById: getAdminFn },
            identity: {
              checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }),
              reviewAccessRequest: reviewAccessRequestFn,
            },
          },
        } as unknown as AdminContext,
        {} as GraphQLResolveInfo,
      );

      expect(result).toEqual({ metadata: { status: 200 } });
      expect(reviewAccessRequestFn).toHaveBeenCalledWith({
        requestId: 'mockRequestId',
        approved: true,
        reviewerId: 'mockIdentityUserId',
        detail: 'Go for it my man',
      });
    });
  });
});

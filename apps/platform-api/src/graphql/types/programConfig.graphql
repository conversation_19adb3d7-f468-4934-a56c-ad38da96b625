type RecurringPaymentConfig {
  amount: PositiveInt
  pattern: PaymentSchedule
  count: PositiveInt
  totalAmount: PositiveInt
  start: DateTime
  frequency: PaymentSchedule! @deprecated(reason: "Replaced with pattern")
  startDate: DateTime @deprecated(reason: "Replaced with start")
}

type PaymentsConfig {
  recurring: RecurringPaymentConfig
}

enum ReapplicationValidationType {
  TimeSinceDecision
  MaxNumberOfApplications
}

type ReapplicationRules {
  type: ReapplicationValidationType!
  value: NonNegativeInt!
  unit: String
}

type VerificationFileKey {
  details: String
  key: String
  sample: String
}

type VerificationConfig {
  dataLookup: [VerificationFileKey!]
}

type ApplicationConfigReviewFields {
  appConfigId: UUID!
  fields: [String!]!
}

type ProgramContext {
  link: String
  description: String!
}

type ProgramConfig {
  applicationConfiguration: String
  maxFundingAmount: NonNegativeInt
  mailingAddressOverride: Address
  payments: PaymentsConfig
  reapplicationRules: [ReapplicationRules!]
  sortOrder: Int
  verification: VerificationConfig
    @deprecated(reason: "Use program.verificationConfigurations instead")
  rulesEvaluationEnabled: Boolean
  applicationReviewFields: [ApplicationConfigReviewFields!]
  programContext: ProgramContext
}

import { mockAdminToken, mockLoginToken } from '@platform-api-test/mocks.js';
import { AdminContext, AuthenticatedContext, Context } from '@platform-api/@types/graphql.js';
import { Services } from '@platform-api/@types/services.js';
import ProgramService from '@platform-api/services/programs/ProgramService.js';
import { GraphQLResolveInfo } from 'graphql';
import programResolvers from './program.resolver.js';
import {
  CreateProgramInput,
  Program,
  ProgramStatus,
  UpdateProgramsInput,
} from '@bybeam/platform-types';
import { AuthorizationError } from '@platform-api/@types/errors.js';
import { FileUpload } from 'graphql-upload/Upload.mjs';
import { UpsertLookupConfigInput } from '@bybeam/verification-types';

describe('Program Resolvers', () => {
  describe('Mutation.program', () => {
    it('should return ProgramMutation parent object', () => {
      const result = programResolvers.Mutation.program();
      expect(result).toEqual({ _type: 'ProgramMutations' });
    });
  });
  describe('Query.programs', () => {
    it('passes input to services', async () => {
      const findAndCountFn = vi.fn().mockResolvedValueOnce({
        nodes: [],
        pageInfo: { count: 0 },
      });
      const filter = { id: 'mockProgramId' };
      const pagination = { page: 0, take: 150 };
      const response = await programResolvers.Query.programs(
        undefined,
        {
          filter,
          pagination,
        },
        {
          services: {
            admins: {
              findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
            },
            programs: {
              findAndCount: findAndCountFn,
            } as unknown as ProgramService,
            identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
          } as unknown as Services,
          token: mockAdminToken,
        } as unknown as AuthenticatedContext,
        {} as GraphQLResolveInfo,
      );
      expect(findAndCountFn).toHaveBeenCalledWith(mockAdminToken, filter, pagination);
      expect(response).toEqual({ programs: [], pageInfo: { count: 0 } });
    });
  });
  describe('Program field resolvers', () => {
    describe('applicantTypes', () => {
      it('retrieves the applicantTypes for the program', async () => {
        const findApplicantTypesFn = vi
          .fn()
          .mockResolvedValueOnce([
            { id: 'mockProgramApplicantType1' },
            { id: 'mockProgramApplicantType2' },
          ]);

        const result = await programResolvers.Program.applicantTypes(
          { id: 'mockProgramId' } as Program,
          {},
          {
            services: {
              applicantTypes: { findByProgramId: findApplicantTypesFn },
            },
          } as unknown as Context,
          {} as GraphQLResolveInfo,
        );

        expect(result).toEqual([
          { id: 'mockProgramApplicantType1' },
          { id: 'mockProgramApplicantType2' },
        ]);
        expect(findApplicantTypesFn).toHaveBeenCalledWith('mockProgramId');
      });
    });
    describe('applicationConfigurations', () => {
      it('retrieves the configurations for the program', async () => {
        const findAppConfigFn = vi
          .fn()
          .mockResolvedValueOnce([
            { applicantTypeId: 'mockAppTypeId', configuration: { sections: [] } },
          ]);

        const result = await programResolvers.Program.applicationConfigurations(
          { id: 'mockProgramId' } as Program,
          {},
          {
            services: {
              config: { findApplicationConfigs: findAppConfigFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as Context,
          {} as GraphQLResolveInfo,
        );

        expect(result).toEqual([
          { applicantTypeId: 'mockAppTypeId', configuration: { sections: [] } },
        ]);
        expect(findAppConfigFn).toHaveBeenCalledWith({ programId: 'mockProgramId' });
      });
    });
    describe('documents', () => {
      it('should retrieve the most recent document for a program if user is an admin', async () => {
        const findMostRecentFn = vi.fn().mockResolvedValueOnce({ id: 'mockDocId' });
        const result = await programResolvers.Program.documents(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              programDocuments: { findMostRecent: findMostRecentFn },
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual([{ id: 'mockDocId' }]);
        expect(findMostRecentFn).toHaveBeenCalledWith('mockProgramId');
      });
      it('should return an empty array if the program has no most recent document', async () => {
        const findMostRecentFn = vi.fn();
        const result = await programResolvers.Program.documents(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              programDocuments: { findMostRecent: findMostRecentFn },
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual([]);
        expect(findMostRecentFn).toHaveBeenCalledWith('mockProgramId');
      });
      it('should throw an error if user is not an admin', async () => {
        const findMostRecentFn = vi.fn();
        await expect(
          programResolvers.Program.documents(
            { id: 'mockProgramId' } as Program,
            {},
            {
              token: mockLoginToken,
              services: {
                programDocuments: { findMostRecent: findMostRecentFn },
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
        expect(findMostRecentFn).not.toHaveBeenCalled();
      });
      it('should throw an AuthorizationError for an admin on a different partner', async () => {
        await expect(() =>
          programResolvers.Program.documents(
            { partnerId: 'diffPartnerId' } as Program,
            {},
            {
              token: mockAdminToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
    });
    describe('documentLabels', () => {
      it('should throw if there is no authenticated user', async () => {
        const getAdminFn = vi.fn();
        await expect(() =>
          programResolvers.Program.documentLabels(
            { id: 'mockProgramId' } as Program,
            {},
            {
              services: {
                admins: { findById: getAdminFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should throw if the authenticated is not an admin', async () => {
        const getAdminFn = vi.fn();
        await expect(() =>
          programResolvers.Program.documentLabels(
            { id: 'mockProgramId' } as Program,
            {},
            {
              token: { userId: 'mockUserId' },
              services: {
                admins: { findById: getAdminFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should fetch the documentLabels', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
        const getDocumentLabelFn = vi.fn().mockResolvedValueOnce([{ id: 'mockLabelId' }]);
        const result = await programResolvers.Program.documentLabels(
          { id: 'mockProgramId' } as Program,
          {},
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: { getDocumentLabels: getDocumentLabelFn },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(result).toEqual([{ id: 'mockLabelId' }]);
      });
    });
    describe('feature', () => {
      const mockFeature = { id: 'mockFeatureId' };
      it('returns the features if it is included in the entity', async () => {
        const result = await programResolvers.Program.features(
          { features: [mockFeature] } as unknown as Program,
          {},
          {} as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual([mockFeature]);
      });
      it('retrieves the features with program ID from the programFeatures service if it is not included in the entity', async () => {
        const findByProgramIdFn = vi.fn();
        await programResolvers.Program.features(
          { id: 'mockProgramId' } as unknown as Program,
          {},
          {
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programFeatures: {
                findByProgramId: findByProgramIdFn.mockReturnValueOnce([mockFeature]),
              },
            },
          } as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByProgramIdFn).toBeCalledTimes(1);
        expect(findByProgramIdFn).toHaveBeenCalledWith('mockProgramId');
      });
    });
    describe('funds', () => {
      const mockFund = { id: 'mockFundId', name: 'Mock Fund' };
      it('returns the funds if it is included in the entity', async () => {
        const result = await programResolvers.Program.funds(
          { funds: [mockFund] } as unknown as Program,
          {},
          {} as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual([mockFund]);
      });
      it('retrieves the funds with program ID from the programs service if it is not included in the entity', async () => {
        const findWithOptionsFn = vi.fn();
        const result = await programResolvers.Program.funds(
          { id: 'mockProgramId' } as unknown as Program,
          {},
          {
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: {
                findWithOptions: findWithOptionsFn.mockReturnValueOnce({
                  id: 'mockProgramId',
                  funds: [mockFund],
                }),
              },
            },
          } as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual([mockFund]);
        expect(findWithOptionsFn).toBeCalledTimes(1);
        expect(findWithOptionsFn).toHaveBeenCalledWith({
          where: { id: 'mockProgramId' },
          relations: ['funds'],
        });
      });
      it('retrieves undefined if program not defined', async () => {
        const findWithOptionsFn = vi.fn();
        const result = await programResolvers.Program.funds(
          { id: 'mockProgramId' } as unknown as Program,
          {},
          {
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: {
                findWithOptions: findWithOptionsFn.mockReturnValueOnce(undefined),
              },
            },
          } as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toBe(undefined);
        expect(findWithOptionsFn).toBeCalledTimes(1);
        expect(findWithOptionsFn).toHaveBeenCalledWith({
          where: { id: 'mockProgramId' },
          relations: ['funds'],
        });
      });
    });
    describe('outcomes', () => {
      const mockOutcome = { id: 'mockOutcomeId' };
      it('should throw an AuthorizationError for a non-admin', async () => {
        await expect(() =>
          programResolvers.Program.outcomes(
            { outcomes: [mockOutcome], partnerId: mockLoginToken.partnerId } as unknown as Program,
            {},
            {
              token: mockLoginToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('should throw an AuthorizationError for an admin on a different partner', async () => {
        await expect(() =>
          programResolvers.Program.outcomes(
            { outcomes: [mockOutcome], partnerId: 'diffPartnerId' } as unknown as Program,
            {},
            {
              token: mockAdminToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('returns the outcomes if it is included in the entity and user is an admnin', async () => {
        const result = await programResolvers.Program.outcomes(
          { outcomes: [mockOutcome], partnerId: mockAdminToken.partnerId } as unknown as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual([mockOutcome]);
      });
      it('retrieves the outcomes with program ID from the outcomes service if it is not included in the entity and user is an admin', async () => {
        const findByProgramIdFn = vi.fn();
        await programResolvers.Program.outcomes(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              outcomes: {
                findByProgramId: findByProgramIdFn.mockReturnValueOnce([mockOutcome]),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByProgramIdFn).toHaveBeenCalledTimes(1);
        expect(findByProgramIdFn).toHaveBeenCalledWith('mockProgramId');
      });
    });
    describe('services', () => {
      const mockService = { id: 'mockServiceId' };
      it('should throw an AuthorizationError for a non-admin', () => {
        expect(() =>
          programResolvers.Program.services(
            { id: 'mockProgramId', partnerId: mockLoginToken.partnerId } as Program,
            {},
            {
              token: mockLoginToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('should throw an AuthorizationError for an admin on a different partner', async () => {
        await expect(() =>
          programResolvers.Program.services(
            { partnerId: 'diffPartnerId', services: [mockService] } as unknown as Program,
            {},
            {
              token: mockAdminToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('returns the services if it is included in the entity and user is an admin', async () => {
        const result = await programResolvers.Program.services(
          { partnerId: mockAdminToken.partnerId, services: [mockService] } as unknown as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual([mockService]);
      });
      it('retrieves the services with program ID from the services service if it is not included in the entity and user is an admin', async () => {
        const findByProgramIdFn = vi.fn();
        await programResolvers.Program.services(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              services: {
                findByProgramId: findByProgramIdFn.mockReturnValueOnce([mockService]),
              },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByProgramIdFn).toHaveBeenCalledTimes(1);
        expect(findByProgramIdFn).toHaveBeenCalledWith('mockProgramId');
      });
    });
    describe('stats', () => {
      const mockStats = { awardedBalance: 10000 };
      it('should throw an AuthorizationError for a non-admin', () => {
        expect(() =>
          programResolvers.Program.stats(
            { id: 'mockProgramId' } as Program,
            {},
            {
              token: mockLoginToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('should throw an AuthorizationError for an admin on a different partner', async () => {
        await expect(() =>
          programResolvers.Program.stats(
            { id: 'mockProgramId', partnerId: 'diffPartnerId' } as Program,
            {},
            {
              token: mockAdminToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('returns the stats if they are included in the entity and user is an admin', async () => {
        const result = await programResolvers.Program.stats(
          { partnerId: mockAdminToken.partnerId, stats: mockStats } as unknown as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual(mockStats);
      });
      it('calls calculateStats with the programId if it is not included in the entity and user is an admin', async () => {
        const calculateStatsFn = vi.fn();
        await programResolvers.Program.stats(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              programs: {
                calculateStats: calculateStatsFn.mockReturnValueOnce(mockStats),
              },
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(calculateStatsFn).toHaveBeenCalledTimes(1);
        expect(calculateStatsFn).toHaveBeenCalledWith('mockProgramId');
      });
    });
    describe('workflowSummary', () => {
      it('should throw an AuthorizationError for a non-admin', () => {
        expect(() =>
          programResolvers.Program.workflowSummary(
            { id: 'mockProgramId' } as Program,
            {},
            {
              token: mockLoginToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('should throw an AuthorizationError for an admin on a different partner', async () => {
        await expect(() =>
          programResolvers.Program.workflowSummary(
            { partnerId: 'diffPartnerId' } as Program,
            {},
            {
              token: mockAdminToken,
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('retrieves the workflowSummary with program ID from the workflowEvents service if user is an admin', async () => {
        const summarizeFn = vi.fn();
        await programResolvers.Program.workflowSummary(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              workflowEvents: {
                summarize: summarizeFn.mockReturnValueOnce({
                  casesReviewed: 1,
                  casesCertified: 1,
                  casesDenied: 1,
                  casesApproved: 1,
                }),
              },
            },
          } as unknown as AdminContext,
          {
            filter: {},
          } as unknown as GraphQLResolveInfo,
        );
        expect(summarizeFn).toBeCalledTimes(1);
        expect(summarizeFn).toHaveBeenCalledWith(mockAdminToken, { programId: 'mockProgramId' });
      });
    });
    describe('notifications', () => {
      it('should throw an AuthorizationError for a non-admin', () => {
        expect(() =>
          programResolvers.Program.notifications(
            { id: 'mockProgramId' } as Program,
            {},
            {
              token: mockLoginToken,
              services: {
                admins: { findById: vi.fn().mockReturnValueOnce(undefined) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  getProgramNotifications: vi.fn(),
                },
              },
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(expect.any(AuthorizationError));
      });
      it('should return the notifications if user is an admin', async () => {
        const getProgramNotificationsFn = vi.fn();
        const mockNotification = [
          {
            type: 'ApplicationDenied',
            channelConfigurations: [
              {
                channel: 'email',
                enabled: true,
                template: { id: 'mockTemplateId' },
              },
            ],
          },
        ];
        const result = await programResolvers.Program.notifications(
          { id: 'mockProgramId', partnerId: mockAdminToken.partnerId } as Program,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: {
                getProgramNotifications: getProgramNotificationsFn.mockReturnValueOnce([
                  mockNotification,
                ]),
              },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toStrictEqual([mockNotification]);
        expect(getProgramNotificationsFn).toHaveBeenCalledTimes(1);
        expect(getProgramNotificationsFn).toHaveBeenCalledWith('mockProgramId');
      });
    });
  });
  describe('ProgramMutations', () => {
    describe('create', () => {
      it('Throws if user is a non-admin', async () => {
        const input = { name: 'Mock Program', status: ProgramStatus.Closed } as CreateProgramInput;
        await expect(
          programResolvers.ProgramMutations.create(
            undefined,
            { input },
            {
              operations: {
                programs: {
                  create: { run: vi.fn() },
                },
              },
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });
      it('Allow access if user is an admin', async () => {
        const mockCreateFn = vi.fn();
        const input = { name: 'Mock Program', status: ProgramStatus.Closed } as CreateProgramInput;
        await programResolvers.ProgramMutations.create(
          undefined,
          { input },
          {
            operations: {
              programs: {
                create: { run: mockCreateFn },
              },
            },
            services: {
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(mockCreateFn).toHaveBeenCalledWith(mockAdminToken, input);
      });
    });
    describe('update', () => {
      it('Throws if user is a non-admin', async () => {
        const input = [
          { id: 'mockProgramId', status: ProgramStatus.Closed },
        ] as UpdateProgramsInput;
        await expect(
          programResolvers.ProgramMutations.update(
            undefined,
            { input },
            {
              services: {
                programs: {
                  update: vi.fn(),
                  findByIds: vi
                    .fn()
                    .mockResolvedValueOnce([{ partnerId: mockLoginToken.partnerId }]),
                },
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });
      it('Throws if program does not belong to the partner', async () => {
        const input = [
          { id: 'mockProgramId', status: ProgramStatus.Closed },
        ] as UpdateProgramsInput;
        await expect(
          programResolvers.ProgramMutations.update(
            undefined,
            { input },
            {
              services: {
                programs: {
                  findByIds: vi.fn().mockResolvedValueOnce([{ partnerId: 'anotherPartnerId' }]),
                },
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as unknown as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });
      it('Allow access if user is an admin and program belongs to the partner', async () => {
        const mockUpdateFn = vi.fn();
        await programResolvers.ProgramMutations.update(
          undefined,
          {
            input: [{ id: 'mockProgramId', status: ProgramStatus.Closed }],
          },
          {
            services: {
              programs: {
                update: mockUpdateFn,
                findByIds: vi.fn().mockResolvedValueOnce([{ partnerId: mockAdminToken.partnerId }]),
              },
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(mockUpdateFn).toHaveBeenCalledWith(mockAdminToken, [
          { id: 'mockProgramId', status: ProgramStatus.Closed },
        ]);
      });
    });
    describe('uploadVerificationFile', () => {
      const mockFile = { filename: 'file.csv' } as unknown as Promise<FileUpload>;
      it('Throws if user is a non-admin', async () => {
        const uploadVerificationFileFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.uploadVerificationFile(
            undefined,
            {
              input: { id: 'mockProgramId', file: mockFile },
            },
            {
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                programFeatures: {
                  hasFeatures: vi.fn(),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              operations: {
                programs: {
                  uploadVerificationFile: { run: uploadVerificationFileFn },
                },
              },
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(uploadVerificationFileFn).not.toHaveBeenCalled();
      });
      it('Throws if program does not have verification feature', async () => {
        const uploadVerificationFileFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.uploadVerificationFile(
            undefined,
            {
              input: { id: 'mockProgramId', file: mockFile },
            },
            {
              services: {
                admins: {
                  findById: vi.fn().mockResolvedValue({ id: 'mockAdminId' }),
                },
                programFeatures: {
                  hasFeatures: vi.fn().mockResolvedValueOnce(false),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
              operations: {
                programs: {
                  uploadVerificationFile: { run: uploadVerificationFileFn },
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(uploadVerificationFileFn).not.toHaveBeenCalled();
      });
      it('Allow access if user is an admin and program has verification features', async () => {
        const uploadVerificationFileFn = vi.fn();
        const mockInput = {
          id: 'mockProgramId',
          file: mockFile,
        };
        await programResolvers.ProgramMutations.uploadVerificationFile(
          undefined,
          { input: mockInput },
          {
            services: {
              admins: {
                findById: vi.fn().mockResolvedValue({ id: 'mockAdminId' }),
              },
              programFeatures: {
                hasFeatures: vi.fn().mockResolvedValueOnce(true),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
            operations: {
              programs: {
                uploadVerificationFile: { run: uploadVerificationFileFn },
              },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(uploadVerificationFileFn).toHaveBeenCalledWith(mockAdminToken, mockInput);
      });
    });
    describe('upsertLookupConfig', () => {
      it('Throws if user is a non-admin', async () => {
        const upsertLookupConfigFn = vi.fn();
        const input = {
          id: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          fields: [],
        } as UpsertLookupConfigInput;
        await expect(
          programResolvers.ProgramMutations.upsertLookupConfig(
            undefined,
            { input },
            {
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                programFeatures: {
                  hasFeatures: vi.fn(),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              operations: {
                programs: {
                  upsertLookupConfig: { run: upsertLookupConfigFn },
                },
              },
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });
      it('Throws if program does not have verification feature', async () => {
        const upsertLookupConfigFn = vi.fn();
        const input = {
          id: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          fields: [],
        } as UpsertLookupConfigInput;
        await expect(
          programResolvers.ProgramMutations.upsertLookupConfig(
            undefined,
            {
              input,
            },
            {
              services: {
                admins: {
                  findById: vi.fn().mockResolvedValue({ id: 'mockAdminId' }),
                },
                programFeatures: {
                  hasFeatures: vi.fn().mockResolvedValueOnce(false),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
              operations: {
                programs: {
                  upsertLookupConfig: { run: upsertLookupConfigFn },
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(upsertLookupConfigFn).not.toHaveBeenCalled();
      });
      it('Allow access if user is an admin and program has verification features', async () => {
        const upsertLookupConfigFn = vi.fn();
        const input = {
          id: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          fields: [],
        } as UpsertLookupConfigInput;
        await programResolvers.ProgramMutations.upsertLookupConfig(
          undefined,
          { input },
          {
            services: {
              admins: {
                findById: vi.fn().mockResolvedValue({ id: 'mockAdminId' }),
              },
              programFeatures: {
                hasFeatures: vi.fn().mockResolvedValueOnce(true),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
            operations: {
              programs: {
                upsertLookupConfig: { run: upsertLookupConfigFn },
              },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(upsertLookupConfigFn).toHaveBeenCalledWith(mockAdminToken, input);
      });
    });
    describe('removeProgramFund', () => {
      it('Throws if user is a non-admin', async () => {
        const removeProgramFundFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.removeProgramFund(
            undefined,
            {
              input: { programId: 'mockProgramId', fundId: 'mockFundId' },
            },
            {
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              operations: {
                programs: {
                  removeProgramFund: { run: removeProgramFundFn },
                },
              },
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(removeProgramFundFn).not.toHaveBeenCalled();
      });

      it('Throws if program does not belong to the partner', async () => {
        const removeProgramFundFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.removeProgramFund(
            undefined,
            {
              input: { programId: 'mockProgramId', fundId: 'mockFundId' },
            },
            {
              services: {
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'anotherPartnerId' }),
                },
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
              operations: {
                programs: {
                  removeProgramFund: { run: removeProgramFundFn },
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(removeProgramFundFn).not.toHaveBeenCalled();
      });

      it('Allow access if user is an admin and program belongs to the partner', async () => {
        const removeProgramFundFn = vi.fn();
        const mockInput = {
          programId: 'mockProgramId',
          fundId: 'mockFundId',
        };
        await programResolvers.ProgramMutations.removeProgramFund(
          undefined,
          { input: mockInput },
          {
            services: {
              programs: {
                findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
              },
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
            operations: {
              programs: {
                removeProgramFund: { run: removeProgramFundFn },
              },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(removeProgramFundFn).toHaveBeenCalledWith(mockAdminToken, mockInput);
      });
    });
    describe('addProgramFund', () => {
      it('Throws if user is a non-admin', async () => {
        const addProgramFundFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.addProgramFund(
            undefined,
            {
              input: { programId: 'mockProgramId', fundId: 'mockFundId' },
            },
            {
              services: {
                admins: {
                  findById: vi.fn().mockReturnValueOnce(undefined),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
              operations: {
                programs: {
                  addProgramFund: { run: addProgramFundFn },
                },
              },
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(addProgramFundFn).not.toHaveBeenCalled();
      });
      it('Throws if program does not belong to the partner', async () => {
        const addProgramFundFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.addProgramFund(
            undefined,
            {
              input: { programId: 'mockProgramId', fundId: 'mockFundId' },
            },
            {
              services: {
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'anotherPartnerId' }),
                },
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
              operations: {
                programs: {
                  addProgramFund: { run: addProgramFundFn },
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(addProgramFundFn).not.toHaveBeenCalled();
      });
      it('Throws if fund does not belong to the partner', async () => {
        const addProgramFundFn = vi.fn();
        await expect(
          programResolvers.ProgramMutations.addProgramFund(
            undefined,
            {
              input: { programId: 'mockProgramId', fundId: 'mockFundId' },
            },
            {
              services: {
                funds: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'anotherPartnerId' }),
                },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
                admins: {
                  findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
                },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              },
              operations: {
                programs: {
                  addProgramFund: { run: addProgramFundFn },
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
        expect(addProgramFundFn).not.toHaveBeenCalled();
      });
      it('Allow access if user is an admin and program belongs to the partner', async () => {
        const addProgramFundFn = vi.fn();
        const mockInput = {
          programId: 'mockProgramId',
          fundId: 'mockFundId',
        };
        await programResolvers.ProgramMutations.addProgramFund(
          undefined,
          { input: mockInput },
          {
            services: {
              funds: {
                findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
              },
              programs: {
                findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
              },
              admins: {
                findById: vi.fn().mockReturnValueOnce({ id: 'mockAdminId', archivedAt: null }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
            operations: {
              programs: {
                addProgramFund: { run: addProgramFundFn },
              },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(addProgramFundFn).toHaveBeenCalledWith(mockAdminToken, mockInput);
      });
    });
  });
});

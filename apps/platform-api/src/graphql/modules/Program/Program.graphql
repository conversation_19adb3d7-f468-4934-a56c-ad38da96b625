enum ProgramStatus {
  Open
  ReferralOnly
  Closed
}

enum ProgramDocumentStatus {
  InProgress
  Failed
  Completed
}

type ProgramDocument {
  id: UUID!
  status: ProgramDocumentStatus
  document: Document
}

type ProgramFundStats {
  fundId: UUID!
  programId: UUID!
  awardedBalance: BigInt!
  obligatedBalance: BigInt!
  paymentCount: Int
}

type CaseCounts {
  Incomplete: Int
  InProgress: Int
  ReadyForReview: Int
  InReview: Int
  PendingCertification: Int
  FiscalReview: Int
  PaymentSent: Int
  Denied: Int
  Withdrawn: Int
  Archived: Int
  Approved: Int
  All: Int
}

type ProgramStats {
  id: UUID!
  awardedBalance: BigInt!
  caseCounts: CaseCounts!
  programFundStats: [ProgramFundStats!]!
}

type ProgramApplicationConfiguration {
  programId: UUID!
  # TODO: Make required once data completely migrated
  applicantTypeId: UUID
  configurationId: UUID!
  configuration: ApplicationConfiguration!
}

type ProgramApplicantType {
  id: UUID!
  applicantType: ApplicantType!
  nameOverride: NonEmptyString
}

type Program {
  id: UUID!
  name: String!
  status: ProgramStatus!
  heroImage: String
  config: ProgramConfig!

  applicantTypes: [ProgramApplicantType!]!
  applicationConfiguration: ApplicationConfiguration
    @deprecated(reason: "Use program.applicationConfigurations instead")
  applicationConfigurations: [ProgramApplicationConfiguration!]!
  documents: [ProgramDocument!]
  features: [FeatureSetting]!
  funds: [Fund!]!
  outcomes: [Outcome!]!
  services: [Service!]!
  stats: ProgramStats!
  verificationConfigurations: [VerificationConfiguration!]!
  workflowSummary(filter: WorkflowSummaryFilter): WorkflowSummary!
  documentLabels: [ActiveLabel!]!
  workflow: Workflow!
  notifications: [NotificationConfig!]
}

input ProgramFilter {
  id: UUID!
}

type ProgramPage {
  programs: [Program!]!
  pageInfo: PageInfo!
}

input ApplicationConfigReviewFieldsInput {
  appConfigId: UUID!
  fields: [String!]
}

input ProgramContextInput {
  description: String!
  link: String
}

input UpdateProgramInput {
  id: UUID!
  status: ProgramStatus
  rulesEvaluationEnabled: Boolean
  applicationReviewFields: [ApplicationConfigReviewFieldsInput!]
  programContext: ProgramContextInput
}

input UploadVerificationFileInput {
  id: UUID!
  file: Upload!
}

input CreateProgramInput {
  name: NonEmptyString!
  status: ProgramStatus
  heroImage: String
  fundIds: [UUID!]!
}

input RemoveProgramFundInput {
  programId: UUID!
  fundId: UUID!
}

input AddProgramFundInput {
  programId: UUID!
  fundId: UUID!
}

type ProgramsMutationResponse {
  metadata: ResponseMetadata!
  query: Query!
  record: [Program!]
}

type ProgramMutationResponse {
  metadata: ResponseMetadata!
  query: Query!
  record: Program
}

type ProgramMutations {
  create(input: CreateProgramInput): ProgramMutationResponse!
    @experimental(reason: "Will be used as POC for program management")
  update(input: [UpdateProgramInput!]!): ProgramsMutationResponse!
  uploadVerificationFile(input: UploadVerificationFileInput!): ProgramMutationResponse!
  upsertLookupConfig(input: UpsertLookupConfigInput!): ProgramMutationResponse!
  addProgramFund(input: AddProgramFundInput): ProgramMutationResponse!
  removeProgramFund(input: RemoveProgramFundInput): ProgramMutationResponse!
}

extend type Query {
  programs(filter: ProgramFilter, pagination: CursorPagination): ProgramPage!
}

extend type Mutation {
  program: ProgramMutations
}

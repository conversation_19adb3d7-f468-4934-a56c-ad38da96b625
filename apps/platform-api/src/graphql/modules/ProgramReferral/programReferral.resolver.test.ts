import { ProgramReferral, ProgramStatus } from '@bybeam/platform-types';
import { mockAdminToken, mockLoginToken } from '@platform-api-test/mocks.js';
import { GraphQLResolveInfo } from 'graphql';
import { Permission } from '@bybeam/identity-client/types';
import programReferralResolvers from './programReferral.resolver.js';
import { ProgramReferralService, Services } from '../../../@types/services.js';
import { AuthorizationError } from '../../../@types/errors.js';
import { AdminContext, AuthenticatedContext } from '../../../@types/graphql.js';

describe('Program Referral Resolvers', () => {
  describe('Mutation.programReferral', () => {
    it('should return ProgramReferralMutations parent object', () => {
      const result = programReferralResolvers.Mutation.programReferral();
      expect(result).toEqual({ _type: 'ProgramReferralMutations' });
    });
  });
  describe('Query.programReferral', () => {
    it('passes input to services for applicantReferrals', async () => {
      const mockServices = {
        admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
        identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
        programReferrals: {
          aggregateApplicantsReferred: vi.fn(),
        } as unknown as ProgramReferralService,
      } as unknown as Services;
      await programReferralResolvers.Query.applicantsReferred(
        undefined,
        {},
        {
          services: mockServices,
          token: mockAdminToken,
        } as unknown as AdminContext,
        {} as GraphQLResolveInfo,
      );
      expect(mockServices.programReferrals.aggregateApplicantsReferred).toHaveBeenCalledWith(
        mockAdminToken,
      );
    });
  });
  describe('ProgramReferralMutations', () => {
    describe('create', () => {
      it('should throw if there is no authenticated user', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {} as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should throw if the authenticated user is not an admin', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            { token: { userId: 'mockUserId', partnerId: 'mockPartnerId' } } as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should throw when the program belongs to a different partner', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'someOtherPartner' }),
                },
                users: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'someOtherPartner' }),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw when user belongs to a different partner', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
                users: { findById: vi.fn().mockResolvedValueOnce({ partnerId: 'otherPartnerId' }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw when user does not exist', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
                users: { findById: vi.fn().mockResolvedValueOnce(undefined) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw when the program is Closed', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValue({
                    partnerId: mockAdminToken.partnerId,
                    status: ProgramStatus.Closed,
                  }),
                },
                users: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Program is not open');
      });

      it('should throw when the program does not have the Referrals feature', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
                programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
                users: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Feature Programs: Referral is not enabled');
      });

      it('should throw if the user does not have EDIT permission on the program', async () => {
        const checkPermissionFn = vi.fn();
        await expect(() =>
          programReferralResolvers.ProgramReferralMutations.create(
            undefined,
            { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
            {
              token: {
                userId: 'mockUserId',
                identityUserId: 'mockIdentityUserId',
                adminId: 'mockAdminId',
                partnerId: 'mockPartnerId',
              },
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: {
                  checkPermission: checkPermissionFn
                    .mockResolvedValueOnce({ canAccess: true })
                    .mockResolvedValueOnce({ canAccess: false }),
                },
                partners: {
                  hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
                },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'mockPartnerId' }),
                },
                programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
                users: { findById: vi.fn().mockResolvedValueOnce({ partnerId: 'mockPartnerId' }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Does not have access');
        expect(checkPermissionFn).toHaveBeenCalledWith({
          action: Permission.Edit,
          resource: { objectId: 'mockProgramId', objectType: 'PROGRAM' },
          subject: { objectId: 'mockIdentityUserId', objectType: 'USER' },
        });
      });

      it('should delegate create to the service when program is Open', async () => {
        const createFn = vi.fn();

        await programReferralResolvers.ProgramReferralMutations.create(
          undefined,
          { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
          {
            token: mockAdminToken,
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              identity: {
                checkPermission: vi
                  .fn()
                  .mockResolvedValueOnce({ canAccess: true })
                  .mockResolvedValueOnce({ canAccess: true }),
              },
              programs: {
                findById: vi.fn().mockResolvedValueOnce({
                  status: ProgramStatus.Open,
                  partnerId: mockAdminToken.partnerId,
                }),
              },
              programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
              programReferrals: { create: createFn },
              users: {
                findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
              },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );

        expect(createFn).toHaveBeenCalledWith(mockAdminToken, {
          programId: 'mockProgramId',
          userId: 'mockUserId',
        });
      });

      it('should delegate create to the service when program is Referral-Only', async () => {
        const createFn = vi.fn();

        await programReferralResolvers.ProgramReferralMutations.create(
          undefined,
          { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
          {
            token: mockAdminToken,
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              identity: {
                checkPermission: vi
                  .fn()
                  .mockResolvedValueOnce({ canAccess: true })
                  .mockResolvedValueOnce({ canAccess: true }),
              },
              programs: {
                findById: vi.fn().mockResolvedValueOnce({
                  status: ProgramStatus.ReferralOnly,
                  partnerId: mockAdminToken.partnerId,
                }),
              },
              programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
              programReferrals: { create: createFn },
              users: {
                findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
              },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );

        expect(createFn).toHaveBeenCalledWith(mockAdminToken, {
          programId: 'mockProgramId',
          userId: 'mockUserId',
        });
      });
    });
    describe('createBulkProgramReferral', () => {
      it('should throw if there is no authenticated user', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            {} as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should throw if the authenticated user is not an admin', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            { token: { userId: 'mockUserId', partnerId: 'mockPartnerId' } } as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should throw when the program belongs to a different partner', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'someOtherPartner' }),
                },
                users: {
                  findByIds: vi
                    .fn()
                    .mockResolvedValueOnce([{ id: 'mockUserId', partnerId: 'someOtherPartner' }]),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw when any user belongs to a different partner', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
                users: {
                  findByIds: vi
                    .fn()
                    .mockResolvedValueOnce([{ id: 'mockUserId', partnerId: 'someOtherPartner' }]),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw when the program is Closed', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValue({
                    partnerId: mockAdminToken.partnerId,
                    status: ProgramStatus.Closed,
                  }),
                },
                users: {
                  findByIds: vi
                    .fn()
                    .mockResolvedValueOnce([
                      { id: 'mockUserId', partnerId: mockAdminToken.partnerId },
                    ]),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Program is not open');
      });

      it('should throw when the program does not have the Referrals feature', async () => {
        await expect(
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            {
              token: mockAdminToken,
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: mockAdminToken.partnerId }),
                },
                programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
                users: {
                  findByIds: vi
                    .fn()
                    .mockResolvedValueOnce([
                      { id: 'mockUserId', partnerId: mockAdminToken.partnerId },
                    ]),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Feature Programs: Referral is not enabled');
      });

      it('should throw if the user does not have EDIT permission on the program', async () => {
        const checkPermissionFn = vi.fn();
        await expect(() =>
          programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
            undefined,
            { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
            {
              token: {
                userId: 'mockUserId',
                identityUserId: 'mockIdentityUserId',
                adminId: 'mockAdminId',
                partnerId: 'mockPartnerId',
              },
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
                identity: {
                  checkPermission: checkPermissionFn
                    .mockResolvedValueOnce({ canAccess: true })
                    .mockResolvedValueOnce({ canAccess: false }),
                },
                partners: {
                  hasFeature: vi.fn().mockResolvedValueOnce(true).mockResolvedValueOnce(true),
                },
                programs: {
                  findById: vi.fn().mockResolvedValueOnce({ partnerId: 'mockPartnerId' }),
                },
                programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
                users: {
                  findByIds: vi
                    .fn()
                    .mockResolvedValueOnce([
                      { id: 'mockUserId', partnerId: mockAdminToken.partnerId },
                    ]),
                },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Does not have access');
        expect(checkPermissionFn).toHaveBeenCalledWith({
          action: Permission.Edit,
          resource: { objectId: 'mockProgramId', objectType: 'PROGRAM' },
          subject: { objectId: 'mockIdentityUserId', objectType: 'USER' },
        });
      });

      it('should delegate createBulkProgramReferral to the operation when program is Open', async () => {
        const createBulkProgramReferralFn = vi.fn();

        await programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
          undefined,
          { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
          {
            token: mockAdminToken,
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              identity: {
                checkPermission: vi
                  .fn()
                  .mockResolvedValueOnce({ canAccess: true })
                  .mockResolvedValueOnce({ canAccess: true }),
              },
              programs: {
                findById: vi.fn().mockResolvedValueOnce({
                  status: ProgramStatus.Open,
                  partnerId: mockAdminToken.partnerId,
                }),
              },
              programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
              users: {
                findByIds: vi
                  .fn()
                  .mockResolvedValueOnce([
                    { id: 'mockUserId', partnerId: mockAdminToken.partnerId },
                  ]),
              },
            },
            operations: {
              users: { createBulkProgramReferral: { run: createBulkProgramReferralFn } },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );

        expect(createBulkProgramReferralFn).toHaveBeenCalledWith(mockAdminToken, {
          programId: 'mockProgramId',
          userIds: ['mockUserId'],
        });
      });

      it('should delegate createBulkProgramReferral to the operation when program is Referral-Only', async () => {
        const createBulkProgramReferralFn = vi.fn();

        await programReferralResolvers.ProgramReferralMutations.createBulkProgramReferral(
          undefined,
          { input: { programId: 'mockProgramId', userIds: ['mockUserId'] } },
          {
            token: mockAdminToken,
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              identity: {
                checkPermission: vi
                  .fn()
                  .mockResolvedValueOnce({ canAccess: true })
                  .mockResolvedValueOnce({ canAccess: true }),
              },
              programs: {
                findById: vi.fn().mockResolvedValueOnce({
                  status: ProgramStatus.ReferralOnly,
                  partnerId: mockAdminToken.partnerId,
                }),
              },
              programFeatures: { hasFeature: vi.fn().mockResolvedValueOnce(true) },
              users: {
                findByIds: vi
                  .fn()
                  .mockResolvedValueOnce([
                    { id: 'mockUserId', partnerId: mockAdminToken.partnerId },
                  ]),
              },
            },
            operations: {
              users: { createBulkProgramReferral: { run: createBulkProgramReferralFn } },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );

        expect(createBulkProgramReferralFn).toHaveBeenCalledWith(mockAdminToken, {
          programId: 'mockProgramId',
          userIds: ['mockUserId'],
        });
      });
    });
  });
  describe('Program Referral Fields resolvers', () => {
    describe('user', () => {
      it('should throw if there is no authenticated user', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferral.user(
            { id: 'mockReferralId', userId: 'mockUserId' } as ProgramReferral,
            {},
            {} as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Only an admin or the entity owner can access this field');
      });

      it('returns the user on the referral if they are loaded already', async () => {
        const result = await programReferralResolvers.ProgramReferral.user(
          {
            id: 'mockReferralId',
            user: { id: 'mockUserId' },
          } as ProgramReferral,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockUserId' });
      });

      it('should retrieve user if the authenticated user is an admin', async () => {
        const findByIdFn = vi.fn().mockReturnValueOnce({ id: 'mockUserId' });
        const result = await programReferralResolvers.ProgramReferral.user(
          { id: 'mockReferralId', userId: 'mockUserId' } as ProgramReferral,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              users: { findById: findByIdFn },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockUserId' });
        expect(findByIdFn).toBeCalledWith('mockUserId');
      });

      it('should retrieve user if the authenticated user owns the referral', async () => {
        const findByIdFn = vi.fn().mockReturnValueOnce({ id: 'authUserId' });
        const result = await programReferralResolvers.ProgramReferral.user(
          { id: 'mockReferralId', userId: 'authUserId' } as ProgramReferral,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId' },
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              users: { findById: findByIdFn },
            },
          } as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'authUserId' });
        expect(findByIdFn).toBeCalledWith('authUserId');
      });
    });
    describe('program', () => {
      it('should throw if there is no authenticated user', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferral.program(
            { id: 'mockReferralId', userId: 'mockUserId' } as ProgramReferral,
            {},
            {} as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('Only an admin or the entity owner can access this field');
      });

      it('returns the program on the referral if they are loaded already', async () => {
        const result = await programReferralResolvers.ProgramReferral.program(
          {
            id: 'mockReferralId',
            program: { id: 'mockProgramId' },
          } as ProgramReferral,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockProgramId' });
      });

      it('should retrieve program if the authenticated user is an admin', async () => {
        const findByIdFn = vi.fn().mockReturnValueOnce({ id: 'mockProgramId' });
        const result = await programReferralResolvers.ProgramReferral.program(
          {
            id: 'mockReferralId',
            programId: 'mockProgramId',
          } as ProgramReferral,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: { findById: findByIdFn },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockProgramId' });
        expect(findByIdFn).toBeCalledWith('mockProgramId');
      });

      it('should retrieve program if the authenticated user owns the referral', async () => {
        const findByIdFn = vi.fn().mockReturnValueOnce({ id: 'mockProgramId' });
        const result = await programReferralResolvers.ProgramReferral.program(
          {
            id: 'mockReferralId',
            programId: 'mockProgramId',
            userId: 'authUserId',
          } as ProgramReferral,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId' },
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              programs: { findById: findByIdFn },
            },
          } as unknown as AuthenticatedContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockProgramId' });
        expect(findByIdFn).toBeCalledWith('mockProgramId');
      });
    });
    describe('admin', () => {
      it('should throw if there is no authenticated user', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferral.admin(
            { id: 'mockReferralId' } as ProgramReferral,
            {},
            {} as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('should throw if authenticated user is not admin', async () => {
        await expect(() =>
          programReferralResolvers.ProgramReferral.admin(
            { id: 'mockReferralId' } as ProgramReferral,
            {},
            { token: mockLoginToken } as unknown as AuthenticatedContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('returns the admin on the referral if they are loaded already', async () => {
        const result = await programReferralResolvers.ProgramReferral.admin(
          {
            id: 'mockReferralId',
            admin: { id: 'mockAdminId' },
          } as ProgramReferral,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockAdminId' });
      });

      it('should retrieve admin if the authenticated user is an admin', async () => {
        const result = await programReferralResolvers.ProgramReferral.admin(
          {
            id: 'mockReferralId',
            adminId: 'mockAdminId',
          } as ProgramReferral,
          {},
          {
            token: mockAdminToken,
            services: {
              admins: {
                findById: vi.fn().mockResolvedValue({ id: 'mockAdminId' }),
              },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockAdminId' });
      });
    });
  });
});

import { Document } from '@bybeam/platform-types';
import { YesNoUnsure } from '@bybeam/platform-types';
import { AuthenticationError, AuthorizationError } from '@platform-api/@types/errors.js';
import { AdminContext } from '@platform-api/@types/graphql.js';
import { GraphQLResolveInfo } from 'graphql';
import composedResolvers from './Document.resolver.js';

describe('document', () => {
  describe('resolvers', () => {
    describe('previewUrl', () => {
      const {
        Document: { previewUrl: composedResolver },
      } = composedResolvers;

      it('should throw if there is no authenticated user', async () => {
        const getPreviewUrlFn = vi.fn();
        expect(() =>
          composedResolver(
            { id: 'mockDocumentId' } as Document,
            {},
            {
              services: {
                documents: { getPreviewUrl: getPreviewUrlFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).toThrow(new AuthenticationError('No access token provided'));
      });

      it('should retrieve the previewUrl if user is authenticated', async () => {
        const getPreviewUrlFn = vi.fn().mockReturnValueOnce({ docId: 'mockDocumentId' });
        const result = await composedResolver(
          { id: 'mockDocumentId' } as Document,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId' },
            services: {
              documents: { getPreviewUrl: getPreviewUrlFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ docId: 'mockDocumentId' });
        expect(getPreviewUrlFn).toHaveBeenCalledWith(
          { partnerId: 'mockPartnerId', userId: 'authUserId' },
          { id: 'mockDocumentId' },
        );
      });
    });
    describe('summary', () => {
      const {
        Document: { summary: composedResolver },
      } = composedResolvers;

      it('should throw if there is no authenticated user', async () => {
        const getSummaryFn = vi.fn().mockReturnValueOnce({ docId: 'mockDocId' });
        await expect(() =>
          composedResolver(
            { id: 'mockDocumentId' } as Document,
            {},
            {
              services: {
                documents: { getSummary: getSummaryFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow();
      });

      it('returns the summary on the document if it is loaded already', async () => {
        const result = await composedResolver(
          { id: 'mockDocumentId', summary: { docId: 'mockExistingDocId' } } as unknown as Document,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              documents: { getSummary: vi.fn().mockResolvedValueOnce({ docId: 'mockDocId' }) },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ docId: 'mockExistingDocId' });
      });

      it('should retrieve addresses if the authenticated user is an admin', async () => {
        const getSummaryFn = vi.fn().mockReturnValueOnce({ docId: 'mockDocumentId' });
        const result = await composedResolver(
          { id: 'mockDocumentId' } as Document,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              documents: { getSummary: getSummaryFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ docId: 'mockDocumentId' });
        expect(getSummaryFn).toHaveBeenCalledWith('mockDocumentId');
      });
    });
    describe('uploader', () => {
      const {
        Document: { uploader: composedResolver },
      } = composedResolvers;

      it('should throw if there is no authenticated user', async () => {
        const getUserFn = vi.fn().mockReturnValueOnce({ id: 'mockUserId' });
        await expect(() =>
          composedResolver(
            { id: 'mockDocumentId' } as Document,
            {},
            { services: {} } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow();
        expect(getUserFn).not.toHaveBeenCalled();
      });

      it('returns the uploader on the document if it is loaded already', async () => {
        const getUserFn = vi.fn().mockReturnValueOnce({ id: 'mockUserId' });
        const result = await composedResolver(
          { id: 'mockDocumentId', uploader: { id: 'mockExistingUploaderId' } } as Document,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              users: { findById: getUserFn },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockExistingUploaderId' });
        expect(getUserFn).not.toHaveBeenCalled();
      });

      it('returns the uploader on the document if it is loaded already', async () => {
        const getUserFn = vi.fn().mockReturnValueOnce({ id: 'mockUserId' });
        const result = await composedResolver(
          { id: 'mockDocumentId', uploaderId: 'mockUploaderId' } as Document,
          {},
          {
            token: { userId: 'authUserId', partnerId: 'mockPartnerId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' }) },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              users: { findById: getUserFn },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(result).toEqual({ id: 'mockUserId' });
        expect(getUserFn).toHaveBeenCalledWith('mockUploaderId');
      });
    });
  });
  describe('mutations', () => {
    describe('pinDocument', () => {
      const {
        DocumentMutations: { pinDocument: composedResolver },
      } = composedResolvers;

      it('should throw if there is no authenticated user', async () => {
        const getAdminFn = vi.fn();
        const pinFn = vi.fn();
        await expect(() =>
          composedResolver(
            undefined,
            { input: { id: 'mockDocId', pinned: true } },
            {
              services: {
                admins: { findById: getAdminFn },
                documents: { pin: pinFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw if the authenticated user is not an admin', async () => {
        const getAdminFn = vi.fn();
        const pinFn = vi.fn();
        await expect(() =>
          composedResolver(
            undefined,
            { input: { id: 'mockDocId', pinned: true } },
            {
              services: {
                admins: { findById: getAdminFn },
                documents: { pin: pinFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should call the service with the token and input if the authenticated user is an admin', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
        const pinFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });

        const result = await composedResolver(
          undefined,
          { input: { id: 'mockDocId', pinned: true } },
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              documents: { pin: pinFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );

        expect(result).toEqual({ metadata: { status: 200 } });
        expect(pinFn).toHaveBeenCalledWith({ id: 'mockDocId', pinned: true });
      });
    });
    describe('submitFeedback', () => {
      const {
        DocumentMutations: { submitFeedback: composedResolver },
      } = composedResolvers;

      it('should throw if there is no authenticated user', async () => {
        const getAdminFn = vi.fn();
        const submitFeedbackFn = vi.fn();
        await expect(() =>
          composedResolver(
            undefined,
            { input: { id: 'mockDocId', accurate: YesNoUnsure.Yes } },
            {
              services: {
                admins: { findById: getAdminFn },
                documents: { submitPredictionFeedback: submitFeedbackFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should throw if the authenticated user is not an admin', async () => {
        const getAdminFn = vi.fn();
        const submitFeedbackFn = vi.fn();
        await expect(() =>
          composedResolver(
            undefined,
            { input: { id: 'mockDocId', accurate: YesNoUnsure.Yes } },
            {
              token: { userId: 'mockUserId' },
              services: {
                admins: { findById: getAdminFn },
                documents: { submitPredictionFeedback: submitFeedbackFn },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
              },
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(AuthorizationError);
      });

      it('should call the service with the token and input if the authenticated user is an admin', async () => {
        const getAdminFn = vi.fn().mockResolvedValueOnce({ id: 'mockAdminId' });
        const submitFeedbackFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });

        const result = await composedResolver(
          undefined,
          { input: { id: 'mockDocId', accurate: YesNoUnsure.Yes } },
          {
            token: { userId: 'mockUserId', adminId: 'mockAdminId' },
            services: {
              admins: { findById: getAdminFn },
              documents: { submitPredictionFeedback: submitFeedbackFn },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
            },
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );

        expect(result).toEqual({ metadata: { status: 200 } });
        expect(submitFeedbackFn).toHaveBeenCalledWith(
          { userId: 'mockUserId', adminId: 'mockAdminId' },
          { id: 'mockDocId', accurate: YesNoUnsure.Yes },
        );
      });
    });
  });
});

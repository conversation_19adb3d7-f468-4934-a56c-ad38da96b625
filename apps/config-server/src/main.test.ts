import { Transport } from '@nestjs/microservices';
import { describe, expect, it, vi } from 'vitest';
import { AppModule } from './app.module.js';

const { createMicroserviceFn, mockEnableShutdownHooks, mockListenFn, mockLogger, mockUseLogger } =
  vi.hoisted(() => {
    const mockLogger = { log: vi.fn() };
    const mockEnableShutdownHooks = vi.fn();
    const mockListenFn = vi.fn().mockResolvedValue(undefined);
    const mockUseLogger = vi.fn();
    const createMicroserviceFn = vi.fn().mockResolvedValueOnce({
      enableShutdownHooks: mockEnableShutdownHooks,
      get: () => mockLogger,
      listen: mockListenFn,
      useLogger: mockUseLogger,
    });

    return {
      createMicroserviceFn,
      mockEnableShutdownHooks,
      mockListenFn,
      mockLogger,
      mockUseLogger,
    };
  });

vi.mock('@nestjs/core', () => ({
  NestFactory: { createMicroservice: createMicroserviceFn },
}));

describe('service initialization', () => {
  it('initializes the Nest microservice', async () => {
    process.env.PORT = '1234';
    await import('./main.js');

    expect(createMicroserviceFn).toHaveBeenCalledWith(AppModule, {
      transport: Transport.GRPC,
      options: {
        url: '0.0.0.0:1234',
        package: ['config', 'grpc.health.v1', 'grpc.reflection.v1alpha', 'grpc.reflection.v1'],
        protoPath: [
          expect.stringContaining('@bybeam/config-client/proto/config.proto'),
          expect.stringContaining('health/v1/health.proto'),
          expect.stringContaining('grpc/reflection/v1alpha/reflection.proto'),
          expect.stringContaining('grpc/reflection/v1/reflection.proto'),
        ],
        loader: {
          longs: String,
          enums: String,
          defaults: true,
          oneofs: true,
        },
      },
    });
    expect(mockEnableShutdownHooks).toHaveBeenCalled();
    expect(mockUseLogger).toHaveBeenCalledWith(mockLogger);
    expect(mockListenFn).toHaveBeenCalled();
  });
});

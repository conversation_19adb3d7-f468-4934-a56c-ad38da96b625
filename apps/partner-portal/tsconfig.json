{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "downlevelIteration": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "sourceMap": true, "types": ["node", "vitest/globals", "@testing-library/jest-dom", "vitest"], "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@platform-ui-common/*": ["./src/spa-legacy/common/*"], "@platform-ui/*": ["./src/spa-legacy/*"], "@platform-ui-common/types": ["./src/spa-legacy/@types"], "@/tests/*": ["./src/tests/*"]}, "target": "ES2017"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".storybook/*.ts", ".storybook/*.tsx", ".next/dev/types/**/*.ts"], "exclude": ["node_modules"]}
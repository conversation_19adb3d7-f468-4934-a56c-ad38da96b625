/**
 * Posthog feature flags are defined and configured in the Posthog Dashboard.
 * Each relevant project in posthog must define the feature flag.
 */
export const POSTHOG_FEATURE_FLAGS = {
  accessRequests: 'access-requests',
  appConfigPreview: 'app-config-preview',
  applicationReviewFields: 'application-review-fields',
  bannerMessagesPage: 'banner-messages-page',
  bulkProgramReferral: 'bulk-program-referral',
  bulkPartnerIssuedPayments: 'bulk-partner-issued-payments',
  participantsTab: 'participants-tab',
  casePaymentsPage: 'case-payments-page',
  caseDetailsDocumentsList: 'case-details-documents-list',
  caseDetails2024: 'case-details-2024',
  // Keep this key aligned with platform-api POSTHOG_FEATURE_FLAGS.rulesEngine
  rulesEngine: 'rules-engine-enabled',
  changelog: 'changelog',
  docsAi2024Q4: 'docs-ai-2024-Q4',
  documentsExtractedFields: 'documents-extracted-fields',
  fundsPage: 'funds-page',
  integrations: 'integrations',
  presetEmbeddedAnalytics: 'preset-embedded-analytics',
  presetDisableStandardReport: 'preset-disable-standard-report',
  presetPreviewPage: 'preset-preview-page',
  programCreation: 'program-creation',
  programWorkflowPage: 'program-workflow-page',
  programNotificationsPage: 'program-notifications-page',
  paymentMailingAddress: 'payment-mailing-address',
  searchElasticsearch: 'search-elasticsearch',
  sso: 'sso',
  tagAutomations: 'tag-automations',
  verificationConfig: 'verification-config',
  recentLoginDetails: 'recent-login-details',
  autoLinkParticipant: 'auto-link-participant',
  duplicateDetectionUI: 'duplicate-detection-ui',
};

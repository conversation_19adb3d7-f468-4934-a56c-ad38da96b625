import { auth } from '@/app/firebase';
import { Box, Button, Flex } from '@radix-ui/themes';
import { GoogleAuthProvider, signInWithRedirect } from 'firebase/auth';
import { useEffect } from 'react';

import { Apple, Google, Microsoft } from '@mui/icons-material';
import styles from './SocialLoginsModule.module.css';

const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('profile');
googleProvider.addScope('email');

interface SocialLoginsModuleProps {
  enabledSocialLogins?: ('google' | 'apple' | 'microsoft')[];
}

export const SocialLoginsModule = ({
  enabledSocialLogins = ['google', 'apple', 'microsoft'],
}: SocialLoginsModuleProps) => {
  useEffect(() => {});

  const socialLogins = {
    google: (
      <Button
        key={'google'}
        variant="outline"
        onClick={() => {
          signInWithRedirect(auth, googleProvider);
        }}
      >
        <Flex align="center" wrap="wrap" gap="1">
          <Google fontSize="inherit" />
          Continue With Google
        </Flex>
      </Button>
    ),
    apple: (
      <Button key={'apple'} variant="outline">
        <Flex align="center" wrap="wrap" gap="1">
          <Apple fontSize="inherit" />
          Continue With Apple
        </Flex>
      </Button>
    ),
    microsoft: (
      <Button key={'msft'} variant="outline">
        <Flex align="center" wrap="wrap" gap="1">
          <Microsoft fontSize="inherit" />
          Continue With Microsoft
        </Flex>
      </Button>
    ),
  };

  return (
    <>
      <Box my="2" className={styles.divider}>
        <span className={styles.dividerText}>or</span>
      </Box>
      {enabledSocialLogins.map((socialLogin) => socialLogins[socialLogin])}
    </>
  );
};

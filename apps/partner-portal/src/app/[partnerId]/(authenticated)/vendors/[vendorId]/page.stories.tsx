import { Meta, <PERSON>Obj } from '@storybook/nextjs';
import {
  CAN_ACCESS_VENDOR_MOCK,
  VENDOR_MOCK,
  VENDOR_PAYMENTS_MOCK,
} from '../graphql/VendorDetails.mock';
import { HAS_ACCESS_ORGANIZATION_EDIT_MOCK } from '@/tests/mocks/hasAccessMocks';
import Page from './page';

const meta: Meta = {
  component: Page,
};
export default meta;

type Story = StoryObj<typeof Page>;

export const Primary: Story = {
  args: {},
  parameters: {
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/partner/mockPartnerId/vendors/mock-vendor-id',
        segments: [
          ['mock-vendor-id', 'vendors'],
          ['mockPartnerId', 'partner'],
        ],
      },
    },
    apolloClient: {
      mocks: [HAS_ACCESS_ORGANIZATION_EDIT_MOCK, CAN_ACCESS_VENDOR_MOCK, VENDOR_MOCK, VENDOR_PAYMENTS_MOCK],
    },
  },
};

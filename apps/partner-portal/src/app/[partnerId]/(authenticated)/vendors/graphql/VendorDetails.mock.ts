import { Permission } from '@/app/_utils/roles';
import CAN_ACCESS_QUERY from '@/app/providers/HasAccessQuery.graphql';
import { PaymentMethod, PaymentStatus } from '@bybeam/platform-types';
import VENDOR_PAYMENTS_QUERY from './GetPaymentsByVendorIdQuery.graphql';
import VENDOR_QUERY from './GetVendorQuery.graphql';
import VENDORS_LIST from './ListVendors.graphql';

const CAN_ACCESS_PARTNER_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectId: 'mockPartnerId', objectType: 'ORGANIZATION' },
      },
    },
  },
  result: {
    data: {
      canAccess: {
        id: 'user:mock-user-id:partner:mockPartnerId',
        canAccess: true,
        message: 'user:mock-user-id can access partner:mockPartnerId',
        token: 'mockToken',
      },
    },
  },
  newData: () => ({
    data: {
      canAccess: {
        id: 'user:mock-user-id:partner:mockPartnerId',
        canAccess: true,
        message: 'user:mock-user-id can access partner:mockPartnerId',
        token: 'mockToken',
      },
    },
  }),
};

const CAN_ACCESS_VENDOR_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: Permission.View,
        resource: { objectType: 'VENDOR', objectId: 'mock-vendor-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:vendor:mock-vendor-id',
        canAccess: true,
        hasAccess: true,
        message: 'user:mock-user-id can access program:uwkc-program',
        token: 'mockToken',
        __typename: 'HasAccessResponse',
      },
    },
  },
  newData: () => ({
    data: {
      hasAccess: {
        id: 'user:mock-user-id:vendor:mock-vendor-id',
        canAccess: true,
        hasAccess: true,
        message: 'user:mock-user-id can access program:uwkc-program',
        token: 'mockToken',
        __typename: 'HasAccessResponse',
      },
    },
  }),
};

const VENDOR_MOCK = {
  request: {
    query: VENDOR_QUERY,
    variables: {
      bankAccount: true,
    },
  },
  result: {
    data: {
      vendors: {
        nodes: [
          {
            id: 'mockVendorA',
            name: 'Brighter Days Cleaning',
            mailingAddress: {
              addressLine1: '650 S Town Center Dr',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89106,
            },
            types: [{ name: 'Cleaning Services' }],
            externalId: 'EXT1234',
            email: '<EMAIL>',
            phone: '+***********',
            aggregatePayments: { count: 10 },
            documents: [
              {
                id: 'mock-doc-id',
                previewUrl: 'fake.com',
                filename: '403b-certification.pdf',
                mimetype: 'application/pdf',
                createdAt: new Date('2020-10-20T17:39'),
                uploader: { name: 'Eugene Debs' },
              },
            ],
            notes: [
              {
                id: 'mock-note-id',
                content: 'We should look into this vendor something fishy is going on',
                author: { identityUser: { name: 'Eugene Debs' } },
              },
            ],
            bankAccount: {
              accountType: 'checking',
              accountNumber: '1234',
              routingNumber: '********',
            },
          },
        ],
      },
    },
  },
};

const VENDOR_PAYMENTS_MOCK = {
  request: {
    query: VENDOR_PAYMENTS_QUERY,
    variables: {
      vendorId: 'mockVendorA',
      pagination: { page: 0, take: 15 },
      sort: { column: 'CheckDate', direction: 'Ascending' },
    },
  },
  result: {
    data: {
      payments: {
        payments: [
          {
            id: 'mockPaymentA',
            displayId: 'P0001245',
            amount: 35000,
            status: PaymentStatus.Success,
            method: PaymentMethod.DirectDeposit,
            initiatedAt: new Date('2020-10-20T07:12'),
            completedAt: new Date('2020-10-21T17:39'),
            fulfillment: {
              case: {
                id: 'mockCaseIdA',
                displayId: 'C00012456',
                program: {
                  id: 'mockProgramId',
                  name: 'Housing Assistance Program',
                  features: [],
                },
              },
              fulfillmentMeta: { checkNumber: '632759', billingCode: 'EXT-123' },
            },
          },
        ],
        pageInfo: { count: 3 },
      },
    },
  },
};

const VENDORS_LIST_MOCK = {
  request: {
    query: VENDORS_LIST,
    variables: {
      filter: { search: '' },
      pagination: { page: 0, take: 10 },
      sort: { column: 'Name', direction: 'Ascending' },
    },
  },
  result: {
    data: {
      vendors: {
        nodes: [
          {
            id: 'mockVendorA',
            name: 'Brighter Days Cleaning',
            mailingAddress: {
              addressLine1: '650 S Town Center Dr',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89106,
            },
            types: [{ name: 'Cleaning Services' }],
            externalId: 'EXT1234',
            email: '<EMAIL>',
            phone: '+***********',
            aggregatePayments: { count: 10 },
          },
          {
            id: 'mockVendorB',
            name: 'Evergreen Landscaping',
            mailingAddress: {
              addressLine1: '2211 Rainbow Blvd',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89108,
            },
            types: [{ name: 'Landscaping' }],
            externalId: 'EXT9876',
            email: '<EMAIL>',
            phone: '+17025557890',
            aggregatePayments: { count: 24 },
          },
          {
            id: 'mockVendorC',
            name: 'Summit Property Management',
            mailingAddress: {
              addressLine1: '8465 W Sahara Ave',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89117,
            },
            types: [{ name: 'Property Management' }],
            externalId: 'EXT5432',
            email: '<EMAIL>',
            phone: '+17027778888',
            aggregatePayments: { count: 36 },
          },
          {
            id: 'mockVendorD',
            name: 'Desert Plumbing Solutions',
            mailingAddress: {
              addressLine1: '4325 E Craig Rd',
              city: 'North Las Vegas',
              state: 'NV',
              zip: 89030,
            },
            types: [{ name: 'Plumbing' }, { name: 'Maintenance' }],
            externalId: 'EXT6789',
            email: '<EMAIL>',
            phone: '+17022223344',
            aggregatePayments: { count: 18 },
          },
          {
            id: 'mockVendorE',
            name: 'Ace Security Systems',
            mailingAddress: {
              addressLine1: '5757 Wayne Newton Blvd',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89119,
            },
            types: [{ name: 'Security' }],
            externalId: 'EXT3344',
            email: '<EMAIL>',
            phone: '+17028889999',
            aggregatePayments: { count: 12 },
          },
          {
            id: 'mockVendorF',
            name: 'Valley HVAC Experts',
            mailingAddress: {
              addressLine1: '3333 S Maryland Pkwy',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89169,
            },
            types: [{ name: 'HVAC' }, { name: 'Maintenance' }],
            externalId: 'EXT7788',
            email: '<EMAIL>',
            phone: '+17024569870',
            aggregatePayments: { count: 45 },
          },
          {
            id: 'mockVendorG',
            name: 'Golden State Painting',
            mailingAddress: {
              addressLine1: '1420 E Sunset Rd',
              city: 'Henderson',
              state: 'NV',
              zip: 89014,
            },
            types: [{ name: 'Painting' }, { name: 'Renovation' }],
            externalId: 'EXT5151',
            email: '<EMAIL>',
            phone: '+17023334422',
            aggregatePayments: { count: 27 },
          },
          {
            id: 'mockVendorH',
            name: 'Silver State Electrical',
            mailingAddress: {
              addressLine1: '7720 S Jones Blvd',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89139,
            },
            types: [{ name: 'Electrical' }],
            externalId: 'EXT2468',
            email: '<EMAIL>',
            phone: '+17026669999',
            aggregatePayments: { count: 32 },
          },
          {
            id: 'mockVendorI',
            name: 'Desert Waste Management',
            mailingAddress: {
              addressLine1: '1200 N Nellis Blvd',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89110,
            },
            types: [{ name: 'Waste Management' }],
            externalId: 'EXT1357',
            email: '<EMAIL>',
            phone: '+17025553322',
            aggregatePayments: { count: 56 },
          },
          {
            id: 'mockVendorJ',
            name: 'Blue Sky Pest Control',
            mailingAddress: {
              addressLine1: '5050 S Durango Dr',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89113,
            },
            types: [{ name: 'Pest Control' }],
            externalId: 'EXT9090',
            email: '<EMAIL>',
            phone: '+17027779955',
            aggregatePayments: { count: 16 },
          },
        ],
        pageInfo: { count: 10 },
      },
    },
  },
};

export {
  CAN_ACCESS_PARTNER_MOCK,
  CAN_ACCESS_VENDOR_MOCK,
  VENDOR_MOCK,
  VENDORS_LIST_MOCK,
  VENDOR_PAYMENTS_MOCK,
};

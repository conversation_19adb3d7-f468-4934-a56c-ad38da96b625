import { getSelectedFund } from '@/app/_utils/payments';
import CanAccess from '@/app/components/features/CanAccess';
import useForm, {
  addRepeated,
  removeRepeated,
  setRepeated,
  SimpleFieldUpdateAction,
  updateRepeated,
  ValidationMode,
} from '@/app/hooks/useForm';
import { FulfillmentContext } from '@/spa-legacy/common/hooks/contexts/useFulfillment';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { dollarsToCents } from '@/spa-legacy/common/utilities/currency';
import { sum } from '@/spa-legacy/common/utilities/math';
import FundEquation from '@/spa-legacy/portal/components/RequestSummary/FundEquation';
import {
  buildFormValidation,
  convertPaymentToFormData,
  convertToFormData,
} from '@/spa-legacy/portal/components/RequestSummary/utils/formData';
import { fixFundStats } from '@/spa-legacy/portal/components/RequestSummary/utils/payment';
import {
  PaymentFormData,
  RequestSummaryFormData,
} from '@/spa-legacy/portal/components/RequestSummary/utils/types';
import useCase from '@/spa-legacy/portal/hooks/useCase';
import { checkProgramHasAnyFeature } from '@/spa-legacy/utilities/checkFeature';
import { checkFeature, checkFeatures } from '@bybeam/platform-lib/features/check';
import sort from '@bybeam/platform-lib/utilities/sort';
import { CaseStatus, FeatureName, Fulfillment } from '@bybeam/platform-types';
import { InfoCircledIcon, PlusIcon } from '@radix-ui/react-icons';
import { Button, Callout, Heading } from '@radix-ui/themes';
import { Flex, Text } from '@radix-ui/themes';
import { useEffect } from 'react';
import styles from './CorePayments.module.css';
import Payment from './components/Payment';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { usePostHog } from 'posthog-js/react';

interface CorePaymentsProps {
  onAddVendor: () => void;
}

export default function CorePayments({ onAddVendor }: CorePaymentsProps): JSX.Element {
  const program = useProgram();
  const case_ = useCase();
  const postHog = usePostHog();

  if (!case_) {
    return <>Failed to load case details</>;
  }

  if (!program) {
    return <>Failed to load program details</>;
  }

  const fulfillments = case_.fulfillments ?? [];
  const funds = fixFundStats(case_.program.funds, fulfillments);
  const canAddVendors = checkFeature(program?.features, FeatureName.PaymentsVendors);
  const isRecurring = checkFeature(program?.features, FeatureName.PaymentsRecurring);

  const { formData, dispatch } = useForm<RequestSummaryFormData>(
    convertToFormData(program, case_),
    {
      payments: {
        type: 'repeated',
        required: true,
        schema: buildFormValidation(
          program,
          case_,
          false /* skipBalanceValidation */,
          !postHog.isFeatureEnabled(
            POSTHOG_FEATURE_FLAGS.paymentMailingAddress,
          ) /** skipAddressValidation */,
        ),
      },
    },
    ValidationMode.RequiredOnSubmit,
  );

  const fundIds = Array.from(new Set(formData?.payments?.map(({ fund }) => fund)));
  const selectedFunds = sort(
    fundIds
      // If there are >1 payments, and 1 has no fund selected, there will be an "undefined" in the list
      ?.filter((fundId) => fundIds.length <= 1 || !!fundId)
      ?.map((fund) => getSelectedFund(fund, funds)),
    { accessor: (fund) => fund?.name },
  );

  const isCaseEditable = [
    CaseStatus.Approved,
    CaseStatus.InReview,
    CaseStatus.ReadyForReview,
    CaseStatus.Incomplete,
    CaseStatus.InProgress,
  ].includes(case_.status);

  // biome-ignore lint/correctness/useExhaustiveDependencies: We only want to update if case is changed
  useEffect(() => {
    if (!formData.payments) return;
    const casePayments = convertToFormData(program, case_).payments;
    for (const [idx, curPayment] of formData.payments.entries()) {
      const updatedRecord = casePayments.find(
        (each) => !!curPayment.fulfillmentId && each.fulfillmentId === curPayment.fulfillmentId,
      );
      if (updatedRecord) {
        // console.log('Updating ...', idx, updatedRecord);
        dispatch(setRepeated('payments', idx, updatedRecord));
      }
    }
  }, [dispatch, program, case_]);

  return (
    <Flex direction="column" gap="2">
      <Flex direction="column" gap="4" px="4" py="4" className={styles.CorePaymentsContainer}>
        <Heading size="3">Payment Details</Heading>
        {case_?.status !== CaseStatus.PaymentSent &&
          !checkFeature(program?.features, FeatureName.PaymentsMultiPayment) &&
          checkProgramHasAnyFeature(program, [
            FeatureName.PaymentsClaimFunds,
            FeatureName.PaymentsPartnerIssued,
          ]) && (
            <Flex maxWidth="46rem" asChild>
              <Callout.Root>
                <Callout.Icon>
                  <InfoCircledIcon />
                </Callout.Icon>
                <Callout.Text>
                  After saving the required information below, select the Initiate Payment button at
                  the top of the page.
                </Callout.Text>
              </Callout.Root>
            </Flex>
          )}
        {case_?.status !== CaseStatus.PaymentSent &&
          checkFeatures(program?.features, [
            FeatureName.PaymentsPartnerIssued,
            FeatureName.PaymentsMultiPayment,
          ]) && (
            <Flex maxWidth="46rem" asChild>
              <Callout.Root>
                <Callout.Icon>
                  <InfoCircledIcon />
                </Callout.Icon>
                <Callout.Text>
                  Once all payments have been initiated, you will be able to select the “Mark as
                  Done” button to update the case status to "Payment Sent".
                </Callout.Text>
              </Callout.Root>
            </Flex>
          )}
        {!!selectedFunds?.length &&
          selectedFunds.map((selectedFund) => {
            const approvedAmounts = formData?.payments
              ?.filter(({ fund }) => fund === selectedFund?.id)
              ?.map(({ approvedAmount, count }) =>
                isRecurring
                  ? dollarsToCents((Number(approvedAmount) * Number(count)).toString())
                  : dollarsToCents(approvedAmount),
              );
            return (
              <Flex direction="column" gap="1" key={selectedFund?.id ?? 'fund'}>
                {selectedFunds.length > 1 && <Text weight="bold">{selectedFund?.name}</Text>}
                <FundEquation fund={selectedFund} approvedAmount={sum(approvedAmounts)} />
              </Flex>
            );
          })}
      </Flex>
      <Flex direction="column" gap="2">
        {formData?.payments?.map((payment, idx) => {
          return (
            <FulfillmentContext.Provider
              key={payment.id}
              value={
                fulfillments.find(({ payments = [] }) =>
                  payments.some(({ id }) => id === payment.id),
                ) as Fulfillment
              }
            >
              <Payment
                formData={payment}
                onRemove={() => dispatch(removeRepeated('payments', idx))}
                onUpdate={(data: PaymentFormData) => dispatch(setRepeated('payments', idx, data))}
                onUpdateField={(action: SimpleFieldUpdateAction<PaymentFormData>) =>
                  dispatch(
                    updateRepeated('payments', idx, action.payload.field, action.payload.value),
                  )
                }
                totalCount={formData.payments.length}
              />
            </FulfillmentContext.Provider>
          );
        })}
        <Flex gap="4" align="center" justify="end" mb="2">
          {canAddVendors && (
            <CanAccess resource={{ objectId: program.id, objectType: 'PROGRAM' }}>
              <Button
                variant="ghost"
                size="2"
                onClick={() => {
                  onAddVendor();
                }}
              >
                <PlusIcon /> Add new vendor
              </Button>
            </CanAccess>
          )}
          {(checkFeatures(program?.features, [
            FeatureName.PaymentsExternalTracking,
            FeatureName.PaymentsMultiPayment,
          ]) ||
            (checkFeatures(program?.features, [
              FeatureName.PaymentsPartnerIssued,
              FeatureName.PaymentsMultiPayment,
            ]) &&
              isCaseEditable)) && (
              <CanAccess resource={{ objectId: program.id, objectType: 'PROGRAM' }}>
                <Button
                  onClick={() =>
                    dispatch(addRepeated('payments', convertPaymentToFormData(program, case_)))
                  }
                  size="2"
                  variant="outline"
                >
                  <PlusIcon /> Add additional payment
                </Button>
              </CanAccess>
            )}
        </Flex>
      </Flex>
    </Flex>
  );
}

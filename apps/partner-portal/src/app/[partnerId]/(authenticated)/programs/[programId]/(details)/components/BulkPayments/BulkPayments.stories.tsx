import { Meta, StoryObj } from '@storybook/nextjs';
import BulkPayments from './BulkPayments';
import {
  GET_FULFILLMENTS_EMPTY_MOCK,
  GET_FULFILLMENTS_MOCK,
  GET_FULFILLMENTS_LARGE_MOCK,
  BULK_ISSUE_FUNDS_SUCCESS_MOCK,
  BULK_ISSUE_FUNDS_ERROR_MOCK,
  BULK_APPROVE_CASE_PAYMENTS_SUCCESS_MOCK,
  BULK_APPROVE_CASE_PAYMENTS_ERROR_MOCK,
} from './BulkPayments.mock';

// Mock CSV content for testing
const mockCSVContent = `fulfillmentId,caseId
fulfillment-1,case-1
fulfillment-2,case-2
fulfillment-3,case-3`;
const mockEmptyCSV = 'fulfillmentId,caseId';

// Mock FileReader for Storybook
const mockFileReader = (content: string) => {
  const originalFileReader = window.FileReader;

  window.FileReader = class MockFileReader {
    onload: ((event: ProgressEvent<FileReader>) => void) | null = null;
    result: string | ArrayBuffer | null = content;

    readAsText() {
      setTimeout(() => {
        if (this.onload) {
          this.onload({ target: this } as unknown as ProgressEvent<FileReader>);
        }
      }, 100);
    }
  } as unknown as typeof FileReader;

  return () => {
    window.FileReader = originalFileReader;
  };
};

const meta: Meta<typeof BulkPayments> = {
  component: BulkPayments,

  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
The BulkPayments component allows administrators to upload a CSV file containing fulfillment IDs
and initiate payments for multiple cases at once. It supports:

- CSV file upload with fulfillment ID validation
- Real-time data fetching and display
- Bulk payment processing with error handling
- Progress tracking and status updates
- Pagination for large datasets

**Usage:**
1. Click "Get started" to open the dialog
2. Upload a CSV file with fulfillment IDs
3. Review the loaded fulfillments
4. Click "Initiate Payment" to process all payments
        `,
      },
    },
    nextjs: {
      appDirectory: true,
      navigation: {
        segments: [
          ['partnerId', 'mock-partner-id'],
          ['programId', 'mock-program-id'],
        ],
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof BulkPayments>;


export const Default: Story = {
  parameters: {
    apolloClient: {
      mocks: [GET_FULFILLMENTS_EMPTY_MOCK, GET_FULFILLMENTS_MOCK, BULK_ISSUE_FUNDS_SUCCESS_MOCK, BULK_APPROVE_CASE_PAYMENTS_SUCCESS_MOCK],
    },
    docs: {
      description: {
        story:
          'Default state showing the bulk payments trigger button and basic functionality with file upload simulation.',
      },
    },
  },
  play: async () => {
    // Mock FileReader to simulate CSV file upload
    mockFileReader(mockCSVContent);
  },
};

export const WithErrors: Story = {
  parameters: {
    apolloClient: {
      mocks: [
        GET_FULFILLMENTS_EMPTY_MOCK,
        GET_FULFILLMENTS_MOCK,
        BULK_ISSUE_FUNDS_ERROR_MOCK,
        BULK_APPROVE_CASE_PAYMENTS_ERROR_MOCK,
      ],
    },
    docs: {
      description: {
        story:
          'Shows error handling when bulk operations fail. Errors are displayed with trash icons to remove problematic rows. Uses CSV with some invalid fulfillment IDs.',
      },
    },
  },
  play: async () => {
    // Mock FileReader to simulate CSV file upload with some invalid IDs
    mockFileReader(mockCSVContent);
  },
};

export const EmptyState: Story = {
  parameters: {
    apolloClient: {
      mocks: [GET_FULFILLMENTS_EMPTY_MOCK],
    },
    docs: {
      description: {
        story:
          'Shows the state when no fulfillments are found for the uploaded CSV file. Uses an empty CSV file.',
      },
    },
  },
  play: async () => {
    // Mock FileReader to simulate empty CSV file upload
    mockFileReader(mockEmptyCSV);
  },
};

export const Loading: Story = {
  parameters: {
    apolloClient: {
      mocks: [
        GET_FULFILLMENTS_EMPTY_MOCK,
        {
          ...GET_FULFILLMENTS_MOCK,
          delay: 4000,
        },
        BULK_ISSUE_FUNDS_SUCCESS_MOCK,
        BULK_APPROVE_CASE_PAYMENTS_SUCCESS_MOCK,
      ],
    },
    docs: {
      description: {
        story:
          'Shows the loading state with skeleton components while data is being fetched. File upload is simulated.',
      },
    },
  },
  play: async () => {
    // Mock FileReader to simulate CSV file upload during loading
    mockFileReader(mockCSVContent);
  },
};

export const LargeDataset: Story = {
  parameters: {
    apolloClient: {
      mocks: [GET_FULFILLMENTS_EMPTY_MOCK, GET_FULFILLMENTS_LARGE_MOCK, BULK_ISSUE_FUNDS_SUCCESS_MOCK, BULK_APPROVE_CASE_PAYMENTS_SUCCESS_MOCK],
    },
    docs: {
      description: {
        story:
          'Demonstrates pagination functionality with a large dataset of 50 fulfillments, showing 15 per page. File upload simulates a large CSV.',
      },
    },
  },
  play: async () => {
    // Mock FileReader to simulate large CSV file upload
    const largeMockCSV = `fulfillmentId,caseId\n${Array.from(
      { length: 50 },
      (_, i) => `fulfillment-${i + 1},case-${i + 1}`,
    ).join('\n')}`;
    mockFileReader(largeMockCSV);
  },
};

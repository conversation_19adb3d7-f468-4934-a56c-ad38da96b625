import { Role } from '@/app/_utils/roles';
import { mock, mockPile } from '@/tests/mocks/apolloClient';
import { HAS_ACCESS_CASE_EDIT_MOCK } from '@/tests/mocks/hasAccessMocks';
import { Meta, StoryObj } from '@storybook/nextjs';
import GetAccessRequestsQuery from './GetAccessRequests.graphql';
import RequestAccess from './index';

const meta: Meta = { 
  component: RequestAccess,
  parameters: {
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/partner/mockPartnerId/programs/mock-program-id/cases/mock-case-id',
        segments: [
          ['mockPartnerId', 'partner'],
          ['mock-program-id', 'programs'],
          ['mock-case-id', 'cases'],
        ],
      },
    },
  },
};
export default meta;

type Story = StoryObj<typeof RequestAccess>;

export const ViewOnly: Story = {
  args: {
    caseId: 'mock-case-id',
  },
  parameters: {
    roles: [Role.ViewOnly],
    canAccess: false,
    postHogClient: {
      isFeatureEnabled: () => true,
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_CASE_EDIT_MOCK,
        ...mockPile([
          mock(
            GetAccessRequestsQuery,
            {
              accessRequests: { accessRequests: [], pageInfo: { count: 0 } },
            },
            {
              filter: {
                partnerId: 'mockPartnerId',
                resource: { objectId: 'mock-case-id', objectType: 'CASE' },
                subject: { objectId: undefined, objectType: 'USER' },
              },
            },
          ),
        ]),
      ],
    },
  },
};

export const UnreviewedRequest: Story = {
  args: {
    caseId: 'mock-case-id',
  },
  parameters: {
    roles: [Role.ViewOnly],
    canAccess: false,
    postHogClient: {
      isFeatureEnabled: () => true,
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_CASE_EDIT_MOCK,
        ...mockPile([
          mock(
            GetAccessRequestsQuery,
            {
              accessRequests: {
                accessRequests: [{ id: 'mockAccessRequestId', createdAt: new Date('2020-10-20') }],
                pageInfo: { count: 0 },
              },
            },
            {
              filter: {
                partnerId: 'mockPartnerId',
                resource: { objectId: 'mock-case-id', objectType: 'CASE' },
                subject: { objectId: undefined, objectType: 'USER' },
              },
            },
          ),
        ]),
      ],
    },
  },
};

export const DeniedRequest: Story = {
  args: {
    caseId: 'mock-case-id',
  },
  parameters: {
    roles: [Role.ViewOnly],
    canAccess: false,
    postHogClient: {
      isFeatureEnabled: () => true,
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_CASE_EDIT_MOCK,
        ...mockPile([
          mock(
            GetAccessRequestsQuery,
            {
              accessRequests: {
                accessRequests: [
                  {
                    id: 'mockAccessRequestId',
                    approved: false,
                    createdAt: new Date('2020-10-20'),
                    updatedAt: new Date('2020-10-20'),
                  },
                ],
                pageInfo: { count: 0 },
              },
            },
            {
              filter: {
                partnerId: 'mockPartnerId',
                resource: { objectId: 'mock-case-id', objectType: 'CASE' },
                subject: { objectId: undefined, objectType: 'USER' },
              },
            },
          ),
        ]),
      ],
    },
  },
};

export const DeniedRequestWithReason: Story = {
  args: {
    caseId: 'mock-case-id',
  },
  parameters: {
    roles: [Role.ViewOnly],
    canAccess: false,
    postHogClient: {
      isFeatureEnabled: () => true,
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_CASE_EDIT_MOCK,
        ...mockPile([
          mock(
            GetAccessRequestsQuery,
            {
              accessRequests: {
                accessRequests: [
                  {
                    id: 'mockAccessRequestId',
                    approved: false,
                    createdAt: new Date('2020-10-20'),
                    updatedAt: new Date('2020-10-20'),
                    reviewDetail: 'It is not your job to edit cases',
                  },
                ],
                pageInfo: { count: 0 },
              },
            },
            {
              filter: {
                partnerId: 'mockPartnerId',
                resource: { objectId: 'mock-case-id', objectType: 'CASE' },
                subject: { objectId: undefined, objectType: 'USER' },
              },
            },
          ),
        ]),
      ],
    },
  },
};

import Accordion from '@/spa-legacy/common/components/Accordion';
import Markdown from '@/spa-legacy/common/components/Markdown';
import { isQuestionEnabled } from '@/spa-legacy/utilities/application/question';
import {
  ApplicationAnswers,
  ApplicationConfig,
  ApplicationQuestion,
  ApplicationQuestionGroup,
  FieldValidation,
  ProgramApplicationConfiguration,
  WelcomeIntroPage,
} from '@bybeam/platform-types';
import { FileTextIcon, MixIcon, ShuffleIcon } from '@radix-ui/react-icons';
import {
  Box,
  Button,
  Dialog,
  Flex,
  Heading,
  IconButton,
  Table,
  Text,
  Tooltip,
} from '@radix-ui/themes';
import React from 'react';
import styles from './AppConfigSections.module.css';

const formatKey = (key: string) => {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/\.|(^.)/g, (str, offset) => {
      if (str === '.') {
        return ' > ';
      }
      return offset === 0 ? str.toUpperCase() : str;
    })
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

interface AppConfigSectionsProps {
  config: ProgramApplicationConfiguration;
}

interface StickyHeaderProps {
  children: React.ReactNode;
}

const StickyHeader: React.FC<StickyHeaderProps> = ({ children }) => {
  return (
    <Flex className={styles.StickyHeader} py="2" px="4">
      {children}
    </Flex>
  );
};

const AppConfigIntroPages: React.FC<{ config: ProgramApplicationConfiguration }> = ({ config }) => {
  const introPage = config.configuration.introPages[0] as WelcomeIntroPage;
  return (
    <Flex direction="column">
      <StickyHeader>
        <Heading size="3">Application Intro Pages</Heading>
      </StickyHeader>
      <Box px="4" pt="2" pb="3">
        <Text>
          <i>Intro pages appear at the beginning of the application.</i>
        </Text>
        <ul>
          <li>
            <Flex gap="3" align="center">
              <Text>{introPage.title}</Text>
              <Dialog.Root>
                <Dialog.Trigger>
                  <IconButton variant="ghost" title={`View ${introPage.title} intro page details`}>
                    <FileTextIcon />
                  </IconButton>
                </Dialog.Trigger>
                <Dialog.Content>
                  <Dialog.Title>{introPage.title}</Dialog.Title>
                  <Box className={styles.IntroPageOverview}>
                    <Markdown>{introPage.body}</Markdown>
                  </Box>
                  <Text size="1">
                    This intro page is using the <strong>{introPage.layout}</strong> configuration.
                  </Text>
                </Dialog.Content>
              </Dialog.Root>
            </Flex>
          </li>
        </ul>
      </Box>
    </Flex>
  );
};

const AppConfigSectionList: React.FC<{ configuration: ApplicationConfig }> = ({
  configuration,
}) => (
  <>
    <StickyHeader>
      <Heading size="3">Application Sections</Heading>
    </StickyHeader>
    <Box px="4" py="2" maxWidth="920px">
      <ol>
        {configuration.sections.map((section) => {
          return (
            <Box asChild mb="3" key={section.key}>
              <li>
                <Flex asChild align="center" gap="3" py="1">
                  <Heading size="3">
                    {section.name}
                    <Dialog.Root>
                      <Dialog.Trigger>
                        <IconButton
                          variant="ghost"
                          title={`View section details for ${section.name}`}
                        >
                          <FileTextIcon />
                        </IconButton>
                      </Dialog.Trigger>
                      <Dialog.Content>
                        <Dialog.Title>{section.name}</Dialog.Title>
                        <Dialog.Description mb="2">
                          <Text size="2">
                            The following content will be shown to applicants at the beginning of
                            this section.
                          </Text>
                        </Dialog.Description>
                        <Box className={styles.SectionDetailsContent}>
                          <Heading size="3" mb="2">
                            {section.overview.title}
                          </Heading>
                          <Markdown>{section.overview.description}</Markdown>
                        </Box>
                      </Dialog.Content>
                    </Dialog.Root>
                  </Heading>
                </Flex>
                <ul>
                  {section.questionGroups.map((group) => {
                    return (
                      <li key={group.key} className={styles.questionGroupListItem}>
                        <Accordion title={group.name}>
                          <QuestionGroup group={group} />
                        </Accordion>
                      </li>
                    );
                  })}
                </ul>
              </li>
            </Box>
          );
        })}
      </ol>
    </Box>
  </>
);

const ApplicationQuestionItem: React.FC<{ question: ApplicationQuestion }> = ({ question }) => {
  const formattedQuestionKey = formatKey(question.key);

  const QuestionFeatures = () => {
    return (
      <Flex gap="1">
        {' '}
        {question.skippable ? (
          <Tooltip content="skippable">
            <ShuffleIcon tabIndex={0} className={styles.skippableIcon} />
          </Tooltip>
        ) : null}
        {question.dynamicLogic ? (
          <Tooltip content="This question has dynamic logic configured">
            <MixIcon tabIndex={0} className={styles.skippableIcon} />
          </Tooltip>
        ) : null}
      </Flex>
    );
  };

  return (
    <>
      <div className={styles.questionDetails}>
        <Dialog.Root>
          <Dialog.Trigger>
            <button
              aria-describedby={`${question.key}-question-details`}
              className={styles.questionDialogTriggerButton}
              type="button"
            >
              <Flex
                gap="1"
                align="center"
                aria-hidden="true"
                id={`${question.key}-question-details`}
              >
                <Text color="gray">{formattedQuestionKey}</Text>
                <QuestionFeatures />
              </Flex>
              <Text>{question.copy.title}</Text>
            </button>
          </Dialog.Trigger>
          <Dialog.Content size="2" width={'740px'} maxWidth={'90vw'}>
            <Dialog.Title>
              <Flex gap="1" justify="between" align="center">
                {formattedQuestionKey}
              </Flex>
            </Dialog.Title>
            <Dialog.Description>
              <Flex direction="column" mb="3">
                <Heading size="3" mb="1">
                  Question Overview
                </Heading>
                <Flex direction="column" gap="1">
                  <Box p="2" className={styles.questionPrompt}>
                    <Heading size="1">Quesiton Prompt</Heading>
                    <Text size="2">{question.copy.title}</Text>
                  </Box>
                  <Box p="2" className={styles.questionIntro}>
                    <Heading size="1">Supporting content</Heading>
                    {question.copy.intro ? (
                      <Markdown>{question.copy.intro}</Markdown>
                    ) : (
                      <Text size="2">
                        No additional supporting content is confugred for this question
                      </Text>
                    )}
                  </Box>
                </Flex>
              </Flex>
            </Dialog.Description>
            <Heading size="3" mb="1">
              Question Fields
            </Heading>
            <Table.Root size="1" mb="4" variant="surface">
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeaderCell>Field name</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Type</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Dynamic logic</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Validation rules</Table.ColumnHeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {question.fields.map((field) => {
                  const formattedFieldKey = formatKey(field.key);
                  const fieldValidation: FieldValidation | null =
                    'validation' in field ? (field.validation as FieldValidation) : null;
                  return (
                    <Table.Row key={field.key}>
                      <Table.RowHeaderCell>{formattedFieldKey}</Table.RowHeaderCell>
                      <Table.Cell>{field.type ? formatKey(field.type) : null}</Table.Cell>
                      <Table.Cell>{field.dynamicLogic ? 'Yes' : 'None'}</Table.Cell>
                      <Table.Cell>{fieldValidation ? 'Yes' : 'None'}</Table.Cell>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table.Root>
            <Dialog.Close>
              <Flex justify="end">
                <Button type="button" variant="outline">
                  Close
                </Button>
              </Flex>
            </Dialog.Close>
          </Dialog.Content>
        </Dialog.Root>
      </div>
    </>
  );
};

const QuestionGroup: React.FC<{ group: ApplicationQuestionGroup }> = ({ group }) => {
  return (
    <div>
      <Heading size="2" mb="1">
        Question Group Overview
      </Heading>
      <Box p="2" mb="2" className={styles.questionGroupOverview}>
        {group.dynamicLogic ? (
          <div>
            <strong>This question group relies on dynamic logic</strong>
          </div>
        ) : null}
        {group.overview ? (
          <>
            <Text>{group.overview?.title}</Text>
            <div>
              <Text>{group.overview?.description}</Text>
            </div>
          </>
        ) : (
          <Text>This question group does not have an overview.</Text>
        )}
      </Box>
      <Heading size="2" mb="1">
        Questions
      </Heading>
      <ul>
        {group.questions
          .filter((question) => isQuestionEnabled(question, [] as Partial<ApplicationAnswers>))
          .map((question) => {
            return (
              <li key={question.key} className={styles.questionListItem}>
                <ApplicationQuestionItem question={question} />
              </li>
            );
          })}
      </ul>
    </div>
  );
};

const AppConfigSections: React.FC<AppConfigSectionsProps> = ({ config }) => {
  const { configuration } = config;
  return (
    <>
      <AppConfigIntroPages config={config} />
      <hr className={styles.headingDivider} />
      <AppConfigSectionList configuration={configuration} />
    </>
  );
};

export default AppConfigSections;

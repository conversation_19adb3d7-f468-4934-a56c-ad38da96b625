import GetProgramFundsQuery from './page.graphql';
import GetPartnerFundsQuery from './_components/AddProgramFundDialog/GetPartnerFunds.graphql';
import HasAccessQuery from '../../../../../../../providers/HasAccessQuery.graphql';

export const GET_PROGRAM_FUNDS = {
  request: {
    query: GetProgramFundsQuery,
    variables: {
      programId: undefined,
    },
  },
  result: {
    loading: false,
    data: {
      programs: {
        programs: [
          {
            id: 'mock-program-id',
            name: 'Mock Program Name',
            stats: {
              programFundStats: [{ fundId: 'mockFundIdI', awardedBalance: 5000, paymentCount: 0 }],
            },
            funds: [
              {
                id: 'mockFundIdI',
                name: 'Fund I',
                startingBalance: 1000000,
                stats: {
                  obligatedBalance: 30000,
                  remainingBalance: 965000,
                },
              },
            ],
          },
        ],
      },
    },
  },
};

export const GET_PARTNER_FUNDS = {
  request: {
    query: GetPartnerFundsQuery,
  },
  result: {
    loading: false,
    data: {
      funds: {
        nodes: [
          {
            id: 'mockFundIdI',
            name: 'Fund I',
            config: {
              programId: 'mockProgramId',
              usioKey: 'testUsioKey',
              id: 'mockFundingSourceI',
            },
            stats: {
              remainingBalance: 99619100,
            },
          },
          {
            id: 'mockFundIdII',
            name: 'Fund II',
            config: {
              programId: 'mockProgramId',
              usioKey: 'testUsioKey',
              id: 'mockFundingSourceII',
            },
            stats: {
              remainingBalance: 100000,
            },
          },
        ],
      },
    },
  },
};

export const HAS_ACCESS_QUERY = {
  request: {
    query: HasAccessQuery,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'ORGANIZATION', objectId: 'mockPartnerId' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        canAccess: true,
      },
    },
  },
  newData: () => ({
    data: {
      hasAccess: {
        canAccess: true,
      },
    },
  }),
};

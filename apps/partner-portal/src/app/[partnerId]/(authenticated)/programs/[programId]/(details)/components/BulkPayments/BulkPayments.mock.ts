import { PaymentMethod, PaymentStatus, ScheduleType } from '@bybeam/platform-types';
import BulkIssueFunds from './BulkIssueFunds.graphql';
import BulkApproveCasePayments from './BulkApproveCasePayments.graphql';
import GetFulfillments from './GetFulfillments.graphql';

// Mock data for fulfillments
const mockFulfillments = [
  {
    id: 'fulfillment-1',
    scheduleType: ScheduleType.OneTime,
    approvedAmount: 150000,
    fund: {
      id: 'fund-1',
      name: 'Emergency Relief Fund',
    },
    case: {
      displayId: 'CASE-001',
      name: '<PERSON>',
    },
    payments: [
      {
        id: 'payment-1',
        status: PaymentStatus.Pending,
        method: PaymentMethod.DirectDeposit,
        payeeType: 'User',
        payee: {
          id: 'user-1',
          name: '<PERSON>',
          payeeType: 'User',
          applicantProfile: {
            id: 'profile-1',
            applicantType: {
              id: 'type-1',
            },
          },
        },
      },
    ],
  },
  {
    id: 'fulfillment-2',
    scheduleType: ScheduleType.Recurring,
    approvedAmount: 250000, // $2,500.00 in cents
    fund: {
      id: 'fund-2',
      name: 'Housing Assistance Fund',
    },
    case: {
      displayId: 'CASE-002',
      name: 'Jane Smith',
    },
    payments: [
      {
        id: 'payment-2',
        status: PaymentStatus.Success,
        method: PaymentMethod.Check,
        payeeType: 'Vendor',
        payee: {
          id: 'vendor-1',
          name: 'ABC Property Management',
          payeeType: 'Vendor',
        },
      },
    ],
  },
  {
    id: 'fulfillment-3',
    scheduleType: ScheduleType.OneTime,
    approvedAmount: 75000, // $750.00 in cents
    fund: {
      id: 'fund-1',
      name: 'Emergency Relief Fund',
    },
    case: {
      displayId: 'CASE-003',
      name: 'Jane Smith',
    },
    payments: [
      {
        id: 'payment-3',
        status: PaymentStatus.Failed,
        method: PaymentMethod.DirectDeposit,
        payeeType: 'User',
        payee: {
          id: 'user-2',
          name: 'Jane Smith',
          payeeType: 'User',
          applicantProfile: {
            id: 'profile-2',
            applicantType: {
              id: 'type-1',
            },
          },
        },
      },
    ],
  },
];

// Mock for empty filter (initial state)
export const GET_FULFILLMENTS_EMPTY_MOCK = {
  request: {
    query: GetFulfillments,
    variables: {
      filter: {
        ids: [],
      },
      pagination: { page: 0, take: 15 },
    },
  },
  result: {
    data: {
      fulfillments: {
        pageInfo: {
          count: 0,
          nextCursor: null,
          verifiedIds: [],
        },
        fulfillments: [],
      },
    },
  },
  newData: () => ({
    data: {
      fulfillments: {
        pageInfo: {
          count: 0,
          nextCursor: null,
          verifiedIds: [],
        },
        fulfillments: [],
      },
    },
  }),
};

// Mock for populated fulfillments
export const GET_FULFILLMENTS_MOCK = {
  request: {
    query: GetFulfillments,
    variables: {
      filter: {
        ids: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
      },
      pagination: { page: 0, take: 15 },
    },
  },
  result: {
    data: {
      fulfillments: {
        pageInfo: {
          count: 3,
          nextCursor: null,
          verifiedIds: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
        },
        fulfillments: mockFulfillments,
      },
    },
  },
  newData: () => ({
    data: {
      fulfillments: {
        pageInfo: {
          count: 3,
          nextCursor: null,
          verifiedIds: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
        },
        fulfillments: mockFulfillments,
      },
    },
  }),
};

// Mock for large dataset
export const GET_FULFILLMENTS_LARGE_MOCK = {
  request: {
    query: GetFulfillments,
    variables: {
      filter: {
        ids: Array.from({ length: 50 }, (_, i) => `fulfillment-${i + 1}`),
      },
      pagination: { page: 0, take: 15 },
    },
  },
  result: {
    data: {
      fulfillments: {
        pageInfo: {
          count: 50,
          nextCursor: 'cursor-next',
          verifiedIds: Array.from({ length: 50 }, (_, i) => `fulfillment-${i + 1}`),
        },
        fulfillments: Array.from({ length: 15 }, (_, i) => ({
          id: `fulfillment-${i + 1}`,
          scheduleType: i % 2 === 0 ? ScheduleType.OneTime : ScheduleType.Recurring,
          approvedAmount: 50000 + (i * 30000), // Deterministic amounts from $500 to $4700
          fund: {
            id: `fund-${(i % 3) + 1}`,
            name: [
              'Emergency Relief Fund',
              'Housing Assistance Fund',
              'Utility Assistance Fund',
            ][i % 3],
          },
          case: {
            displayId: `CASE-${String(i + 1).padStart(3, '0')}`,
          },
          payments: [
            {
              id: `payment-${i + 1}`,
              status: [
                PaymentStatus.Pending,
                PaymentStatus.Success,
                PaymentStatus.Failed,
                PaymentStatus.Authorized,
              ][i % 4],
              method: [
                PaymentMethod.DirectDeposit,
                PaymentMethod.Check,
                PaymentMethod.Zelle,
              ][i % 3],
              payeeType: i % 2 === 0 ? 'User' : 'Vendor',
              payee:
                i % 2 === 0
                  ? {
                      id: `user-${i + 1}`,
                      name: `User ${i + 1}`,
                      payeeType: 'User',
                      applicantProfile: {
                        id: `profile-${i + 1}`,
                        applicantType: {
                          id: 'type-1',
                        },
                      },
                    }
                  : {
                      id: `vendor-${i + 1}`,
                      name: `Vendor ${i + 1}`,
                      payeeType: 'Vendor',
                    },
            },
          ],
        })),
      },
    },
  },
  newData: () => ({
    data: {
      fulfillments: {
        pageInfo: {
          count: 50,
          nextCursor: 'cursor-next',
          verifiedIds: Array.from({ length: 50 }, (_, i) => `fulfillment-${i + 1}`),
        },
        fulfillments: Array.from({ length: 15 }, (_, i) => ({
          id: `fulfillment-${i + 1}`,
          scheduleType: i % 2 === 0 ? ScheduleType.OneTime : ScheduleType.Recurring,
          approvedAmount: 50000 + (i * 30000), // Deterministic amounts from $500 to $4700
          fund: {
            id: `fund-${(i % 3) + 1}`,
            name: [
              'Emergency Relief Fund',
              'Housing Assistance Fund',
              'Utility Assistance Fund',
            ][i % 3],
          },
          case: {
            displayId: `CASE-${String(i + 1).padStart(3, '0')}`,
          },
          payments: [
            {
              id: `payment-${i + 1}`,
              status: [
                PaymentStatus.Pending,
                PaymentStatus.Success,
                PaymentStatus.Failed,
                PaymentStatus.Authorized,
              ][i % 4],
              method: [
                PaymentMethod.DirectDeposit,
                PaymentMethod.Check,
                PaymentMethod.Zelle,
              ][i % 3],
              payeeType: i % 2 === 0 ? 'User' : 'Vendor',
              payee:
                i % 2 === 0
                  ? {
                      id: `user-${i + 1}`,
                      name: `User ${i + 1}`,
                      payeeType: 'User',
                      applicantProfile: {
                        id: `profile-${i + 1}`,
                        applicantType: {
                          id: 'type-1',
                        },
                      },
                    }
                  : {
                      id: `vendor-${i + 1}`,
                      name: `Vendor ${i + 1}`,
                      payeeType: 'Vendor',
                    },
            },
          ],
        })),
      },
    },
  }),
};

export const BULK_ISSUE_FUNDS_SUCCESS_MOCK = {
  request: {
    query: BulkIssueFunds,
    variables: {
      input: {
        ids: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
      },
    },
  },
  result: {
    data: {
      fulfillment: {
        bulkIssueFunds: {
          metadata: {
            status: 200,
            errors: [],
          },
          records: mockFulfillments.map((f) => ({
            id: f.id,
            payments: f.payments.map((p) => ({
              id: p.id,
              status: PaymentStatus.Success,
            })),
          })),
        },
      },
    },
  },
  newData: () => ({
    data: {
      fulfillment: {
        bulkIssueFunds: {
          metadata: {
            status: 200,
            errors: [],
          },
          records: mockFulfillments.map((f) => ({
            id: f.id,
            payments: f.payments.map((p) => ({
              id: p.id,
              status: PaymentStatus.Success,
            })),
          })),
        },
      },
    },
  }),
};

export const BULK_ISSUE_FUNDS_ERROR_MOCK = {
  request: {
    query: BulkIssueFunds,
    variables: {
      input: {
        ids: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
      },
    },
  },
  result: {
    data: {
      fulfillment: {
        bulkIssueFunds: {
          metadata: {
            status: 400,
            errors: [
              {
                id: 'fulfillment-1',
                message: 'Insufficient funds in account',
              },
              {
                id: 'fulfillment-3',
                message: 'Invalid payment method',
              },
            ],
          },
          records: [],
        },
      },
    },
  },
  newData: () => ({
    data: {
      fulfillment: {
        bulkIssueFunds: {
          metadata: {
            status: 400,
            errors: [
              {
                id: 'fulfillment-1',
                message: 'Insufficient funds in account',
              },
              {
                id: 'fulfillment-3',
                message: 'Invalid payment method',
              },
            ],
          },
          records: [],
        },
      },
    },
  }),
};

export const BULK_APPROVE_CASE_PAYMENTS_SUCCESS_MOCK = {
  request: {
    query: BulkApproveCasePayments,
    variables: {
      input: {
        ids: ['case-1', 'case-2', 'case-3'],
        fulfillmentIds: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
      },
    },
  },
  result: {
    data: {
      case: {
        approvePayments: {
          metadata: {
            status: 200,
            errors: [],
          },
          records: [
            {
              id: 'case-1',
              status: 'Approved',
              assignee: { id: 'admin-1' },
              fulfillments: [mockFulfillments[0]],
            },
            {
              id: 'case-2',
              status: 'Approved',
              assignee: { id: 'admin-1' },
              fulfillments: [mockFulfillments[1]],
            },
            {
              id: 'case-3',
              status: 'Approved',
              assignee: { id: 'admin-1' },
              fulfillments: [mockFulfillments[2]],
            },
          ],
        },
      },
    },
  },
  newData: () => ({
    data: {
      case: {
        approvePayments: {
          metadata: {
            status: 200,
            errors: [],
          },
          records: [
            {
              id: 'case-1',
              status: 'Approved',
              assignee: { id: 'admin-1' },
              fulfillments: [mockFulfillments[0]],
            },
            {
              id: 'case-2',
              status: 'Approved',
              assignee: { id: 'admin-1' },
              fulfillments: [mockFulfillments[1]],
            },
            {
              id: 'case-3',
              status: 'Approved',
              assignee: { id: 'admin-1' },
              fulfillments: [mockFulfillments[2]],
            },
          ],
        },
      },
    },
  }),
};

export const BULK_APPROVE_CASE_PAYMENTS_ERROR_MOCK = {
  request: {
    query: BulkApproveCasePayments,
    variables: {
      input: {
        ids: ['case-1', 'case-2', 'case-3'],
        fulfillmentIds: ['fulfillment-1', 'fulfillment-2', 'fulfillment-3'],
      },
    },
  },
  result: {
    data: {
      case: {
        approvePayments: {
          metadata: {
            status: 400,
            errors: [
              {
                id: 'case-2',
                message: 'Case is not eligible for approval',
              },
            ],
          },
          records: [],
        },
      },
    },
  },
  newData: () => ({
    data: {
      case: {
        approvePayments: {
          metadata: {
            status: 400,
            errors: [
              {
                id: 'case-2',
                message: 'Case is not eligible for approval',
              },
            ],
          },
          records: [],
        },
      },
    },
  }),
};

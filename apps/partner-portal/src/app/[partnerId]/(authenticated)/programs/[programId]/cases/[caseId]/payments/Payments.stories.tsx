import { ProgramContext } from '@/spa-legacy/common/hooks/contexts/useProgram';
import { PARTNER, mockProgram } from '@/tests/mocks/mockData';
import { HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK } from '@/tests/mocks/hasAccessMocks';
import { FeatureName } from '@bybeam/platform-types';
import type { Meta, StoryObj } from '@storybook/nextjs';
import {
  APPLICANT_PAYMENTS_MOCK,
  CAN_ACCESS_CASE_MOCK,
  CAN_ACCESS_FISCAL_MOCK,
  CAN_ACCESS_PROGRAM_MOCK,
  CASE_PAYMENTS_APPROVED_MOCK,
  CASE_PAYMENTS_CLAIM_MOCK,
  CASE_PAYMENTS_MOCK,
  NO_PAYMENT_HISTORY_MOCK,
} from './CasePayments.mock';
import CasePayments from './page';

const meta: Meta<typeof CasePayments> = {
  component: CasePayments,
};

export default meta;
type Story = StoryObj<typeof CasePayments>;

export const Primary: Story = {
  decorators: [
    (Story) => {
      return (
        <ProgramContext.Provider value="uwkc-program">
          <Story />
        </ProgramContext.Provider>
      );
    },
  ],
  parameters: {
    apolloClient: {
      mocks: [
        HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK,
        CAN_ACCESS_CASE_MOCK,
        CAN_ACCESS_FISCAL_MOCK,
        CAN_ACCESS_PROGRAM_MOCK,
        CASE_PAYMENTS_MOCK,
        APPLICANT_PAYMENTS_MOCK,
      ],
    },
    postHogClient: {
      isFeatureEnabled: () => false,
    },
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/partner/mock-partner-id/programs/mock-program-id/cases/mock-case-id/payments',
        segments: [
          ['mock-application-id', 'applications'],
          ['mock-case-id', 'cases'],
          ['mock-program-id', 'uwkc-program'],
          ['mock-partner-id', 'baltimore'],
        ],
      },
    },
  },
};

export const ClaimFundPayments: Story = {
  decorators: [
    (Story) => {
      return (
        <ProgramContext.Provider value={'mock-program-id'}>
          <Story />
        </ProgramContext.Provider>
      );
    },
  ],
  parameters: {
    partner: {
      ...PARTNER,
      programs: [
        {
          ...mockProgram([
            FeatureName.WorkflowCore,
            FeatureName.PaymentsClaimFunds,
            FeatureName.PaymentsDirectDeposit,
          ]),
          id: 'mock-program-id',
        },
      ],
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK,
        CAN_ACCESS_FISCAL_MOCK,
        CAN_ACCESS_CASE_MOCK,
        CASE_PAYMENTS_CLAIM_MOCK,
        NO_PAYMENT_HISTORY_MOCK,
      ],
    },
    postHogClient: {
      isFeatureEnabled: () => true,
    },
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/partner/mock-partner-id/programs/mock-program-id/cases/mock-case-id/payments',
        segments: [
          ['mock-case-id', 'cases'],
          ['mock-program-id', 'programs'],
          ['mock-partner-id', 'partner'],
        ],
      },
    },
  },
};

export const PartnerIssuedMultiPayments: Story = {
  decorators: [
    (Story) => {
      return (
        <ProgramContext.Provider value={'mock-program-id'}>
          <Story />
        </ProgramContext.Provider>
      );
    },
  ],
  parameters: {
    partner: {
      ...PARTNER,
      programs: [
        {
          ...mockProgram([
            FeatureName.WorkflowCore,
            FeatureName.PaymentsMultiPayment,
            FeatureName.PaymentsPartnerIssued,
            FeatureName.PaymentsDirectDeposit,
          ]),
          id: 'mock-program-id',
        },
      ],
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK,
        CAN_ACCESS_FISCAL_MOCK,
        CAN_ACCESS_CASE_MOCK,
        CASE_PAYMENTS_APPROVED_MOCK,
        NO_PAYMENT_HISTORY_MOCK,
      ],
    },
    postHogClient: {
      isFeatureEnabled: () => true,
    },
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/partner/mock-partner-id/programs/mock-program-id/cases/mock-case-id/payments',
        segments: [
          ['mock-case-id', 'cases'],
          ['mock-program-id', 'programs'],
          ['mock-partner-id', 'partner'],
        ],
      },
    },
  },
};

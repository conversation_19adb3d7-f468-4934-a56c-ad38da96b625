import { loadingMock } from '@/tests/mocks/apolloClient';
import { gql } from '@apollo/client';
import { CaseStatus, SortDirection } from '@bybeam/platform-types';
import SearchCasesApplications from './SearchCasesApplications.graphql';

export const CASES_APPLICATIONS_MOCK = {
  request: {
    query: SearchCasesApplications,
    variables: {
      input: {
        index: 'cases_applications',
        filters: {},
        search: '',
        searchCategories: [],
      },
      pagination: {
        take: 10,
        after: undefined,
        before: undefined,
      },
      sort: {
        column: 'SubmittedAt',
        direction: SortDirection.Descending,
      },
    },
  },
  result: {
    data: {
      search: {
        pageInfo: {
          count: 5,
          endCursor: 'someEndCursor',
          startCursor: 'someStartCursor',
          hasNextPage: false,
          hasPreviousPage: false,
        },
        nodes: [
          {
            id: 'mock-case-1',
            data: {
              submitterName: '<PERSON>',
              submitterEmail: '<EMAIL>',
              submitterPhone: '************',
              caseId: 'case-123',
              caseDisplayId: '1001',
              caseName: 'Emergency Case',
              caseProgramId: 'program-abc',
              caseProgramName: 'Emergency Assistance',
              casePriority: 3,
              caseParticipantCount: 2,
              caseApplicantTypes: 'Applicant,Third Party',
              caseApplicantTypeIds: 'applicant-id,third-party-id',
              hasMissingParticipant: false,
              hasPendingLink: false,
              hasFailedLink: false,
              applicationId: 'app-123',
              applicationReferralId: 'ref-001',
              applicationSubmittedAt: '2023-05-15T10:30:00Z',
              caseTags: 'urgent,review',
              caseStatus: CaseStatus.Approved,
              assigneeName: 'Jane Smith',
              applicationVerificationScore: 0.95,
              confidence: null,
              applicationEligibility: null,
              applicationEligibilityReason: null,
            },
          },
          {
            id: 'mock-case-2',
            data: {
              submitterName: 'Alice Johnson',
              submitterEmail: '<EMAIL>',
              submitterPhone: '************',
              caseId: 'case-456',
              caseDisplayId: '1002',
              caseName: 'Housing Case',
              caseProgramId: 'program-xyz',
              caseProgramName: 'Housing Support',
              casePriority: 10,
              caseParticipantCount: 1,
              caseApplicantTypes: 'Applicant',
              caseApplicantTypeIds: 'applicant-id',
              hasMissingParticipant: true,
              hasPendingLink: false,
              hasFailedLink: false,
              applicationId: 'app-456',
              applicationReferralId: null,
              applicationSubmittedAt: '2023-05-10T14:45:00Z',
              caseTags: 'pending',
              caseStatus: CaseStatus.InReview,
              assigneeName: null,
              applicationVerificationScore: 0.85,
              confidence: null,
              applicationEligibility: null,
              applicationEligibilityReason: null,
            },
          },
          {
            id: 'mock-case-3',
            data: {
              submitterName: 'Bob Brown',
              submitterEmail: '<EMAIL>',
              submitterPhone: '************',
              caseId: 'case-789',
              caseDisplayId: '1003',
              caseName: 'Utility Case',
              caseProgramId: 'program-abc',
              caseProgramName: 'Utility Assistance',
              casePriority: 3,
              caseParticipantCount: 1,
              caseApplicantTypes: 'Applicant',
              caseApplicantTypeIds: 'applicant-id',
              hasMissingParticipant: false,
              hasPendingLink: false,
              hasFailedLink: false,
              applicationId: 'app-789',
              applicationReferralId: null,
              applicationSubmittedAt: '2023-05-10T14:45:00Z',
              caseTags: 'pending',
              caseStatus: CaseStatus.InReview,
              assigneeName: null,
              applicationVerificationScore: null,
              confidence: null,
              applicationEligibility: null,
              applicationEligibilityReason: null,
            },
          },
          {
            id: 'mock-case-4',
            data: {
              submitterName: 'Charlie Green',
              submitterEmail: '<EMAIL>',
              submitterPhone: '************',
              caseId: 'case-101',
              caseDisplayId: '1004',
              caseName: 'Food Assistance Case',
              caseProgramId: 'program-xyz',
              caseProgramName: 'Food Assistance',
              casePriority: 1,
              caseParticipantCount: 1,
              caseApplicantTypes: 'Applicant',
              caseApplicantTypeIds: 'applicant-id',
              hasMissingParticipant: false,
              hasPendingLink: true,
              hasFailedLink: false,
              applicationId: 'app-101',
              applicationReferralId: null,
              applicationSubmittedAt: '2023-05-10T14:45:00Z',
              caseTags: 'pending',
              caseStatus: CaseStatus.ReadyForReview,
              assigneeName: 'Mark Miller',
              applicationVerificationScore: null,
              confidence: 0.2,
              applicationEligibility: null,
              applicationEligibilityReason: null,
            },
          },
          {
            id: 'mock-case-5',
            data: {
              submitterName: 'David White',
              submitterEmail: '<EMAIL>',
              submitterPhone: '************',
              caseId: 'case-202',
              caseDisplayId: '1005',
              caseName: 'Healthcare Case',
              caseProgramId: 'program-abc',
              caseProgramName: 'Healthcare Assistance',
              casePriority: 10,
              caseParticipantCount: 1,
              caseApplicantTypes: 'Third Party',
              caseApplicantTypeIds: 'third-party-id',
              hasMissingParticipant: false,
              hasPendingLink: false,
              hasFailedLink: true,
              applicationId: 'app-202',
              applicationReferralId: null,
              applicationSubmittedAt: '2023-05-10T14:45:00Z',
              caseTags: 'pending',
              caseStatus: CaseStatus.ReadyForReview,
              assigneeName: null,
              applicationVerificationScore: null,
              confidence: 0.85,
              applicationEligibility: null,
              applicationEligibilityReason: null,
            },
          },
        ],
      },
    },
  },
};

export const EMPTY_CASES_APPLICATIONS_MOCK = {
  request: {
    query: SearchCasesApplications,
    variables: {
      input: {
        index: 'cases_applications',
        filters: {},
        search: '',
        searchCategories: [],
      },
      pagination: {
        take: 10,
        after: undefined,
        before: undefined,
      },
      sort: {
        column: 'SubmittedAt',
        direction: SortDirection.Descending,
      },
    },
  },
  result: {
    data: {
      search: {
        pageInfo: {
          count: 0,
          endCursor: null, // Added missing field
          startCursor: null, // Added missing field
          hasNextPage: false,
          hasPreviousPage: false,
        },
        nodes: [],
      },
    },
  },
};

// These two are required for the filter menu
export const GET_PARTNER_TAGS_MOCK = {
  request: {
    query: gql`
      query GetPartnerTags($externalId: NonEmptyString!) {
        partners(filter: {externalId: $externalId}) {
          partners {
            id
            tags {
              id
              name
            }
          }
        }
      }
    `,
    variables: {
      externalId: 'baltimore',
    },
  },
  result: {
    data: {
      partners: {
        partners: [
          {
            id: 'mockPartnerId',
            tags: [
              {
                id: 'tag1',
                name: 'Urgent',
              },
              {
                id: 'tag2',
                name: 'Follow-up',
              },
              {
                id: 'tag3',
                name: 'Pending',
              },
            ],
          },
        ],
      },
    },
  },
};

export const GET_ADMINS_MOCK = {
  request: {
    query: gql`
      query GetAdmins($page: NonNegativeInt! = 0, $take: take_Int_NotNull_min_0_max_150! = 10) {
        admins(pagination: { page: $page, take: $take }) {
          nodes {
            id
            roles
            identityUser {
              id
              name
              email
              phone
            }
          }
          pageInfo {
            count
          }
        }
      }
    `,
    variables: {
      page: 0,
      take: 150, // Changed from 10 to 150 to match the expected variables
    },
  },
  result: {
    data: {
      admins: {
        nodes: [
          {
            id: 'admin1',
            roles: ['manager'],
            identityUser: {
              id: 'user1',
              name: 'Admin 1',
              email: '<EMAIL>',
              phone: '************',
            },
          },
          {
            id: 'admin2',
            roles: ['standard'],
            identityUser: {
              id: 'user2',
              name: 'Admin 2',
              email: '<EMAIL>',
              phone: '************',
            },
          },
        ],
        pageInfo: {
          count: 2,
        },
      },
    },
  },
};

export const SEARCH_LOADING_MOCK = loadingMock(SearchCasesApplications, {
  input: {
    index: 'cases_applications',
    filters: {},
    search: '',
    searchCategories: [],
  },
  pagination: {
    take: 10,
    after: undefined,
    before: undefined,
  },
  sort: {
    column: 'SubmittedAt',
    direction: 'Descending',
  },
});

import GetPartnerFundsQuery from './page.graphql';
import HasAccessQuery from '../../../providers/HasAccessQuery.graphql';

export const FUNDS_LIST_MOCK = {
  request: {
    query: GetPartnerFundsQuery,
    variables: {
      sort: { column: 'Name', direction: 'Ascending' },
    },
  },
  result: {
    loading: false,
    data: {
      funds: {
        pageInfo: {
          count: 2,
        },
        nodes: [
          {
            id: 'mockFundIdI',
            config: {
              physicalDistributorId: 'testUsioPhysicalId',
              programId: 'mockProgramId',
              provider: 'jpmc',
              includeCheckMemo: null,
              skipACHTransfer: null,
              usioKey: 'testUsioKey',
              virtualDistributorId: 'testUsioVirtualId',
              id: 'mockFundingSourceI',
            },
            name: 'Fund I',
            startingBalance: 100000000,
            stats: {
              obligatedBalance: 330900,
              remainingBalance: 99619100,
              awardedBalance: 0,
            },
            programs: [
              {
                id: 'mockProgramId',
                name: 'Program One',
              },
              {
                id: 'mockProgramIdII',
                name: 'Program Two',
              },
            ],
          },
          {
            id: 'mockFundIdII',
            config: null,
            name: 'Fund II',
            startingBalance: 0,
            stats: {
              obligatedBalance: 0,
              remainingBalance: 0,
            },
          },
        ],
      },
    },
  },
};

export const HAS_ACCESS_QUERY = {
  request: {
    query: HasAccessQuery,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'ORGANIZATION', objectId: 'mockPartnerId' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        canAccess: true,
      },
    },
  },
  newData: () => ({
    data: {
      hasAccess: {
        canAccess: true,
      },
    },
  }),
};

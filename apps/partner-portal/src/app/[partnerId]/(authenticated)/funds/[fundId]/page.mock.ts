import GetFundQuery from './_components/FundDetails/GetFundQuery.graphql';
import HasAccessQuery from '../../../../providers/HasAccessQuery.graphql';

export const GET_FUND_MOCK = {
  request: {
    query: GetFundQuery,
    variables: {
      fundId: undefined,
    },
  },
  result: {
    loading: false,
    data: {
      funds: {
        pageInfo: {
          count: 1,
        },
        nodes: [
          {
            id: 'mockFundIdI',
            config: {
              physicalDistributorId: 'testUsioPhysicalId',
              programId: 'mockProgramId',
              provider: 'jpmc',
              skipACHTransfer: null,
              usioKey: 'testUsioKey',
              virtualDistributorId: 'testUsioVirtualId',
              id: 'mockFundingSourceI',
            },
            name: 'Fund I',
            startingBalance: 1000000,
            stats: {
              obligatedBalance: 30000,
              remainingBalance: 965000,
              awardedBalance: 5000,
            },
            programs: [
              {
                id: 'mockProgramId',
                name: 'Program One',
                stats: {
                  programFundStats: [{ fundId: 'mockFundIdI', awardedBalance: 5000 }],
                },
              },
              {
                id: 'mockProgramIdII',
                name: 'Program Two',
                stats: {
                  programFundStats: [{ fundId: 'mockFundIdI', awardedBalance: 0 }],
                },
              },
            ],
          },
        ],
      },
    },
  },
};

export const HAS_ACCESS_QUERY = {
  request: {
    query: HasAccessQuery,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'ORGANIZATION', objectId: 'mockPartnerId' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        canAccess: true,
      },
    },
  },
  newData: () => ({
    data: {
      hasAccess: {
        canAccess: true,
      },
    },
  }),
};

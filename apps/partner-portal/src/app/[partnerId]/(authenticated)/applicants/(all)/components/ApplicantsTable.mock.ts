import { loadingMock } from '@/tests/mocks/apolloClient';
import { SortDirection, UserSortColumn } from '@bybeam/platform-types';
import GetApplicantsQuery from '../graphql/GetApplicantsQuery.graphql';

export const GET_APPLICANTS_MOCK = {
  request: {
    query: GetApplicantsQuery,
    variables: {
      filter: {},
      pagination: { page: 0, take: 10 },
      sort: {
        column: UserSortColumn.Name,
        direction: SortDirection.Ascending,
      },
      includeDuplicates: true,
    },
  },
  result: {
    data: {
      users: {
        __typename: 'UserConnection',
        pageInfo: {
          __typename: 'PageInfo',
          count: 42,
        },
        users: [
          {
            __typename: 'User',
            id: 'user-1',
            displayId: 'APP-1001',
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-1',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-1',
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                city: 'San Francisco',
                state: 'CA',
                zip: '94102',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 3,
              sum: 250000,
            },
          },
          {
            __typename: 'User',
            id: 'user-2',
            displayId: 'APP-1002',
            name: 'Bob Smith',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-2',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-2',
                addressLine1: '456 Oak Ave',
                addressLine2: null,
                city: 'Oakland',
                state: 'CA',
                zip: '94601',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 1,
              sum: 150000,
            },
          },
          {
            __typename: 'User',
            id: 'user-3',
            displayId: 'APP-1003',
            name: 'Carol Davis',
            email: '<EMAIL>',
            phone: null,
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-3',
              mailingAddress: null,
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 0,
              sum: 0,
            },
          },
          {
            __typename: 'User',
            id: 'user-4',
            displayId: 'APP-1004',
            name: 'David Martinez',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-4',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-4',
                addressLine1: '789 Pine Rd',
                addressLine2: 'Unit 12',
                city: 'Berkeley',
                state: 'CA',
                zip: '94704',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 5,
              sum: 500000,
            },
          },
          {
            __typename: 'User',
            id: 'user-5',
            displayId: 'APP-1005',
            name: 'Eva Chen',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-5',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-5',
                addressLine1: '321 Elm St',
                addressLine2: null,
                city: 'San Jose',
                state: 'CA',
                zip: '95110',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 2,
              sum: 325000,
            },
          },
          {
            __typename: 'User',
            id: 'user-6',
            displayId: 'APP-1006',
            name: 'Frank Wilson',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-6',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-6',
                addressLine1: '654 Maple Dr',
                addressLine2: 'Suite 200',
                city: 'Palo Alto',
                state: 'CA',
                zip: '94301',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 4,
              sum: 400000,
            },
          },
          {
            __typename: 'User',
            id: 'user-7',
            displayId: 'APP-1007',
            name: 'Grace Lee',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-7',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-7',
                addressLine1: '987 Cedar Ln',
                addressLine2: null,
                city: 'Sunnyvale',
                state: 'CA',
                zip: '94086',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 1,
              sum: 125000,
            },
          },
          {
            __typename: 'User',
            id: 'user-8',
            displayId: 'APP-1008',
            name: 'Henry Garcia',
            email: '<EMAIL>',
            phone: null,
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-8',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-8',
                addressLine1: '246 Birch Ave',
                addressLine2: 'Apt 9C',
                city: 'Mountain View',
                state: 'CA',
                zip: '94040',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 3,
              sum: 275000,
            },
          },
          {
            __typename: 'User',
            id: 'user-9',
            displayId: 'APP-1009',
            name: 'Isabella Rodriguez',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-9',
              mailingAddress: null,
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 2,
              sum: 200000,
            },
          },
          {
            __typename: 'User',
            id: 'user-10',
            displayId: 'APP-1010',
            name: 'James Taylor',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-10',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-10',
                addressLine1: '135 Walnut St',
                addressLine2: null,
                city: 'Redwood City',
                state: 'CA',
                zip: '94063',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 6,
              sum: 550000,
            },
          },
        ],
      },
    },
  },
};

export const EMPTY_APPLICANTS_MOCK = {
  request: {
    query: GetApplicantsQuery,
    variables: {
      filter: {},
      pagination: { page: 0, take: 10 },
      sort: {
        column: UserSortColumn.Name,
        direction: SortDirection.Ascending,
      },
      includeDuplicates: true,
    },
  },
  result: {
    data: {
      users: {
        __typename: 'UserConnection',
        pageInfo: {
          __typename: 'PageInfo',
          count: 0,
        },
        users: [],
      },
    },
  },
};

export const APPLICANTS_LOADING_MOCK = loadingMock(GetApplicantsQuery, {
  filter: {},
  pagination: { page: 0, take: 10 },
  sort: {
    column: UserSortColumn.Name,
    direction: SortDirection.Ascending,
  },
  includeDuplicates: true,
});

export const APPLICANTS_WITH_SEARCH_MOCK = {
  request: {
    query: GetApplicantsQuery,
    variables: {
      filter: { search: 'alice' },
      pagination: { page: 0, take: 10 },
      sort: {
        column: UserSortColumn.Name,
        direction: SortDirection.Ascending,
      },
      includeDuplicates: true,
    },
  },
  result: {
    data: {
      users: {
        __typename: 'UserConnection',
        pageInfo: {
          __typename: 'PageInfo',
          count: 1,
        },
        users: [
          {
            __typename: 'User',
            id: 'user-1',
            displayId: 'APP-1001',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-1',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-1',
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                city: 'San Francisco',
                state: 'CA',
                zip: '94102',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 3,
              sum: 250000,
            },
          },
        ],
      },
    },
  },
};

export const APPLICANTS_PAGINATED_MOCK = {
  request: {
    query: GetApplicantsQuery,
    variables: {
      filter: {},
      pagination: { page: 1, take: 10 },
      sort: {
        column: UserSortColumn.Name,
        direction: SortDirection.Ascending,
      },
      includeDuplicates: true,
    },
  },
  result: {
    data: {
      users: {
        __typename: 'UserConnection',
        pageInfo: {
          __typename: 'PageInfo',
          count: 42,
        },
        users: [
          {
            __typename: 'User',
            id: 'user-11',
            displayId: 'APP-1011',
            name: 'Frank Wilson',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-11',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-11',
                addressLine1: '111 Second St',
                addressLine2: null,
                city: 'San Francisco',
                state: 'CA',
                zip: '94103',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 1,
              sum: 100000,
            },
          },
        ],
      },
    },
  },
};

export const APPLICANTS_SORTED_MOCK = {
  request: {
    query: GetApplicantsQuery,
    variables: {
      filter: {},
      pagination: { page: 0, take: 10 },
      sort: {
        column: UserSortColumn.TotalPaid,
        direction: SortDirection.Descending,
      },
      includeDuplicates: true,
    },
  },
  result: {
    data: {
      users: {
        __typename: 'UserConnection',
        pageInfo: {
          __typename: 'PageInfo',
          count: 42,
        },
        users: [
          {
            __typename: 'User',
            id: 'user-10',
            displayId: 'APP-1010',
            name: 'James Taylor',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-10',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-10',
                addressLine1: '135 Walnut St',
                addressLine2: null,
                city: 'Redwood City',
                state: 'CA',
                zip: '94063',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 6,
              sum: 550000,
            },
          },
          {
            __typename: 'User',
            id: 'user-4',
            displayId: 'APP-1004',
            name: 'David Martinez',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-4',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-4',
                addressLine1: '789 Pine Rd',
                addressLine2: 'Unit 12',
                city: 'Berkeley',
                state: 'CA',
                zip: '94704',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 5,
              sum: 500000,
            },
          },
          {
            __typename: 'User',
            id: 'user-6',
            displayId: 'APP-1006',
            name: 'Frank Wilson',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-6',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-6',
                addressLine1: '654 Maple Dr',
                addressLine2: 'Suite 200',
                city: 'Palo Alto',
                state: 'CA',
                zip: '94301',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 4,
              sum: 400000,
            },
          },
          {
            __typename: 'User',
            id: 'user-5',
            displayId: 'APP-1005',
            name: 'Eva Chen',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-5',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-5',
                addressLine1: '321 Elm St',
                addressLine2: null,
                city: 'San Jose',
                state: 'CA',
                zip: '95110',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 2,
              sum: 325000,
            },
          },
          {
            __typename: 'User',
            id: 'user-8',
            displayId: 'APP-1008',
            name: 'Henry Garcia',
            email: '<EMAIL>',
            phone: null,
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-8',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-8',
                addressLine1: '246 Birch Ave',
                addressLine2: 'Apt 9C',
                city: 'Mountain View',
                state: 'CA',
                zip: '94040',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 3,
              sum: 275000,
            },
          },
          {
            __typename: 'User',
            id: 'user-1',
            displayId: 'APP-1001',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-1',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-1',
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                city: 'San Francisco',
                state: 'CA',
                zip: '94102',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 3,
              sum: 250000,
            },
          },
          {
            __typename: 'User',
            id: 'user-9',
            displayId: 'APP-1009',
            name: 'Isabella Rodriguez',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-9',
              mailingAddress: null,
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 2,
              sum: 200000,
            },
          },
          {
            __typename: 'User',
            id: 'user-2',
            displayId: 'APP-1002',
            name: 'Bob Smith',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-2',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-2',
                addressLine1: '456 Oak Ave',
                addressLine2: null,
                city: 'Oakland',
                state: 'CA',
                zip: '94601',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 1,
              sum: 150000,
            },
          },
          {
            __typename: 'User',
            id: 'user-7',
            displayId: 'APP-1007',
            name: 'Grace Lee',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-7',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-7',
                addressLine1: '987 Cedar Ln',
                addressLine2: null,
                city: 'Sunnyvale',
                state: 'CA',
                zip: '94086',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 1,
              sum: 125000,
            },
          },
          {
            __typename: 'User',
            id: 'user-3',
            displayId: 'APP-1003',
            name: 'Carol Davis',
            email: '<EMAIL>',
            phone: null,
            hasPotentialDuplicates: false,
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-3',
              mailingAddress: null,
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 0,
              sum: 0,
            },
          },
        ],
      },
    },
  },
};

export const APPLICANTS_WITH_DUPLICATES_MOCK = {
  request: {
    query: GetApplicantsQuery,
    variables: {
      filter: {},
      pagination: { page: 0, take: 10 },
      sort: {
        column: UserSortColumn.Name,
        direction: SortDirection.Ascending,
      },
      includeDuplicates: true,
    },
  },
  result: {
    data: {
      users: {
        __typename: 'UserConnection',
        pageInfo: {
          __typename: 'PageInfo',
          count: 3,
        },
        users: [
          {
            __typename: 'User',
            id: 'user-1',
            displayId: 'APP-1001',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: true, // This user has duplicates
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-1',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-1',
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                city: 'San Francisco',
                state: 'CA',
                zip: '94102',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 3,
              sum: 250000,
            },
          },
          {
            __typename: 'User',
            id: 'user-2',
            displayId: 'APP-1002',
            name: 'Bob Smith',
            email: '<EMAIL>',
            phone: '************',
            hasPotentialDuplicates: false, // This user has no duplicates
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-2',
              mailingAddress: {
                __typename: 'Address',
                id: 'addr-2',
                addressLine1: '456 Oak Ave',
                addressLine2: null,
                city: 'Oakland',
                state: 'CA',
                zip: '94601',
                incomeLimitArea: null,
              },
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 1,
              sum: 150000,
            },
          },
          {
            __typename: 'User',
            id: 'user-3',
            displayId: 'APP-1003',
            name: 'Carol Davis',
            email: '<EMAIL>',
            phone: null,
            hasPotentialDuplicates: true, // This user also has duplicates
            applicantProfile: {
              __typename: 'ApplicantProfile',
              id: 'profile-3',
              mailingAddress: null,
            },
            aggregatePayments: {
              __typename: 'AggregatePayments',
              count: 0,
              sum: 0,
            },
          },
        ],
      },
    },
  },
};

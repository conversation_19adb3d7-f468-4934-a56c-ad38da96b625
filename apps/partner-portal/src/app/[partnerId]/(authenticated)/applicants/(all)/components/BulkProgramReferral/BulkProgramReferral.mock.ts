import GetProgramsWithFundDetailsQuery from './GetProgramsWithFundDetailsQuery.graphql';
import CreateBulkProgramReferralMutation from './CreateBulkProgramReferralMutation.graphql';
import { FeatureName, ProgramStatus } from '@bybeam/platform-types';
import { HAS_ACCESS_ORGANIZATION_EDIT_MOCK } from '@/tests/mocks/hasAccessMocks';

const programsData = {
  programs: {
    programs: [
      {
        id: 'mockProgramId',
        name: 'Program With Fund',
        status: ProgramStatus.Open,
        features: [
          {
            id: 'programFeatureId',
            enabled: true,
            feature: { id: 'featureId', name: FeatureName.ProgramsReferral },
            __typename: 'FeatureSetting',
          },
        ],
        funds: [
          {
            id: 'mockFundId',
            name: 'Fund Name',
            startingBalance: 10000,
            stats: { remainingBalance: 10000 },
          },
        ],
      },
      {
        id: 'mockProgramId2',
        name: 'Program No Payment Without Fund',
        status: ProgramStatus.Open,
        features: [
          {
            id: 'program2FeatureId',
            enabled: true,
            feature: { id: 'featureId', name: FeatureName.ProgramsReferral },
            __typename: 'FeatureSetting',
          },
        ],
        funds: [
          {
            id: 'mockFundId',
            name: 'Fund Name',
            startingBalance: 0,
            stats: { remainingBalance: 0 },
          },
        ],
      },
      {
        id: 'mockProgramId3',
        name: 'Program With Payment Without Fund',
        status: ProgramStatus.Open,
        features: [
          {
            id: 'program3FeatureId1',
            enabled: true,
            feature: { id: 'featureId', name: FeatureName.ProgramsReferral },
            __typename: 'FeatureSetting',
          },
          {
            id: 'program3FeatureId2',
            enabled: true,
            feature: { id: 'featureId', name: FeatureName.PaymentsClaimFunds },
            __typename: 'FeatureSetting',
          },
        ],
        funds: [
          {
            id: 'mockFundId',
            name: 'Fund Name',
            startingBalance: 0,
            stats: { remainingBalance: 0 },
          },
        ],
      },
    ],
  },
};

export const GET_PROGRAMS_WITH_FUNDS = {
  request: {
    query: GetProgramsWithFundDetailsQuery,
  },
  result: {
    data: programsData,
  },
  newData: () => ({
    data: programsData,
  }),
};

export const CREATE_BULK_REFERRAL = {
  request: {
    query: CreateBulkProgramReferralMutation,
    variables: {
      input: {
        programId: 'mockProgramId',
        userIds: ['mockUserId'],
      },
    },
  },
  result: {
    data: {
      programReferral: {
        createBulkReferral: {
          metadata: {
            status: 400,
            errors: [{ id: 'mockUserId', message: 'error' }],
          },
        },
      },
    },
  },
  newData: () => ({
    data: {
      programReferral: {
        createBulkReferral: {
          metadata: {
            status: 400,
            errors: [{ id: 'mockUserId', message: 'error' }],
          },
        },
      },
    },
  }),
};

// Re-export the shared HasAccess mock for backward compatibility
export const HAS_ACCESS_QUERY = HAS_ACCESS_ORGANIZATION_EDIT_MOCK;

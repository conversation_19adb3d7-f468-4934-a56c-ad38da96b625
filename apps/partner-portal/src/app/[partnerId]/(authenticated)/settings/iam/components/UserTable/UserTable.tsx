'use client';

import { Role } from '@/app/_utils/roles';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import { QueryResult } from '@apollo/client';
import { Admin, PageInfo } from '@bybeam/platform-types';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DotsVerticalIcon,
  Pencil1Icon,
  TrashIcon,
} from '@radix-ui/react-icons';
import { DropdownMenu, Flex, IconButton, Select, Table } from '@radix-ui/themes';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { PortalRolesDisplay } from '../../utils/portalRoles';
import DeleteUserModal from '../DeleteUserModal/DeleteUserModal';
import UpdateUserModal from '../UpdateUserModal/UpdateUserModal';
import styles from './userTable.module.css';

const PAGE_SIZE_OPTIONS = [5, 10, 25, 50];

function UserTable({
  data,
  refetch,
  defaultValues,
  portalRoles,
}: QueryResult<
  { admins: { nodes: Admin[]; pageInfo: PageInfo } },
  { page: number; take: number }
> & { defaultValues: { page: string; size: string }; portalRoles: Role[] }) {
  const [selectedAdmin, setSelectedAdmin] = useState<Admin>();
  const [openModal, setModal] = useState<'edit' | 'delete'>();

  const { user: currentUser } = useNextAuth();
  const { control, getValues, setValue, watch } = useForm<{ page: string; size: string }>({
    values: defaultValues,
  });

  const getFieldValue = (field: 'page' | 'size') => Number(getValues()?.[field]);

  const { nodes: admins, pageInfo } = data?.admins ?? {};
  const currentPage = getFieldValue('page');
  const totalPages = Math.ceil((pageInfo?.count ?? 0) / getFieldValue('size'));

  const handleEditUser = (admin: Admin) => {
    setSelectedAdmin(admin);
    setModal('edit');
  };

  const handleDeleteUser = (admin: Admin) => {
    setSelectedAdmin(admin);
    setModal('delete');
  };

  const closeModal = () => {
    setModal(undefined);
    setSelectedAdmin(undefined);
  };

  useEffect(() => {
    const subscription = watch(({ page, size }, { name }) => {
      if (name === 'size') {
        page = '0';
        setValue('page', '0');
      }
      refetch({ page: Number(page), take: Number(size) });
    });
    return () => subscription.unsubscribe();
  }, [refetch, setValue, watch]);

  return (
    <>
      {openModal === 'edit' && selectedAdmin && (
        <UpdateUserModal
          isOpen={true}
          onClickClose={closeModal}
          onSuccess={() => refetch()}
          portalRoles={portalRoles}
          admin={selectedAdmin}
        />
      )}
      {openModal === 'delete' && selectedAdmin && (
        <DeleteUserModal
          isOpen={true}
          onClickClose={closeModal}
          onSuccess={() => refetch()}
          admin={selectedAdmin}
        />
      )}
      <Table.Root variant="surface">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell>Name</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Email</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Roles</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell />
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {admins?.map((admin) => (
            <Table.Row key={admin.id}>
              <Table.Cell>{admin.identityUser.name}</Table.Cell>
              <Table.Cell>{admin.identityUser.email}</Table.Cell>
              <Table.Cell>
                {(admin?.roles?.length ? admin.roles : [Role.StandardPayment])
                  .map((role: Role) => PortalRolesDisplay(role))
                  .join(', ')}
              </Table.Cell>
              <Table.Cell>
                {currentUser?.admin?.id !== admin.id && (
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger>
                      <IconButton variant="ghost" size="2">
                        <DotsVerticalIcon />
                      </IconButton>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content>
                      <DropdownMenu.Item onSelect={() => handleEditUser(admin)}>
                        <Pencil1Icon /> Edit User
                      </DropdownMenu.Item>
                      <DropdownMenu.Item onSelect={() => handleDeleteUser(admin)}>
                        <TrashIcon /> Delete User
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                )}
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
      <Flex justify="between" p="4">
        <div className={styles.pageSizeSelect}>
          Page Size:
          <Controller
            control={control}
            name="size"
            render={({ field }) => (
              <Select.Root onValueChange={field.onChange} value={field.value}>
                <Select.Trigger />
                <Select.Content>
                  {PAGE_SIZE_OPTIONS.map((size) => (
                    <Select.Item key={size} value={`${size}`}>
                      {size}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select.Root>
            )}
          />
        </div>
        <Flex align="center" gap="2">
          <IconButton
            color="gray"
            size="3"
            variant="ghost"
            disabled={currentPage === 0}
            onClick={() => setValue('page', (currentPage - 1).toString())}
          >
            <ChevronLeftIcon />
          </IconButton>
          {currentPage + 1} of {totalPages}
          <IconButton
            color="gray"
            size="3"
            variant="ghost"
            disabled={currentPage === totalPages - 1}
            onClick={() => setValue('page', (currentPage + 1).toString())}
          >
            <ChevronRightIcon />
          </IconButton>
        </Flex>
      </Flex>
    </>
  );
}

export default UserTable;

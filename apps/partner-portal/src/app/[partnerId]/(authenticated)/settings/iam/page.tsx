'use client';

import GetAdminsQuery from '@/app/hooks/useAdmins/GetAdminsQuery.graphql';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { useQuery } from '@apollo/client';
import { Admin, PageInfo } from '@bybeam/platform-types';
import { PlusIcon } from '@radix-ui/react-icons';
import { Button, Heading } from '@radix-ui/themes';
import { Suspense, useState } from 'react';
import { CreateUserModal, UserTable } from '../iam/components';
import styles from './iam.module.css';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';

export default function IamSettingsPage() {
  const [open, setOpen] = useState(false);

  const partner = usePartner();
  const partnerRoles = partner?.roles ?? [];
  const partnerHasSSOEnabled = !!partner?.config?.identity?.advocate?.saml;

  const tableDefaults = { page: '0', size: '10' };
  const userQuery = useQuery<
    { admins: { nodes: Admin[]; pageInfo: PageInfo } },
    { page: number; take: number }
  >(GetAdminsQuery, {
    variables: { page: Number(tableDefaults.page), take: Number(tableDefaults.size) },
  });
  return (
    <>
      <CreateUserModal
        isOpen={open}
        onClickClose={() => setOpen(false)}
        onSuccess={() => userQuery.refetch()}
        portalRoles={partnerRoles}
      />
      <div className={styles.userContainer}>
        <Heading size="5" weight="medium" as="h1">Users</Heading>
        {!partnerHasSSOEnabled ? (
          <Button onClick={() => setOpen(true)}>
            <PlusIcon />
            Add New User
          </Button>
        ) : null}
        <Suspense fallback={<LoadingComponent />}>
          <UserTable {...userQuery} defaultValues={tableDefaults} portalRoles={partnerRoles} />
        </Suspense>
      </div>
    </>
  );
}

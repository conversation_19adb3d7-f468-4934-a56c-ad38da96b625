import { useMutation, useQuery } from '@apollo/client';
import {
  DeleteTagAutomationInput,
  MutationResponse,
  Partner,
  TagAutomation,
  TagAutomationActionType,
  TagAutomationTriggerType,
} from '@bybeam/platform-types';
import { AlertDialog, Button, Flex, IconButton, Table, Heading } from '@radix-ui/themes';
import GetPartnerTags from './GetPartnerTags.graphql';
import { Pencil2Icon, PlusIcon, TrashIcon } from '@radix-ui/react-icons';
import styles from './TagAutomations.module.css';
import { useState } from 'react';
import TagAutomationDialog from './TagAutomationDialog';
import { TagChips } from '@/spa-legacy/portal/components/CasesTable/cells/TagsCell';
import DeleteTagAutomation from './DeleteTagAutomation.graphql';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';

export const getActionType = (type: TagAutomationActionType) => {
  switch (type) {
    case TagAutomationActionType.AddTag:
      return 'Add Tag';
    default:
      return 'Unknown';
  }
};

export const getTriggerType = (type: TagAutomationTriggerType) => {
  switch (type) {
    case TagAutomationTriggerType.ApplicationSubmitted:
      return 'Application Submitted';

    default:
      return 'Unknown';
  }
};

export default function TagAutomationsManager() {
  const { externalId } = usePartner();
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [selectedTagAutomation, setSelectedTagAutomation] = useState<TagAutomation>();
  const { data: partnerTagData } = useQuery<{ partners: { partners: Partner[] } }>(GetPartnerTags, {
    variables: { externalId },
  });
  const [deleteTagAutomation, { error: mutationError, loading: deleting }] = useMutation<
    { tagAutomation: { delete: MutationResponse<TagAutomation> } },
    { input: DeleteTagAutomationInput }
  >(DeleteTagAutomation, {
    refetchQueries: ['GetPartnerTags'],
  });

  const tagAutomations = partnerTagData?.partners?.partners?.[0]?.tagAutomations ?? [];
  const availableTags = partnerTagData?.partners?.partners?.[0].tags ?? [];
  const { showSnackbar } = useSnackbar();

  const handleDeactivate = async (id: string) => {
    try {
      await deleteTagAutomation({
        variables: {
          input: {
            id,
          },
        },
      });
      if (mutationError) {
        showSnackbar('Failed to delete tag automation');
        return;
      }
      showSnackbar('Tag automation successfully deleted.');
    } catch (e) {
      console.error(e);
      showSnackbar('Failed to delete tag automation');
    }
  };

  return (
    <Flex direction="column" gap="2">
      <div className={styles.header}>
        <Heading size="6">Tag Automations</Heading>
        <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
          <PlusIcon />
          Add Automation
        </Button>
      </div>
      <div>
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Trigger</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Action</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Tag(s)</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Programs</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell> </Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {tagAutomations?.map((automation) => (
              <Table.Row key={automation.id}>
                <Table.RowHeaderCell>{getTriggerType(automation.triggerType)}</Table.RowHeaderCell>
                <Table.Cell>{getActionType(automation.actionType)}</Table.Cell>
                <Table.Cell>
                  <Flex gap="2">
                    <TagChips tags={automation.tags} />
                  </Flex>
                </Table.Cell>
                <Table.Cell>
                  {automation.program ? automation.program.name : 'All programs'}
                </Table.Cell>
                <Table.Cell>
                  <Flex gap="4" align="center">
                    <AlertDialog.Root>
                      <AlertDialog.Trigger>
                        <IconButton color="red" variant="ghost" aria-label="Delete automation">
                          <TrashIcon />
                        </IconButton>
                      </AlertDialog.Trigger>
                      <AlertDialog.Content maxWidth="450px">
                        <AlertDialog.Title>Delete Tag Automation</AlertDialog.Title>
                        <AlertDialog.Description size="2">
                          Are you sure? This automation will no longer be accessible and no further
                          automations will trigger.
                        </AlertDialog.Description>

                        <Flex gap="3" mt="4" justify="end">
                          <AlertDialog.Cancel>
                            <Button variant="soft" color="gray">
                              Cancel
                            </Button>
                          </AlertDialog.Cancel>
                          <AlertDialog.Action>
                            <Button
                              variant="solid"
                              color="red"
                              onClick={() => handleDeactivate(automation.id)}
                              loading={deleting}
                            >
                              Delete
                            </Button>
                          </AlertDialog.Action>
                        </Flex>
                      </AlertDialog.Content>
                    </AlertDialog.Root>

                    <IconButton
                      variant="ghost"
                      aria-label="Edit automation"
                      onClick={() => {
                        setSelectedTagAutomation(automation);
                        setIsDialogOpen(true);
                      }}
                    >
                      <Pencil2Icon />
                    </IconButton>
                  </Flex>
                </Table.Cell>
              </Table.Row>
            ))}
            {!tagAutomations.length && (
              <Table.Row>
                <Table.Cell colSpan={5}>No tag automations found</Table.Cell>
              </Table.Row>
            )}
          </Table.Body>
        </Table.Root>
      </div>
      <TagAutomationDialog
        onSuccess={() => {
          setSelectedTagAutomation(undefined);
          setIsDialogOpen(false);
        }}
        setIsDialogOpen={(value) => {
          setIsDialogOpen(value);
          if (!value) {
            setSelectedTagAutomation(undefined);
          }
        }}
        isDialogOpen={isDialogOpen}
        tagAutomation={selectedTagAutomation}
        availableTags={availableTags}
      />
    </Flex>
  );
}

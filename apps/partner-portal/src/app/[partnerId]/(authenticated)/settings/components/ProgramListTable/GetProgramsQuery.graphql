#import "@/spa-legacy/graphql/fragments/FeatureSettingFragment.graphql"
#import "@/spa-legacy/graphql/fragments/DocumentFragment.graphql"
#import "@/spa-legacy/graphql/fragments/appconfig/ApplicationConfigurationFragment.graphql"

query getPrograms {
  programs {
    programs {
      id
      name
      status
      config {
        programContext {
          description
        }
        rulesEvaluationEnabled
      }
      documents {
        id
        status
        document {
          ...DocumentFragment
        }
      }
      features {
        ...FeatureSettingFragment
      }
      applicantTypes {
        id
        applicantType {
          id
          name
          role
        }
      }
      verificationConfigurations {
        id
        service
        dataLookup {
          fields {
            key
            details
            sample
            metadata
          }
        }
      }
      applicationConfigurations {
        programId
        applicantTypeId
        configuration {
          ...ApplicationConfigurationFragment
        }
      }
    }
  }
}

import CanAccess from '@/app/components/features/CanAccess';
import Loading from '@/spa-legacy/common/components/Loading';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { downloadFile } from '@/spa-legacy/portal/utils/client';
import {
  checkFeatureAnyProgram,
  checkFeaturesAnyProgram,
} from '@/spa-legacy/utilities/checkFeature';
import { useQuery } from '@apollo/client';
import { FeatureName, type Program, ProgramDocumentStatus } from '@bybeam/platform-types';
import { ServiceType } from '@bybeam/verification-types';
import { JSX, useEffect, useState } from 'react';
import GetProgramsQuery from './GetProgramsQuery.graphql';
import { useVerificationFilesStatus } from '@/spa-legacy/portal/pages/Settings/hooks/useVerificationFilesStatus';
import EditProgramModal from '../EditProgramModal';
import VerificationActions, {
  VerificationActionType,
} from '@/spa-legacy/portal/pages/Settings/components/VerificationDataLookup/Actions';
import UploadVerificationFile from '@/spa-legacy/portal/pages/Settings/components/VerificationDataLookup/UploadVerificationFile';
import { UpsertLookupConfigModal } from '@/spa-legacy/portal/pages/Settings/components/VerificationDataLookup/UpsertLookupConfig';
import VerificationFilesStatus from '@/spa-legacy/portal/pages/Settings/components/VerificationDataLookup/VerificationFilesStatus';
import { Button, DropdownMenu, Flex, Heading, IconButton, Table } from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { DotsVerticalIcon, Pencil1Icon } from '@radix-ui/react-icons';
import ProgramCreateDialog from '@/spa-legacy/portal/pages/Settings/ProgramCreate/ProgramCreateDialog';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { ProgramStatusDisplay } from '@/spa-legacy/portal/pages/Settings/utils';

const ProgramListTable = (): JSX.Element => {
  const [isEditProgramsModalOpen, setOpenEditProgramsModal] = useState<boolean>(false);
  const [isUploadVerificationFileOpen, openVerificationFileModal] = useState<boolean>(false);
  const [isLookupFieldModalOpen, setIsLookupFieldModalOpen] = useState<boolean>(false);
  const [selectedProgram, setSelectedProgram] = useState<Program>();
  const { showSnackbar } = useSnackbar();

  const partner = usePartner();
  const postHog = usePostHog();

  const { data, loading, refetch } = useQuery(GetProgramsQuery, {
    fetchPolicy: 'cache-first',
  });

  const { verificationFiles, refetchStatus, resetStatus } = useVerificationFilesStatus(refetch);

  const programs: Program[] = data?.programs?.programs || [];

  const isVerificationFeatureEnabled = checkFeatureAnyProgram(
    programs,
    FeatureName.VerificationDataLookup,
  );

  const handleModalClose = (): void => {
    setOpenEditProgramsModal(false);
  };

  const generateSampleFile = (program: Program): Blob | null => {
    const config = program?.verificationConfigurations?.find(
      ({ service }) => service === ServiceType.DataLookup,
    );
    const setup = config?.dataLookup?.fields?.reduce<{
      headers: string[];
      row: string[];
    }>(
      (data, column) => {
        data.headers.push(column.key);
        data.row.push(column.sample);
        return data;
      },
      { headers: [], row: [] },
    );
    if (!setup) {
      showSnackbar('Error generating sample data. Please contact support.');
      return null;
    }
    const csv = [setup.headers.join(','), setup.row.join(',')].join('\n');
    return new Blob([csv], { type: 'text/csv' });
  };

  const onClickVerificationAction = (action: VerificationActionType, program: Program) => {
    switch (action) {
      case VerificationActionType.UPLOAD_FILE:
      case VerificationActionType.REPLACE_FILE:
        openVerificationFileModal(true);
        setSelectedProgram(program);
        break;
      case VerificationActionType.DOWNLOAD_CSV_TEMPLATE: {
        const file = generateSampleFile(program);
        if (!file) return;
        downloadFile(file, `${program.name}-sample.csv`);
        break;
      }
      case VerificationActionType.UPDATE_FIELDS:
        setIsLookupFieldModalOpen(true);
        setSelectedProgram(program);
        break;
      case VerificationActionType.DOWNLOAD_FILE:
        break;
    }
  };

  const onCloseVerificationFileModal = (doRefetch?: boolean) => {
    if (doRefetch && selectedProgram) {
      refetchStatus(selectedProgram);
    }
    openVerificationFileModal(false);
    setSelectedProgram(undefined);
  };

  const onLookupConfigModalClose = (openFileUpload?: boolean) => {
    refetch();
    setIsLookupFieldModalOpen(false);
    openFileUpload && openVerificationFileModal(true);
  };

  useEffect(() => resetStatus(programs ?? []), [programs, resetStatus]);

  const anyProgramHasVerification = checkFeaturesAnyProgram(programs, [
    FeatureName.ApplicationVerification,
    FeatureName.VerificationDataLookup,
  ]);

  const mapDocumentStatus = (status?: ProgramDocumentStatus): string => {
    switch (status) {
      case ProgramDocumentStatus.Failed:
        return 'Failed';
      default:
        return '';
    }
  };

  if (loading) return <Loading size="XXL" />;
  return (
    <>
      {selectedProgram && (
        <EditProgramModal
          program={selectedProgram}
          isOpen={isEditProgramsModalOpen}
          onClose={handleModalClose}
        />
      )}
      {isUploadVerificationFileOpen && selectedProgram && (
        <UploadVerificationFile
          program={selectedProgram}
          isOpen={isUploadVerificationFileOpen}
          onClose={onCloseVerificationFileModal}
        />
      )}
      {isLookupFieldModalOpen && selectedProgram && (
        <UpsertLookupConfigModal
          program={selectedProgram}
          isOpen={isLookupFieldModalOpen}
          onClose={onLookupConfigModalClose}
        />
      )}
      <Flex direction="column" gap="2">
        <Flex gap="4" align="baseline">
          <Heading as="h2">{`Programs (${programs.length})`}</Heading>
          {postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.programCreation) && (
            <CanAccess resource={{ objectId: partner?.id, objectType: 'ORGANIZATION' }}>
              <ProgramCreateDialog />
            </CanAccess>
          )}
        </Flex>
        {isVerificationFeatureEnabled && verificationFiles?.length && (
          <VerificationFilesStatus files={verificationFiles} />
        )}
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Program Name</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Program Status</Table.ColumnHeaderCell>
              {anyProgramHasVerification && (
                <Table.ColumnHeaderCell>Verification File</Table.ColumnHeaderCell>
              )}
              <Table.ColumnHeaderCell />
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {programs.map((program) => {
              const { name, id, status, documents } = program;
              const isVerificationEnabled = checkFeature(
                program?.features,
                FeatureName.VerificationDataLookup,
              );
              const filename =
                [documents?.[0]?.document?.filename, mapDocumentStatus(documents?.[0]?.status)]
                  .filter(Boolean)
                  .join(' - ') || '-';
              return (
                <Table.Row key={id}>
                  <Table.RowHeaderCell>
                    <strong>{name}</strong>
                  </Table.RowHeaderCell>
                  <Table.Cell>{ProgramStatusDisplay[status]}</Table.Cell>
                  {anyProgramHasVerification && <Table.Cell>{filename}</Table.Cell>}
                  <Table.Cell align="center">
                    {isVerificationEnabled ? (
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <IconButton variant="ghost" aria-label="Actions">
                            <DotsVerticalIcon />
                          </IconButton>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content>
                          <CanAccess
                            resource={{ objectId: partner?.id, objectType: 'ORGANIZATION' }}
                          >
                            <DropdownMenu.Item
                              onClick={() => {
                                setSelectedProgram(program);
                                setOpenEditProgramsModal(true);
                              }}
                            >
                              <Pencil1Icon /> Edit
                            </DropdownMenu.Item>
                          </CanAccess>
                          {isVerificationEnabled && (
                            <CanAccess resource={{ objectId: program.id, objectType: 'PROGRAM' }}>
                              <VerificationActions
                                program={program}
                                onClick={onClickVerificationAction}
                              />
                            </CanAccess>
                          )}
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    ) : (
                      <CanAccess resource={{ objectId: partner?.id, objectType: 'ORGANIZATION' }}>
                        <Button
                          variant="ghost"
                          aria-label="Edit"
                          onClick={() => {
                            setSelectedProgram(program);
                            setOpenEditProgramsModal(true);
                          }}
                        >
                          <Pencil1Icon /> Edit
                        </Button>
                      </CanAccess>
                    )}
                  </Table.Cell>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table.Root>
      </Flex>
    </>
  );
};

export default ProgramListTable;

import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { useMutation, useQuery } from '@apollo/client';
import {
  CreateProgramInput,
  MutationResponse,
  Partner,
  Program,
  ProgramStatus,
} from '@bybeam/platform-types';
import { ErrorMessage } from '@hookform/error-message';
import { InfoCircledIcon, Pencil2Icon } from '@radix-ui/react-icons';
import { Button, Callout, Dialog, Flex, Select, Text, TextField, Tooltip } from '@radix-ui/themes';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import CreateProgramMutation from './CreateProgramMutation.graphql';
import GetPartnerFunds from './GetPartnerFunds.graphql';

export interface CreateProgramModalProps {
  onSuccess: () => void;
}

const ProgramCreateDialog = ({ onSuccess }: CreateProgramModalProps): JSX.Element => {
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [createMutation, { loading: submitting, error: mutationError, reset: resetMutation }] =
    useMutation<
      { program: { create: MutationResponse<Program> } },
      { input: CreateProgramInput; externalId: string }
    >(CreateProgramMutation);
  const { externalId } = usePartner();
  const { showSnackbar } = useSnackbar();
  const { data: fundsData } = useQuery<
    { partners: { partners: Partner[] } },
    { externalId: string }
  >(GetPartnerFunds, {
    variables: { externalId },
  });

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    reset: resetFormState,
  } = useForm<{ name: string; fundId: string; status: ProgramStatus }>({
    defaultValues: {
      name: '',
      fundId: '',
      status: ProgramStatus.Closed,
    },
  });

  const onSubmit = async ({
    fundId,
    ...variables
  }: { fundId: string; name: string; status?: ProgramStatus }) => {
    try {
      await createMutation({
        variables: {
          input: {
            ...variables,
            fundIds: [fundId],
          },
          externalId,
        },
      });

      showSnackbar('Program successfully created');

      resetFormState();
      onSuccess();
      setIsDialogOpen(false);
    } catch (e) {
      console.error(e);
    }
  };

  // TODO: Implement a more deterministic way to retrieve available funds.
  const funds = fundsData?.partners?.partners?.[0]?.funds ?? [];

  return (
    <Dialog.Root
      open={isDialogOpen}
      onOpenChange={(open) => {
        resetFormState();
        resetMutation();
        setIsDialogOpen(open);
      }}
    >
      <Dialog.Trigger>
        <Button variant="outline">
          <Pencil2Icon /> Create Program
        </Button>
      </Dialog.Trigger>
      <Dialog.Content size="3">
        <Dialog.Title>Create a new program</Dialog.Title>
        <Dialog.Description mb="4">
          <Callout.Root>
            <Callout.Text highContrast>
              Your new program will be created with a default application configuration, which can
              be modified later. Contact a Beam account representative for more details.
            </Callout.Text>
          </Callout.Root>
        </Dialog.Description>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Flex direction="column" gap="3" mb="6">
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="program-name">
                Program name
              </Text>
              <TextField.Root
                id="program-name"
                placeholder="Program name"
                {...register('name', { required: 'Name is required' })}
                aria-invalid={!!errors.name}
                aria-describedby="error-name"
              />
              <ErrorMessage
                errors={errors}
                name="name"
                render={({ message }) => (
                  <Text color="red" size="1" id="error-name" ml="1">
                    {message}
                  </Text>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Text as="label" size="2" mb="1" weight="bold" htmlFor="program-fund">
                Fund
              </Text>
              <Controller
                name="fundId"
                control={control}
                rules={{ required: 'Fund is required' }}
                render={({ field }) => (
                  <Select.Root onValueChange={field.onChange}>
                    <Select.Trigger
                      id="program-fund"
                      placeholder="Select a fund"
                      aria-invalid={!!errors.fundId}
                      aria-describedby="error-fund"
                    />
                    <Select.Content position="popper">
                      {funds?.map((fund) => (
                        <Select.Item key={fund.id} value={fund.id}>
                          {fund.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />
              <ErrorMessage
                errors={errors}
                name="fundId"
                render={({ message }) => (
                  <Text color="red" size="1" id="error-fund" ml="1">
                    {message}
                  </Text>
                )}
              />
            </Flex>
            <Flex direction="column">
              <Flex asChild align="center" gap="1">
                <Text as="label" size="2" mb="1" weight="bold" htmlFor="program-status">
                  Program Status
                  <Tooltip content="Create your program with a status of 'Closed' to prevent applications from being submitted too soon. You can change the status to 'Open' at any time.">
                    <InfoCircledIcon tabIndex={0} />
                  </Tooltip>
                </Text>
              </Flex>
              <Controller
                name="status"
                control={control}
                rules={{ required: 'Program status is required' }}
                render={({ field }) => (
                  <Select.Root onValueChange={field.onChange} defaultValue={ProgramStatus.Closed}>
                    <Select.Trigger
                      id="program-fund"
                      placeholder="Program status"
                      aria-invalid={!!errors.fundId}
                      aria-describedby="error-status"
                    />
                    <Select.Content position="popper">
                      {Object.values(ProgramStatus).map((statusOption) => (
                        <Select.Item key={statusOption} value={statusOption}>
                          {statusOption}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />
              <ErrorMessage
                errors={errors}
                name="status"
                render={({ message }) => (
                  <Text color="red" size="1" id="error-status" ml="1">
                    {message}
                  </Text>
                )}
              />
            </Flex>
            {mutationError && (
              <Callout.Root color="red" aria-live="polite">
                <Callout.Text highContrast>
                  Failed to create program. If this problem persists, please contact Beam support.
                </Callout.Text>
              </Callout.Root>
            )}
          </Flex>
          <Flex gap="2" justify="end">
            <Dialog.Close>
              <Button disabled={submitting} variant="soft">
                Cancel
              </Button>
            </Dialog.Close>
            <Button loading={submitting} type="submit">
              Save
            </Button>
          </Flex>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default ProgramCreateDialog;

import { But<PERSON>, <PERSON><PERSON>, Flex, Select, Text, TextArea } from '@radix-ui/themes';
import useErrorIndicator from '@/spa-legacy/common/hooks/useErrorIndicator';
import useSaveIndicator from '@/spa-legacy/common/hooks/useSaveIndicator';
import { useMutation } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, type Program, ProgramContext, ProgramStatus } from '@bybeam/platform-types';
import { Controller, useForm } from 'react-hook-form';
import UpdateProgramMutation from './UpdateProgramMutation.graphql';
import { ProgramStatusDisplay } from '../../../../../../spa-legacy/portal/pages/Settings/utils';
import { JSX, useEffect, useMemo } from 'react';

export interface EditProgramModalProps {
  program: Program;
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  id: string;
  name: string;
  status: string;
  description?: string;
}

const EditProgramModal = ({ program, isOpen, onClose }: EditProgramModalProps): JSX.Element => {
  const [updateProgram, { loading: submitting }] = useMutation(UpdateProgramMutation);
  const showErrorSnackbar = useErrorIndicator();
  const showSuccessSnackbar = useSaveIndicator();

  const defaultValues: FormData = useMemo(
    () => ({
      id: program.id,
      name: program.name,
      status: program.status as string,
      description: program.config?.programContext?.description,
    }),
    [program],
  );

  const { control, handleSubmit, reset, register } = useForm<FormData>({
    defaultValues,
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      reset(defaultValues);
    }
  }, [isOpen, defaultValues, reset]);

  const handleClose = () => {
    reset(defaultValues);
    onClose();
  };

  const onSubmit = async (data: FormData): Promise<void> => {
    try {
      const { data: response } = await updateProgram({
        variables: {
          input: {
            id: program.id,
            status: data.status,
            programContext: {
              description: data.description,
            },
          },
        },
        refetchQueries: ['getPrograms'],
      });

      const { status: responseStatus } = response?.program?.update?.metadata || {};

      if (responseStatus >= 400) {
        showErrorSnackbar('Failed to update programs');
        return;
      }

      showSuccessSnackbar();
      onClose();
    } catch (error) {
      showErrorSnackbar('Failed to update programs');
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <Dialog.Content size="2" maxWidth="35rem">
        <Dialog.Title>Edit {program.name}</Dialog.Title>
        <form className="mt-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-y-4 w-full">
            <Flex key={program.id} direction="column" gap="4">
              <Flex direction="column" gap="1">
                <Text as="label" size="2" weight="bold">
                  Program Status
                </Text>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select.Root value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger placeholder="Select program status" />
                      <Select.Content position="popper">
                        {Object.values(ProgramStatus)
                          .filter(
                            (status) =>
                              status !== ProgramStatus.ReferralOnly ||
                              checkFeature(program.features, FeatureName.ProgramsReferral),
                          )
                          .map((status) => (
                            <Select.Item key={status} value={status}>
                              {ProgramStatusDisplay[status]}
                            </Select.Item>
                          ))}
                      </Select.Content>
                    </Select.Root>
                  )}
                />
              </Flex>
              <Flex direction="column" gap="1">
                <Text as="label" size="2" weight="bold">
                  Program Description
                </Text>
                <TextArea
                  id="description"
                  name="description"
                  size="3"
                  placeholder="Program description"
                  {...register('description')}
                />
              </Flex>
            </Flex>
            <Flex gap="4" justify="end" mt="4">
              <Button variant="outline" type="button" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={submitting} loading={submitting}>
                Update
              </Button>
            </Flex>
          </div>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default EditProgramModal;

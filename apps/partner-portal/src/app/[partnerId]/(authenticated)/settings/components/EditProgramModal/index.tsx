import { But<PERSON>, Callout, Dialog, Flex, Select, Text, TextArea } from '@radix-ui/themes';
import useErrorIndicator from '@/spa-legacy/common/hooks/useErrorIndicator';
import useSaveIndicator from '@/spa-legacy/common/hooks/useSaveIndicator';
import { useMutation } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, type Program, ProgramStatus } from '@bybeam/platform-types';
import { Controller, useForm } from 'react-hook-form';
import UpdateProgramMutation from './UpdateProgramMutation.graphql';
import { ProgramStatusDisplay } from '../../../../../../spa-legacy/portal/pages/Settings/utils';
import { JSX, useEffect, useMemo, useState } from 'react';
import { InfoCircledIcon } from '@radix-ui/react-icons';

export interface EditProgramModalProps {
  program: Program;
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  id: string;
  name: string;
  status: string;
  description?: string;
}

const EditProgramModal = ({ program, isOpen, onClose }: EditProgramModalProps): JSX.Element => {
  const [updateProgram, { loading: submitting }] = useMutation(UpdateProgramMutation);
  const showErrorSnackbar = useErrorIndicator();
  const showSuccessSnackbar = useSaveIndicator();
  const [sanitizationError, setSanitizationError] = useState(false);

  const defaultValues: FormData = useMemo(
    () => ({
      id: program.id,
      name: program.name,
      status: program.status as string,
      description: program.config?.programContext?.description,
    }),
    [program],
  );

  const {
    control,
    handleSubmit,
    reset,
    register,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues,
    mode: 'onChange',
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      reset(defaultValues);
    }
  }, [isOpen, defaultValues, reset]);

  const handleClose = () => {
    reset(defaultValues);
    onClose();
  };

  const onSubmit = async (data: FormData): Promise<void> => {
    setSanitizationError(false);
    try {
      const { data: response } = await updateProgram({
        variables: {
          input: {
            id: program.id,
            status: data.status,
            programContext: {
              description: data.description,
            },
          },
        },
        refetchQueries: ['getPrograms'],
      });

      const { status: responseStatus } = response?.program?.update?.metadata || {};

      if (responseStatus >= 400) {
        responseStatus === 400 && setSanitizationError(true);

        showErrorSnackbar('Failed to update program');
        return;
      }

      showSuccessSnackbar();
      onClose();
    } catch (error) {
      showErrorSnackbar('Failed to update program');
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <Dialog.Content size="2" maxWidth="35rem">
        <Dialog.Title>Edit {program.name}</Dialog.Title>
        <form className="mt-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-y-4 w-full">
            <Flex key={program.id} direction="column" gap="4">
              <Flex direction="column" gap="1">
                <Text as="label" size="2" weight="bold" htmlFor="status">
                  Program Status
                </Text>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select.Root value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger placeholder="Select program status" />
                      <Select.Content position="popper">
                        {Object.values(ProgramStatus)
                          .filter(
                            (status) =>
                              status !== ProgramStatus.ReferralOnly ||
                              checkFeature(program.features, FeatureName.ProgramsReferral),
                          )
                          .map((status) => (
                            <Select.Item key={status} value={status}>
                              {ProgramStatusDisplay[status]}
                            </Select.Item>
                          ))}
                      </Select.Content>
                    </Select.Root>
                  )}
                />
              </Flex>
              <Flex direction="column" gap="1">
                <Text as="label" size="2" weight="bold" htmlFor="description">
                  Program Description
                </Text>
                <TextArea
                  id="description"
                  size="3"
                  placeholder="Program description"
                  {...register('description', {
                    required: false,
                    maxLength: {
                      value: 2000,
                      message: 'Description must be 10 characters or less',
                    },
                  })}
                />
                {errors.description ? (
                  <Text color="red" size="1" aria-live="polite">
                    {errors.description.message}
                  </Text>
                ) : (
                  <Text size="1" color="gray">
                    2000 characters max
                  </Text>
                )}
                {sanitizationError && (
                  <Callout.Root variant="soft" color="red">
                    <Callout.Icon>
                      <InfoCircledIcon />
                    </Callout.Icon>
                    <Callout.Text aria-live="polite">
                      There was a problem with your description. Please try again with different
                      text.
                    </Callout.Text>
                  </Callout.Root>
                )}
              </Flex>
            </Flex>
            <Flex gap="4" justify="end" mt="4">
              <Button variant="outline" type="button" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={submitting} loading={submitting}>
                Save
              </Button>
            </Flex>
          </div>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default EditProgramModal;

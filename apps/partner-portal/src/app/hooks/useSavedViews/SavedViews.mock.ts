import GET_SAVED_VIEWS_QUERY from './savedViews.graphql';

export const saved_views_mock = {
  request: {
    query: GET_SAVED_VIEWS_QUERY,
  },
  result: {
    data: {
      currentUser: {
        id: 'mock-user-id',
        savedViews: [
          {
            id: 'mock-saved-view-id-1',
            name: '📜 Documents with defects',
            isPublic: false,
            filters: {},
          },
          {
            id: 'mock-saved-view-id-2',
            name: '🪜 Escalated cases',
            isPublic: false,
            filters: {
              type: 'cases',
              query: { tab: 'All' },
              routeParameters: { programId: 'mock-program-id', partnerId: 'mock-partner-id' },
              beamQueryVersion: '0.1',
            },
          },
          {
            id: 'mock-saved-view-id-4',
            name: '👻 Cases with no assignee',
            isPublic: false,
            filters: {},
          },
          {
            id: 'inelgible-id-3',
            name: '⚠️ Ineligible cases based on initial triage',
            isPublic: false,
            filters: {},
          },
        ],
      },
    },
  },
  newData: () => ({
    data: {
      currentUser: {
        id: 'mock-user-id',
        savedViews: [
          {
            id: 'mock-saved-view-id-1',
            name: '📜 Documents with defects',
            isPublic: false,
            filters: {},
          },
          {
            id: 'mock-saved-view-id-2',
            name: '🪜 Escalated cases',
            isPublic: false,
            filters: {
              type: 'cases',
              query: { tab: 'All' },
              routeParameters: { programId: 'mock-program-id', partnerId: 'mock-partner-id' },
              beamQueryVersion: '0.1',
            },
          },
          {
            id: 'mock-saved-view-id-4',
            name: '👻 Cases with no assignee',
            isPublic: false,
            filters: {},
          },
          {
            id: 'inelgible-id-3',
            name: '⚠️ Ineligible cases based on initial triage',
            isPublic: false,
            filters: {},
          },
        ],
      },
    },
  }),
};

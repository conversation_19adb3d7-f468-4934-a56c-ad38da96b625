import { Button } from '@radix-ui/themes';
import { type ReactNode, useCallback } from 'react';
import useSnackbar, { type SnackbarKey } from './useSnackbar';

export default function useErrorIndicator(): (message?: ReactNode, retry?: () => void) => void {
  const { showSnackbar } = useSnackbar();
  return useCallback(
    (message = 'Something went wrong!', retry?: () => void): SnackbarKey =>
      showSnackbar(
        message,
        retry ? (
          <Button variant="ghost" onClick={retry}>
            Try Again
          </Button>
        ) : undefined,
      ),
    [showSnackbar],
  );
}

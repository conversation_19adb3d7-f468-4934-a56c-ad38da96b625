import { Button } from '@radix-ui/themes';
import { useCallback } from 'react';
import useSnackbar, { type SnackbarKey } from './useSnackbar';

export default function useSaveIndicator(): () => void {
  const { showSnackbar, removeSnackbar } = useSnackbar();
  return useCallback(
    (): SnackbarKey =>
      showSnackbar('All changes saved!', (key: SnackbarKey) => (
        <Button variant="ghost" onClick={() => removeSnackbar(key)}>
          Dismiss
        </Button>
      )),
    [showSnackbar, removeSnackbar],
  );
}

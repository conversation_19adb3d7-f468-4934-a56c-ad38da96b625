import ErrorDisplay from '@/spa-legacy/common/components/ErrorDisplay';
import Color, { BackgroundColors } from '@/spa-legacy/common/utilities/Color';
import { Pencil1Icon } from '@radix-ui/react-icons';
import { Button, Flex, Heading } from '@radix-ui/themes';
import cx from 'classnames';
import type React from 'react';

interface ReviewSectionProps {
  title: string;
  children: React.ReactNode;
  onEdit?: () => void;
  isComplete?: boolean;
}

// TODO better abstraction here?
function ReviewPanel({
  title,
  children,
  onEdit,
  isComplete = false,
}: ReviewSectionProps): JSX.Element {
  return (
    <Flex direction="column" justify="between" gap="2">
      <div
        className={cx(
          `${BackgroundColors[Color.LightBackground]} py-6 px-6 lg:px-8 rounded grow`,
          !isComplete && 'border border-error',
        )}
      >
        <Flex gap="4" justify="between" mb="3">
          <Heading size="4" as="h2">
            {title}
          </Heading>
          {onEdit && (
            <Button type="button" variant="ghost" onClick={onEdit}>
              <Pencil1Icon />
              Edit
            </Button>
          )}
        </Flex>
        <Flex direction="column" gap="4">
          {children}
        </Flex>
      </div>
      <ErrorDisplay message="Section incomplete" visible={!isComplete} />
    </Flex>
  );
}

export default ReviewPanel;

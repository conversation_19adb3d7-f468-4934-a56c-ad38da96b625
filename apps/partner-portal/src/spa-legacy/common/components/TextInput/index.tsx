import {
  characterCountValidation,
  exactCharacterCountValidation,
  minCharacterCountValidation,
  numberValidation,
} from '@/app/hooks/useForm';
import useHelperTextId from '@/spa-legacy/common/hooks/contexts/useHelperTextId';
import { getLabelProps } from '@/spa-legacy/utilities/application/utils';
import { TextInputType } from '@bybeam/platform-types';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { InputAdornment, type InputBaseComponentProps, TextField } from '@mui/material';
import { IconButton } from '@radix-ui/themes';
import { useMask } from '@react-input/mask';
import cx from 'classnames';
import React, { useMemo } from 'react';
import Color from '../../../../spa-legacy/common/utilities/Color';
import { type Mask, formatIfPhone } from '../../../../spa-legacy/common/utilities/format';
import Icon from '../Icon';
import type { IconType } from '../Icon/Icon.types';
import Tooltip from '../Tooltip';

type MappedInputTypes = { [type in TextInputType]: string };

const mappedInputTypes: MappedInputTypes = {
  ...(Object.fromEntries(
    Object.values(TextInputType).map((type) => [type, type]),
  ) as MappedInputTypes),
  [TextInputType.Currency]: TextInputType.Text,
  [TextInputType.Signature]: TextInputType.Text,
};

const formatTextInputValue = (value, inputType): string => {
  if (inputType === TextInputType.Phone) {
    return formatIfPhone(value);
  }
  return value;
};
export type TextInputValidationError =
  | 'charCountMax'
  | 'charCountMin'
  | 'charCountExact'
  | 'generalValidation';
export interface TextInputProps {
  id: string;
  label: string;
  type?: 'text' | 'date' | 'email' | 'tel' | 'password' | 'currency' | 'number' | 'signature';
  value: string;
  onChange?: (value: string) => void;
  onDragStart?: (value) => void;
  onDrop?: (value) => void;
  required?: boolean;
  disabled?: boolean;
  // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
  ['data-cy']?: string;
  helperText?: string;
  error?: string | boolean;
  // indicates to browsers what type of field this is so they can autocomplete it
  autoComplete?: string;
  inputProps?: InputBaseComponentProps;
  multiline?: boolean;
  rows?: number;
  fullWidth?: boolean;
  tooltip?: string;
  icon?: IconType;
  onError?: (validationError: TextInputValidationError) => void;
  onPaste?: (value) => void;
  onFocus?: (value) => void;
  autoFocus?: boolean;
  mask?: Mask;
  characterCounter?: { max?: number; min?: number; exact?: number };
}

function HelperText({
  error,
  displayedHelperText,
}: { error: string | boolean | undefined; displayedHelperText: string }) {
  return (
    <>
      {error && <span>{error}</span>}
      <span className="block">{displayedHelperText}</span>
    </>
  );
}

export default function TextInput({
  onChange,
  type = 'text',
  error,
  helperText,
  inputProps,
  multiline,
  rows,
  fullWidth,
  tooltip,
  icon,
  disabled,
  value,
  mask,
  characterCounter,
  onError,
  ...rest
}: TextInputProps): JSX.Element {
  const [focused, setFocused] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);
  const [textType, setTextType] = React.useState(type);

  const handleShowPasswordClick = () => {
    setShowPassword((show) => !show);
    setTextType(!showPassword ? 'text' : 'password');
  };

  const isCurrency = type === 'currency';
  const formatCurrency = (val: string): string => Number(val).toFixed(2);

  const validation = (val: string): boolean => {
    if (isCurrency && numberValidation(val as unknown as number)) return false;
    return true;
  };

  const handleChange = (val: string): void => {
    const isValid = validation(val);
    if (isValid) onChange(val);
  };

  const widthClass = multiline ? 'w-multilineField' : 'w-field';

  const { displayedError, displayedHelperText } = useMemo(() => {
    let displayedHelperText = helperText;
    let displayedError: string | boolean | React.ReactNode = error;
    if (characterCounter?.max || characterCounter?.min || characterCounter?.exact) {
      displayedHelperText = `${(value?.toString() ?? '').length}/${characterCounter?.max || characterCounter?.min || characterCounter?.exact} characters`;
      const characterCountMaxError =
        !!characterCounter.max && characterCountValidation(characterCounter.max, rest.label, value);
      const characterCountMinError =
        !!characterCounter.min &&
        minCharacterCountValidation({
          minCharacters: characterCounter.min,
          fieldName: rest.label,
          value,
        });
      const characterCountExactError =
        !!characterCounter.exact &&
        exactCharacterCountValidation({
          exact: characterCounter.exact,
          fieldName: rest.label,
          value,
        });

      if (characterCountMaxError || characterCountMinError || characterCountExactError) {
        displayedError = <HelperText error={error} displayedHelperText={displayedHelperText} />;
        characterCountMaxError && onError?.('charCountMax');
        characterCountMinError && onError?.('charCountMin');
        characterCountExactError && onError?.('charCountExact');
      } else {
        onError?.(null);
      }
    } else if (displayedError) {
      onError?.('generalValidation');
    } else if (typeof error === 'string') displayedHelperText = error;

    return { displayedHelperText, displayedError };
  }, [helperText, error, characterCounter, rest.label, value, onError]);

  const helperTextId = useHelperTextId();
  const helperTextProps = {
    id: helperTextId,
    sx: characterCounter ? { textAlign: 'right' } : {},
  };
  return (
    <div
      className={cx('flex max-w-full items-start', {
        [widthClass]: !fullWidth,
      })}
    >
      <TextField
        {...rest}
        {...(!isCurrency
          ? {
              value,
            }
          : {
              value: value && !focused ? formatCurrency(value) : value,
              onFocus: (): void => setFocused(true),
              onBlur: (e): void => {
                setFocused(false);
                if (e.target.value !== '') onChange(formatCurrency(e.target.value));
              },
            })}
        InputLabelProps={getLabelProps(!!inputProps?.readOnly, value)}
        disabled={disabled}
        variant={'filled'}
        // mask can only be used with type 'text', hence we override the type
        type={mappedInputTypes[mask ? TextInputType.Text : textType]}
        error={!!displayedError}
        helperText={displayedError || displayedHelperText}
        FormHelperTextProps={helperTextProps}
        onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void =>
          onChange && handleChange(formatTextInputValue(e.target.value, mappedInputTypes[type]))
        }
        fullWidth
        inputProps={{
          ...inputProps,
          'aria-errormessage': helperTextId,
          'aria-describedby': helperTextId,
        }}
        multiline={multiline}
        minRows={multiline ? rows || 1 : undefined}
        inputRef={mask && useMask({ mask, replacement: { _: /\d/ } })}
        InputProps={{
          ...(type === 'currency' && {
            startAdornment: (
              <InputAdornment position="start" className="!-mr-[.75rem]">
                $
              </InputAdornment>
            ),
          }),
          ...(type === 'password' && {
            endAdornment: (
              <InputAdornment position="end">
                <Tooltip title={showPassword ? 'Hide Password' : 'Show Password'}>
                  <IconButton
                    onClick={handleShowPasswordClick}
                    variant="ghost"
                    type="button"
                    aria-label="Toggle password visibility"
                    aria-pressed={showPassword}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </Tooltip>
              </InputAdornment>
            ),
          }),
          ...(tooltip && {
            endAdornment: (
              <InputAdornment position="end">
                <Tooltip title={tooltip} placement="right-end">
                  <div className="pt-1">
                    <Icon
                      type="infoOutline"
                      color={disabled ? Color.DisabledLabel : Color.Text}
                      size="LG"
                    />
                  </div>
                </Tooltip>
              </InputAdornment>
            ),
          }),
          ...(icon && {
            endAdornment: (
              <Icon type={icon} color={disabled ? Color.DisabledLabel : Color.Text} size="LG" />
            ),
          }),
        }}
      />
    </div>
  );
}

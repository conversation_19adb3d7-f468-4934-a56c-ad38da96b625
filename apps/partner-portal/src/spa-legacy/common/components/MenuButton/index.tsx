import { Menu, MenuItem } from '@mui/material';
import { Button, type ButtonProps as RadixButtonProps } from '@radix-ui/themes';
import { type ReactNode, useRef, useState } from 'react';
import Color, { BackgroundColors } from '../../../../spa-legacy/common/utilities/Color';
import Typography from '../Typography';
import MenuButtonOptions from './Options';

export interface MenuButtonOption<T> {
  label: ReactNode;
  value: T;
  key: string | number;
  icon?: ReactNode;
  disabled?: boolean;
  hidden?: boolean;
}

export interface MenuButtonProps<T> extends Omit<RadixButtonProps, 'onClick'> {
  id: string;
  onClick: (option: T) => void;
  options: MenuButtonOption<T>[];
  prompt?: string;
  isMultiselect?: boolean;
  selectedValues?: T[];
  title?: string;
}

export default function MenuButton<T>(props: MenuButtonProps<T>): JSX.Element {
  const { id, onClick, options, prompt, isMultiselect, selectedValues, title, ...rest } = props;
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLButtonElement>();
  return (
    <>
      <Button
        ref={ref}
        onClick={(): void => setOpen(true)}
        id={`${id}-button`}
        aria-controls={open ? `${id}-menu` : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        {...rest}
      />
      <Menu
        id={`${id}-menu`}
        anchorEl={ref.current}
        open={open}
        onClose={(): void => setOpen(false)}
        MenuListProps={{ 'aria-labelledby': `${id}-button` }}
      >
        <div className={isMultiselect ? BackgroundColors[Color.LightBackground] : ''}>
          {title && (
            <div className="py-3 px-4">
              <Typography variant="label" color={Color.Text}>
                {title}
              </Typography>
            </div>
          )}
          {prompt && (
            <MenuItem disabled>
              <Typography variant="label" color={Color.TextSecondary}>
                {prompt}
              </Typography>
            </MenuItem>
          )}
          <MenuButtonOptions {...props} setOpen={setOpen} />
        </div>
      </Menu>
    </>
  );
}

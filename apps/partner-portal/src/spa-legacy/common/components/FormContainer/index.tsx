import useCustomPageTitle from '@/spa-legacy/common/hooks/useCustomPageTitle';
import { Box, Flex, Heading } from '@radix-ui/themes';
import { type FormEvent, type ReactNode, useCallback } from 'react';
import styles from './FormContainer.module.css';

interface FormContainerProps {
  children: ReactNode;
  buttons?: ReactNode;
  onSubmit?: () => void;
  title?: string;
  applyMinHeight?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: reason
  errors?: Record<string, any>;
  noValidate?: boolean;
  chip?: ReactNode;
}

export default function FormContainer({
  children,
  buttons,
  onSubmit,
  title,
  chip,
  applyMinHeight = true,
  noValidate = true,
}: FormContainerProps): JSX.Element {
  const onSubmitPreventDefault = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      onSubmit();
    },
    [onSubmit],
  );
  useCustomPageTitle(title);

  return (
    <Flex asChild direction="column" align="center" className={styles.form}>
      <form onSubmit={onSubmitPreventDefault} noValidate={noValidate}>
        {chip && <Box pb="4">{chip}</Box>}
        {title && (
          <Heading id="form-page-title" as="h1" size="8" align="center">
            {title}
          </Heading>
        )}
        <Flex
          direction="column"
          align="center"
          gap="4"
          width="100%"
          mt="8"
          pb={buttons ? '6' : '0'}
          className={`${styles.childrenContainer} ${applyMinHeight ? styles.minHeight : ''}`}
          aria-live="polite"
        >
          {children}
        </Flex>
        {buttons}
      </form>
    </Flex>
  );
}

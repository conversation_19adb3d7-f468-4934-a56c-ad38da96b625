import { Button, Flex } from '@radix-ui/themes';
import Modal from '../Modal';
import Typography from '../Typography';

interface DeleteModalProps {
  onClose: () => void;
  onDelete: () => void;
  loading: boolean;
  text: string;
  label: string;
  isOpen: boolean;
}

export default function DeleteModal({
  onDelete,
  onClose,
  text,
  label,
  loading,
  isOpen,
}: DeleteModalProps): JSX.Element {
  return (
    <Modal size="S" onClickClose={onClose} isOpen={isOpen} title={`Remove ${label}?`}>
      <Flex direction="column" gap="4">
        <Typography variant="largeBody"> {text} </Typography>
        <Flex gap="4" justify="end" className="mt-8">
          <Button type="button" onClick={onClose} variant="soft">
            Cancel
          </Button>
          <Button type="button" loading={loading} onClick={onDelete}>
            Remove {label}
          </Button>
        </Flex>
      </Flex>
    </Modal>
  );
}

import type { Meta, StoryObj } from '@storybook/nextjs';
import DeleteModal from './index';

const meta: Meta<typeof DeleteModal> = {
  component: DeleteModal,
  args: {
    onClose: () => {},
    onDelete: () => {},
  },
};

export default meta;

type Story = StoryObj<typeof DeleteModal>;

export const Default: Story = {
  args: {
    isOpen: true,
    label: 'Document',
    text: 'Are you sure you want to remove this document? This action cannot be undone.',
    loading: false,
  },
};

export const Loading: Story = {
  args: {
    isOpen: true,
    label: 'User',
    text: 'Are you sure you want to remove this user from the system?',
    loading: true,
  },
};

export const LongText: Story = {
  args: {
    isOpen: true,
    label: 'Verification File',
    text: "This will delete the verification file you've uploaded and all associated metadata. This action cannot be undone and may affect any applications that reference this data. Please confirm you want to proceed.",
    loading: false,
  },
};

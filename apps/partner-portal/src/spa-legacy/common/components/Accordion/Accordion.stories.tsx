import type { Meta, StoryObj } from '@storybook/nextjs';
import Accordion from './index';

const meta: Meta<typeof Accordion> = {
  component: Accordion,
};

export default meta;

type Story = StoryObj<typeof Accordion>;

export const Default: Story = {
  args: {
    title: 'Basic Accordion',
    children: (
      <div>
        <p>This is the accordion content.</p>
        <p>It can contain any React elements.</p>
      </div>
    ),
  },
};

export const InitiallyOpen: Story = {
  args: {
    title: 'Initially Open Accordion',
    initializeOpen: true,
    children: (
      <div>
        <p>This accordion starts in an open state.</p>
      </div>
    ),
  },
};

export const WithAction: Story = {
  args: {
    title: 'Accordion with Action',
    initializeOpen: true,
    action: {
      children: 'Edit',
      onClick: () => {},
    },
    children: (
      <div>
        <p>This accordion has an action button that appears when open.</p>
      </div>
    ),
  },
};

export const WithMenuAction: Story = {
  args: {
    title: 'Accordion with Menu',
    initializeOpen: true,
    action: {
      variant: 'menu' as const,
      id: 'accordion-menu',
      children: 'Actions',
      onClick: () => {},
      options: [
        { label: 'Edit', value: 'edit', key: 'edit' },
        { label: 'Delete', value: 'delete', key: 'delete' },
        { label: 'Duplicate', value: 'duplicate', key: 'duplicate' },
      ],
    },
    children: (
      <div>
        <p>This accordion has a menu button with multiple options.</p>
      </div>
    ),
  },
};

export const InMenu: Story = {
  args: {
    title: 'Compact Accordion',
    inMenu: true,
    children: (
      <div>
        <p>This is a more compact version for use in menus.</p>
      </div>
    ),
  },
};

import type { Meta, StoryObj } from '@storybook/nextjs';
import DragzoneFileUpload from './index';

const meta: Meta<typeof DragzoneFileUpload> = {
  component: DragzoneFileUpload,
  args: {
    onDrop: () => {},
  },
};

export default meta;

type Story = StoryObj<typeof DragzoneFileUpload>;

export const Default: Story = {
  args: {
    acceptedFileTypes: 'image/*,application/pdf',
    maxFiles: 1,
  },
};

export const MultipleFiles: Story = {
  args: {
    acceptedFileTypes: 'image/*,application/pdf',
    maxFiles: 5,
  },
};

export const PDFOnly: Story = {
  args: {
    acceptedFileTypes: 'application/pdf',
    maxFiles: 1,
  },
};

export const ImagesOnly: Story = {
  args: {
    acceptedFileTypes: 'image/*',
    maxFiles: 3,
  },
};

export const WithCustomClass: Story = {
  args: {
    acceptedFileTypes: 'image/*,application/pdf',
    maxFiles: 1,
    className: 'border-4 border-dashed',
  },
};

export const LargeMaxSize: Story = {
  args: {
    acceptedFileTypes: 'image/*,application/pdf',
    maxFiles: 1,
    maxSize: 10485760, // 10MB in bytes
  },
};

export const Disabled: Story = {
  args: {
    acceptedFileTypes: 'image/*,application/pdf',
    maxFiles: 1,
    disabled: true,
  },
};

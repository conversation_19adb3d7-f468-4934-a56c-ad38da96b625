import Color, { BackgroundColors, BorderColors } from '@/spa-legacy/common/utilities/Color';
import pluralize from '@bybeam/platform-lib/format/pluralize';
import { Button } from '@radix-ui/themes';
import cx from 'classnames';
import { useEffect } from 'react';
import { type DropzoneProps, useDropzone } from 'react-dropzone';
import Icon from '../Icon';
import Typography from '../Typography';

export default function DragzoneFileUpload({
  className,
  onUpload,
  maxFiles = 1,
  reachedMaxFiles = false,
  ...dropZoneProps
}: DropzoneProps & {
  className?: string;
  onUpload: (files: File[]) => void;
  reachedMaxFiles?: boolean;
}): JSX.Element {
  const { acceptedFiles, getRootProps, getInputProps, open } = useDropzone({
    noClick: true,
    noKeyboard: true,
    maxFiles,
    preventDropOnDocument: reachedMaxFiles,
    ...(dropZoneProps || {}),
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: onUpload is not required to be memoized
  useEffect(() => {
    if (acceptedFiles?.length && onUpload) onUpload(acceptedFiles);
  }, [acceptedFiles]);

  return (
    <div
      className={cx(
        `h-26 w-full p-4 border rounded border-dashed ${BackgroundColors[Color.InputBackground]} ${
          BorderColors[Color.InputBorder]
        }`,
        className,
      )}
    >
      {!reachedMaxFiles && (
        <div {...getRootProps()} className="flex flex-col gap-2 items-center">
          <input {...getInputProps()} disabled={reachedMaxFiles} />
          <Icon type="upload" size="LG" />
          <Typography variant="body" bold>
            <>
              Drag and drop a file or{' '}
              <Button onClick={open} variant="ghost" className="lowercase hover:underline">
                choose from folder
              </Button>
            </>
          </Typography>
        </div>
      )}
      {reachedMaxFiles && (
        <div className="flex flex-col gap-2 items-center">
          <Icon type="UploadDoneOutline" size="LG" />
          <Typography variant="body" bold>
            {maxFiles} {pluralize('file', maxFiles)} selected
          </Typography>
        </div>
      )}
    </div>
  );
}

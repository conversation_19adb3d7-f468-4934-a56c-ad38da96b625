import { Cross1Icon } from '@radix-ui/react-icons';
import { Flex, Heading, IconButton, Theme } from '@radix-ui/themes';
import type { ReactNode } from 'react';
import ReactModal from 'react-modal';
import Color, { BackgroundColors } from '../../../../spa-legacy/common/utilities/Color';

export interface ModalProps {
  title: ReactNode;
  children: ReactNode;
  isOpen: boolean;
  onClickClose: () => void;
  size?: 'S' | 'M' | 'L';
  preventClose?: boolean;
}

const ModalSizes = {
  S: 'max-w-[35rem]',
  M: 'max-w-[52rem]',
  L: 'max-w-[75rem]',
};

export default function Modal({
  title,
  children,
  isOpen,
  size = 'S',
  onClickClose,
  preventClose = false,
}: ModalProps): JSX.Element {
  return (
    <ReactModal
      isOpen={isOpen}
      onRequestClose={onClickClose}
      overlayClassName={`fixed inset-px z-50 ${
        BackgroundColors[Color.Overlay]
      } bg-opacity-75 backdrop-filter backdrop-blur-sm`}
      className={`relative m-auto my-4 ${
        ModalSizes[size]
      } max-h-[calc(100vh-2rem)] rounded-lg overflow-auto ${BackgroundColors[Color.White]}`}
      {...(preventClose && {
        shouldCloseOnOverlayClick: false,
        shouldCloseOnEsc: false,
      })}
    >
      <Theme accentColor="indigo">
        <Flex direction="column" p="4">
          <Flex justify="between" align="start">
            <Heading weight="medium" as="h2" mb="2">
              {title}
            </Heading>
            <IconButton
              onClick={onClickClose}
              variant="ghost"
              aria-label="Close modal dialog"
              disabled={preventClose}
              color="gray"
              highContrast
            >
              <Cross1Icon height="24" width="24" />
            </IconButton>
          </Flex>
          <div>{children}</div>
        </Flex>
      </Theme>
    </ReactModal>
  );
}

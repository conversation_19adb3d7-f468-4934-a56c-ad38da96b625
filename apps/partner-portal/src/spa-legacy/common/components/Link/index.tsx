import { UrlObject } from 'node:url';
import Color from '@/spa-legacy/common/utilities/Color';
import NextLink, { LinkProps } from 'next/link';
import { HTMLProps } from 'react';
import Typography, { type Variant } from '../Typography';

export interface CustomLinkProps extends Omit<LinkProps<HTMLProps<HTMLAnchorElement>>, 'href'> {
  to: UrlObject | LinkProps<string>['href'];
  external?: boolean;
  variant?: Variant;
  bold?: boolean;
}

// This is basically an extension of Next's that applies our
// correct styling.
export default function Link({
  children,
  external = false,
  variant = 'body',
  bold = true,
  to,
  ...rest
}: CustomLinkProps): JSX.Element {
  const copy = (
    <Typography variant={variant} tag="span" color={Color.Link} bold={bold}>
      {children}
    </Typography>
  );
  if (external) {
    const { className, rel, target } = rest;
    return (
      <a href={to as string} target={target} rel={rel} className={className}>
        {copy}
      </a>
    );
  }

  return (
    <NextLink href={to} {...rest}>
      {copy}
    </NextLink>
  );
}

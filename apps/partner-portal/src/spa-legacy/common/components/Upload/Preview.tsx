import CanAccess from '@/app/components/features/CanAccess';
import type { EditDocumentButtonProps } from '@/spa-legacy/@types/document';
import { Cross2Icon } from '@radix-ui/react-icons';
import { IconButton } from '@radix-ui/themes';
import cx from 'classnames';
import { type MouseEvent, useState } from 'react';
import Color, {
  BackgroundColors,
  BorderColors,
} from '../../../../spa-legacy/common/utilities/Color';
import useDeleteModal from '../../../common/hooks/useDeleteModal';
import Icon from '../Icon';
import Loading from '../Loading';
import Tooltip from '../Tooltip';
import Typography from '../Typography';

enum DocumentType {
  Image = 'image',
  PDF = 'pdf',
  CSV = 'csv',
  Other = 'other',
}

const getDocumentType = (mimetype: string): DocumentType => {
  if (mimetype.match('image.*')) return DocumentType.Image;
  if (mimetype?.match('pdf')) return DocumentType.PDF;
  if (mimetype?.match('csv')) return DocumentType.CSV;
  return DocumentType.Other;
};

interface UploadPreviewProps {
  editable?: boolean;
  id?: string;
  loading?: boolean;
  mimetype: string;
  name?: string;
  pinned?: boolean;
  pinSelectedFile?: (id: string, pinned: boolean) => void;
  removeSelectedFile?: (id?: string, url?: string) => void;
  url: string;
  variant?: 'error';
  onOpen?: () => void;
  editButtonProps?: EditDocumentButtonProps;
}

// TODO write tests for this
export default function UploadPreview({
  editable,
  id,
  loading = false,
  mimetype,
  name,
  pinned,
  pinSelectedFile,
  removeSelectedFile,
  url,
  variant,
  onOpen,
  editButtonProps,
}: UploadPreviewProps): JSX.Element {
  const type = getDocumentType(mimetype);
  const [removing, setRemoving] = useState(false);
  const onRemove = (): void => {
    setRemoving(true);
    removeSelectedFile(id, url);
  };

  const { DeleteModalComponent, openModal } = useDeleteModal({
    label: 'Document',
    text: 'This will delete the document you’ve uploaded. This action cannot be undone.',
    loading,
    onRemove,
  });
  const onClickRemove = (e: MouseEvent<HTMLButtonElement>): void => {
    e.stopPropagation();
    openModal();
  };
  const onClickPin = (): void => pinSelectedFile(id, !pinned);
  const noPreview = type === DocumentType.CSV;

  const removeButton = (
    <IconButton
      disabled={editButtonProps?.disabled || loading}
      variant="ghost"
      onClick={onClickRemove}
      aria-label="Remove document"
    >
      <Cross2Icon />
    </IconButton>
  );

  return (
    <div className="flex gap-4">
      <div
        className={cx('flex grow p-2 pl-6 border border-solid rounded-xl items-center', {
          [`${BorderColors[Color.Error]}`]: variant === 'error',
        })}
        aria-invalid={variant === 'error'}
      >
        <a
          className="flex grow items-center justify-items-left gap-x-2 overflow-auto break-all h-10"
          href={url}
          target="_blank"
          rel="noreferrer"
          onClick={onOpen}
        >
          {!noPreview && (
            <div
              className={`flex items-center justify-left shrink-0 overflow-hidden h-full w-13 ${
                BackgroundColors[Color.TableBackground]
              }`}
            >
              {type === DocumentType.Image && (
                // Only Safari supports HEIC/HEIF in img tags
                <img src={url} alt="Thumb" className="max-w-full max-h-full" />
              )}
              {type === DocumentType.PDF && (
                <object
                  data={url}
                  className="max-w-full max-h-full"
                  type="application/pdf"
                  aria-label="Preview document"
                />
              )}
            </div>
          )}
          {name && <Typography variant="body">{name}</Typography>}
        </a>
        {editable && editButtonProps?.tooltip ? (
          <Tooltip title={editButtonProps.tooltip}>{removeButton}</Tooltip>
        ) : (
          editable && removeButton
        )}
        {removing && <Loading size="SM" />}
      </div>
      {pinSelectedFile && (
        <CanAccess>
          <Tooltip title={pinned ? 'Unpin Document' : 'Pin Document'}>
            <IconButton
              variant="ghost"
              onClick={onClickPin}
              aria-label={pinned ? 'Unpin document' : 'Pin document'}
            >
              <Icon type={pinned ? 'pin' : 'pinOutlined'} size="MD" />
            </IconButton>
          </Tooltip>
        </CanAccess>
      )}

      {editable && DeleteModalComponent}
    </div>
  );
}

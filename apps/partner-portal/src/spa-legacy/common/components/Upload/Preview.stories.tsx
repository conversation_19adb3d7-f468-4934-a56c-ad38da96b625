import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK } from '@/tests/mocks/hasAccessMocks';
import UploadPreview from './Preview';

const meta: Meta<typeof UploadPreview> = {
  component: UploadPreview,
  args: {
    removeSelectedFile: () => {},
    pinSelectedFile: () => {},
  },
  parameters: {
    apolloClient: {
      mocks: [HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK],
    },
  },
};

export default meta;

type Story = StoryObj<typeof UploadPreview>;

export const PDFDocument: Story = {
  args: {
    id: 'pdf-1',
    name: 'sample-document.pdf',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    mimetype: 'application/pdf',
    editable: true,
    loading: false,
  },
};

export const ImageDocument: Story = {
  args: {
    id: 'img-1',
    name: 'profile-photo.jpg',
    url: 'https://via.placeholder.com/400x300',
    mimetype: 'image/jpeg',
    editable: true,
    loading: false,
  },
};

export const CSVDocument: Story = {
  args: {
    id: 'csv-1',
    name: 'data-export.csv',
    url: '#',
    mimetype: 'text/csv',
    editable: true,
    loading: false,
  },
};

export const WithError: Story = {
  args: {
    id: 'pdf-2',
    name: 'invalid-document.pdf',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    mimetype: 'application/pdf',
    editable: true,
    loading: false,
    variant: 'error',
  },
};

export const Pinned: Story = {
  args: {
    id: 'pdf-3',
    name: 'important-document.pdf',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    mimetype: 'application/pdf',
    editable: true,
    loading: false,
    pinned: true,
  },
};

export const Loading: Story = {
  args: {
    id: 'pdf-4',
    name: 'uploading-document.pdf',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    mimetype: 'application/pdf',
    editable: true,
    loading: true,
  },
};

export const NotEditable: Story = {
  args: {
    id: 'pdf-5',
    name: 'readonly-document.pdf',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    mimetype: 'application/pdf',
    editable: false,
    loading: false,
  },
};

export const WithTooltip: Story = {
  args: {
    id: 'pdf-6',
    name: 'locked-document.pdf',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    mimetype: 'application/pdf',
    editable: true,
    loading: false,
    editButtonProps: {
      disabled: true,
      tooltip: 'This document cannot be removed because it has been submitted.',
    },
  },
};

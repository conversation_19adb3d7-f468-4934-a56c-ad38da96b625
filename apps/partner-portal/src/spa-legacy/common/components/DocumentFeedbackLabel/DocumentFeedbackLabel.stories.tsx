import { YesNoUnsure } from '@bybeam/platform-types';
import type { Meta, StoryObj } from '@storybook/nextjs';
import DocumentFeedbackLabel from './index';

const meta: Meta<typeof DocumentFeedbackLabel> = {
  component: DocumentFeedbackLabel,
  args: {
    setDocumentFeedback: () => {},
  },
};

export default meta;

type Story = StoryObj<typeof DocumentFeedbackLabel>;

const mockDocument = {
  id: 'doc-1',
  filename: 'sample-document.pdf',
  url: 'https://example.com/doc.pdf',
  mimetype: 'application/pdf',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const NoPrediction: Story = {
  args: {
    document: mockDocument,
    documentLabel: null,
    documentFeedback: null,
    predictedConfidence: 0,
    enableFeedback: true,
  },
};

export const WithHighConfidencePrediction: Story = {
  args: {
    document: mockDocument,
    documentLabel: 'PROOF_OF_IDENTITY',
    documentFeedback: null,
    predictedConfidence: 0.95,
    enableFeedback: true,
  },
};

export const WithLowConfidencePrediction: Story = {
  args: {
    document: mockDocument,
    documentLabel: 'PROOF_OF_INCOME',
    documentFeedback: null,
    predictedConfidence: 0.45,
    enableFeedback: true,
  },
};

export const WithPositiveFeedback: Story = {
  args: {
    document: mockDocument,
    documentLabel: 'PROOF_OF_IDENTITY',
    documentFeedback: {
      documentId: 'doc-1',
      preferredLabel: 'PROOF_OF_IDENTITY',
      isCorrect: YesNoUnsure.Yes,
    },
    predictedConfidence: 0.9,
    enableFeedback: true,
  },
};

export const WithNegativeFeedback: Story = {
  args: {
    document: mockDocument,
    documentLabel: 'PROOF_OF_IDENTITY',
    documentFeedback: {
      documentId: 'doc-1',
      preferredLabel: 'PROOF_OF_INCOME',
      isCorrect: YesNoUnsure.No,
    },
    predictedConfidence: 0.75,
    enableFeedback: true,
  },
};

export const FeedbackDisabled: Story = {
  args: {
    document: mockDocument,
    documentLabel: 'PROOF_OF_IDENTITY',
    documentFeedback: null,
    predictedConfidence: 0.85,
    enableFeedback: false,
  },
};

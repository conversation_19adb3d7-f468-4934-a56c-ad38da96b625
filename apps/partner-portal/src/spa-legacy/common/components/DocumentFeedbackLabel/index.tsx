import CanAccess from '@/app/components/features/CanAccess';
import type { TemporaryDocument } from '@/spa-legacy/@types/document';
import type { DocumentFeedback } from '@/spa-legacy/common/components/DocumentUpload/types';
import { YesNoUnsure } from '@bybeam/platform-types';
import { Button } from '@radix-ui/themes';
import {
  DISPLAY_PREDICTION_THRESHOLD,
  getDisplayLabel,
  isExistingDocument,
} from '../DocumentUpload/utils';
import Icon from '../Icon';
import Tooltip from '../Tooltip';
import Typography from '../Typography';

interface DocumentFeedbackLabelProps {
  document: TemporaryDocument;
  feedback: DocumentFeedback;
  enableFeedback?: boolean;
}

export default function DocumentFeedbackLabel({
  document,
  feedback,
  enableFeedback = true,
}: DocumentFeedbackLabelProps): JSX.Element {
  if (!isExistingDocument(document)) return null;

  const hasPrediction = () =>
    !!document.summary?.prediction?.label && !!document.summary?.prediction.probability;

  if (!hasPrediction()) return null;

  const isClassified = () => document.summary.prediction.probability > DISPLAY_PREDICTION_THRESHOLD;

  const documentFeedback = document.summary?.prediction?.feedback?.[0];
  const documentLabel = document.summary?.prediction?.label;

  const positiveFeedback = (): boolean => {
    if (documentFeedback) if (documentFeedback.accurate === YesNoUnsure.Yes) return true;
    return false;
  };

  const negativeFeedback = (): boolean => {
    if (documentFeedback) if (documentFeedback.accurate === YesNoUnsure.No) return true;
    return false;
  };

  const tooltipLabel = (displayLabel: string): string => {
    return `Thanks for your feedback. You indicated that ${displayLabel} is the correct document type`;
  };

  const getFeedback = (feedbackType?: YesNoUnsure) =>
    feedback.get(document.id, feedbackType, isClassified());

  return (
    <div className="flex gap-2 mb-1 justify-between">
      {isClassified() ? (
        <div className="flex gap-2 flex-row">
          <Tooltip title={'This document category was suggested by AI'}>
            <Icon size="MD" type="aiEnabled" className="pt-1" />
          </Tooltip>
          <Typography variant="body" textTransform="capitalize" bold>
            {getDisplayLabel(document.summary.prediction.label)}
          </Typography>
        </div>
      ) : (
        <Typography variant="body" textTransform="capitalize" bold>
          Unclassified
        </Typography>
      )}
      {enableFeedback && (
        <CanAccess>
          <div className="flex gap-2 flex-row mr-10">
            <Button
              variant="ghost"
              onClick={() => getFeedback(YesNoUnsure.Unsure)}
              data-testid="giveFeedback"
            >
              <Typography variant="label" bold={true}>
                Give Feedback
              </Typography>
            </Button>
            {positiveFeedback() ? (
              <Tooltip title={tooltipLabel(isClassified() ? getDisplayLabel(documentLabel) : '')}>
                <Button
                  variant="ghost"
                  disabled={!isClassified()}
                  onClick={() => getFeedback(YesNoUnsure.Yes)}
                  data-testid="positiveFeedback"
                  aria-label="Positive feedback - document classification is correct"
                >
                  <Icon type="thumbUp" size="MD" />
                </Button>
              </Tooltip>
            ) : (
              <Button
                variant="ghost"
                disabled={!isClassified()}
                onClick={() => getFeedback(YesNoUnsure.Yes)}
                data-testid="submitPositiveFeedback"
                aria-label="Give positive feedback on document classification"
              >
                <Icon type="thumbUpOutline" size="MD" />
              </Button>
            )}
            {negativeFeedback() ? (
              <Tooltip title={tooltipLabel(getDisplayLabel(documentFeedback.preferredLabel))}>
                <Button
                  variant="ghost"
                  onClick={() => getFeedback(YesNoUnsure.No)}
                  data-testid="negativeFeedback"
                  aria-label="Negative feedback - document classification is incorrect"
                >
                  <Icon type="thumbDown" size="MD" />
                </Button>
              </Tooltip>
            ) : (
              <Button
                variant="ghost"
                onClick={() => getFeedback(YesNoUnsure.No)}
                data-testid="submitNegativeFeedback"
                aria-label="Give negative feedback on document classification"
              >
                <Icon type="thumbDownOutline" size="MD" />
              </Button>
            )}
          </div>
        </CanAccess>
      )}
    </div>
  );
}

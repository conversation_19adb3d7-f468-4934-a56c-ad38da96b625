import type { CheckboxGroupField, DropdownField, DropdownValue } from '@bybeam/platform-types';
import { useMemo } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { parseFieldValues } from '../parseFieldValues';

interface ArrayReviewProps {
  answer: DropdownValue[];
  field: CheckboxGroupField | DropdownField;
  defaultValue: string;
}

export default function ArrayReview({ answer, field, defaultValue }: ArrayReviewProps) {
  if (answer && answer.length !== 0) {
    const answerWithId = useMemo(() => answer.map((data) => ({ data, id: uuidv4() })), [answer]);
    return (
      <div className="flex flex-col gap-1">
        {answerWithId.map((item) => (
          <p key={item.id}>{parseFieldValues(field.options, item.data, defaultValue)}</p>
        ))}
      </div>
    );
  }
  return <>{defaultValue}</>;
}

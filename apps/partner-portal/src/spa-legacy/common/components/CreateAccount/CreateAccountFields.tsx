import useForm, {
  fieldUpdate,
  phoneField,
  emailField,
  fullNameField,
  GenericFormData,
  checkboxField,
  ValidationMode,
  requiredField,
  defaultRequiredErrorMessage,
} from '@/app/hooks/useForm';
import useSnackbar, { SnackbarKey } from '@/spa-legacy/common/hooks/useSnackbar';
import { formatPhone, isPhoneNumber, sanitizeFullName } from '@bybeam/formatting';
import { ApplicantType, ApplicantTypeRole } from '@bybeam/platform-types';
import Checkbox from '@platform-ui-common/components/Checkbox';
import FormContainer from '@platform-ui-common/components/FormContainer';
import Link from '@platform-ui-common/components/Link';
import TextInput from '@platform-ui-common/components/TextInput';
import Typography from '@platform-ui-common/components/Typography';
import usePartner from '@platform-ui-common/hooks/contexts/usePartner';
import {
  getDefaultApplicantTypeId,
  getSortedApplicantTypes,
} from '@platform-ui-common/utilities/applicantType';
import { <PERSON><PERSON>, Flex } from '@radix-ui/themes';
import { useEffect, useMemo, useState } from 'react';
import Dropdown from '../Dropdown';
import ErrorDisplay from '../ErrorDisplay';

export interface CreateAccountProps {
  useHook: (formData: GenericFormData) => {
    doMutation: (formData: GenericFormData) => void;
    error?: string;
    loading: boolean;
    displayLoginLink?: boolean;
  };
  adminFacing?: boolean;
  applicantTypeRole?: ApplicantTypeRole;
  applicantTypes: ApplicantType[];
}

export default function CreateAccountFields({
  useHook,
  adminFacing = false,
  applicantTypeRole = ApplicantTypeRole.FirstParty,
  applicantTypes,
}: CreateAccountProps): JSX.Element {
  const { showSnackbar, removeSnackbar } = useSnackbar();
  const partner = usePartner();
  interface BaseFormData {
    fullName: string;
    email: string;
    phone: string;
    userAgreement: boolean;
    applicantTypeId: string;
  }
  const [applicantHasEmail, setApplicantHasEmail] = useState(true);

  const isThirdParty = applicantTypeRole === ApplicantTypeRole.ThirdParty;

  const { formData, dispatch, trySubmit, errors, isValid, counters } = useForm<BaseFormData>(
    {
      fullName: '',
      email: '',
      phone: '',
      userAgreement: false,
      applicantTypeId: getDefaultApplicantTypeId(applicantTypes, applicantTypeRole),
    },
    {
      fullName: fullNameField('fullName', true, !isThirdParty),
      email: emailField(!adminFacing || applicantHasEmail),
      phone: phoneField(!adminFacing),
      applicantTypeId: requiredField(undefined, defaultRequiredErrorMessage('Account Type')),
      userAgreement: checkboxField(
        'userAgreement',
        'Please agree to our Terms of Service and Privacy Policy',
      ),
    },
    ValidationMode.RequiredOnSubmit,
  );
  const { doMutation, error, loading, displayLoginLink } = useHook(formData);

  const thirdParties = useMemo(() => {
    return getSortedApplicantTypes(applicantTypes, ApplicantTypeRole.ThirdParty);
  }, [applicantTypes]);

  const handleSubmission = (): void =>
    trySubmit((): void =>
      doMutation({
        partnerId: partner.id,
        name: sanitizeFullName(formData.fullName),
        email: formData?.email.toLocaleLowerCase() || null,
        phone: (formData?.phone ? formatPhone(formData.phone, 'E164') : null) as string,
        applicantTypeId: formData.applicantTypeId,
      }),
    );

  useEffect(() => {
    if (error)
      showSnackbar(error, (key: SnackbarKey) => (
        <>
          <Button variant="ghost" onClick={(): void => removeSnackbar(key)}>
            Dismiss
          </Button>
        </>
      ));
  }, [error, removeSnackbar, displayLoginLink, adminFacing, showSnackbar]);

  return (
    <FormContainer
      title="Create Account for User"
      onSubmit={handleSubmission}
      buttons={
        <Flex direction="column" align="center" width="100%" gap="4">
          <Button type="submit" loading={loading} style={{ width: '100%' }}>
            Get Started
          </Button>
        </Flex>
      }
    >
      <Typography variant="body">* An asterisk indicates a required response</Typography>
      <TextInput
        id="create-account-fullName"
        label="Full Name"
        value={formData.fullName}
        onChange={(value): void => dispatch(fieldUpdate('fullName', value))}
        error={errors.fullName as string}
        required
      />
      {isThirdParty && thirdParties?.length && (
        <Dropdown
          id="create-account-applicantTypeId"
          label="Account Type"
          items={thirdParties.map((applicantType) => ({
            label: applicantType.name,
            value: applicantType.id,
          }))}
          value={formData.applicantTypeId}
          onChange={(value: string): void => dispatch(fieldUpdate('applicantTypeId', value))}
          error={!!errors.applicantTypeId}
          helperText={errors.applicantTypeId as string}
          required
          searchable={false}
          disabled={thirdParties.length === 1}
        />
      )}
      <TextInput
        id="create-account-email"
        label="Email Address"
        value={formData.email}
        onChange={(value): void => dispatch(fieldUpdate('email', value))}
        error={errors.email as string}
        required={!adminFacing || applicantHasEmail}
        disabled={adminFacing && !applicantHasEmail}
      />
      <TextInput
        id="create-account-phone"
        label="Phone Number"
        value={formData.phone}
        onChange={(value): void => {
          const formatted = isPhoneNumber(value) ? formatPhone(value) : value;
          dispatch(fieldUpdate('phone', formatted));
        }}
        error={errors.phone as string}
        required={!adminFacing}
      />
      {adminFacing && (
        <Checkbox
          checked={!applicantHasEmail}
          onClick={(): void => {
            dispatch(fieldUpdate('email', ''));
            setApplicantHasEmail(!applicantHasEmail);
          }}
          label={
            <Typography variant="body" tag="span">
              This applicant <strong>does not</strong> have an email address
            </Typography>
          }
        />
      )}
      <Checkbox
        required
        checked={formData.userAgreement}
        onClick={(): void => dispatch(fieldUpdate('userAgreement', !formData.userAgreement))}
        label={
          <Typography variant="body" tag="span">
            By checking this box you agree to our{' '}
            <Link
              external
              to="https://www.bybeam.co/terms-of-service"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link
              external
              to="https://www.bybeam.co/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy.
            </Link>
          </Typography>
        }
        error={errors.userAgreement as string}
      />
      <ErrorDisplay
        errors={counters?.missingRequiredFields}
        visible={!isValid && counters?.missingRequiredFields > 0}
      />
    </FormContainer>
  );
}

import useForm, { ValidationMode, fieldUpdate, requiredField } from '@/app/hooks/useForm';
import { ApplicantTypeRole } from '@bybeam/platform-types';
import usePartner from '@platform-ui-common/hooks/contexts/usePartner';
import { Button, Flex } from '@radix-ui/themes';
import { useMemo } from 'react';
import FormContainer from '../FormContainer';
import RadioGroup from '../RadioGroup';
import Typography from '../Typography';

export const DEFAULT_ROLE_LABELS: Record<ApplicantTypeRole, string> = {
  [ApplicantTypeRole.FirstParty]: 'I’m a person who wants to apply to receive benefits',
  [ApplicantTypeRole.ThirdParty]:
    'I’m someone who needs to provide supplemental information on others’ applications',
};

export const DEFAULT_ADMIN_ROLE_LABELS: Record<ApplicantTypeRole, string> = {
  [ApplicantTypeRole.FirstParty]: 'A person who wants to apply to receive benefits',
  [ApplicantTypeRole.ThirdParty]:
    'Someone who needs to provide supplemental information on others’ applications',
};

export default function AccountPartySelection({
  onSubmit,
  defaultRole,
  adminFacing,
}: {
  adminFacing?: boolean;
  onSubmit: (role: ApplicantTypeRole) => void;
  defaultRole?: ApplicantTypeRole;
}) {
  const { formData, dispatch, errors, trySubmit } = useForm<{
    applicantTypeRole: ApplicantTypeRole;
  }>(
    {
      applicantTypeRole: defaultRole,
    },
    {
      applicantTypeRole: requiredField(),
    },
    ValidationMode.RequiredOnSubmit,
  );
  const partner = usePartner();

  const roleOptions = useMemo(() => {
    const config = partner?.config?.applicantTypeRoles ?? {};
    return Object.values(ApplicantTypeRole).map((value) => ({
      label: adminFacing
        ? DEFAULT_ADMIN_ROLE_LABELS[value]
        : (config[value]?.description ?? DEFAULT_ROLE_LABELS[value]),
      value: value,
    }));
  }, [adminFacing, partner?.config?.applicantTypeRoles]);

  return (
    <FormContainer
      title="Create account"
      onSubmit={() => trySubmit((): void => onSubmit(formData.applicantTypeRole))}
      buttons={
        <Flex direction="column" align="center" className="w-full" gap="4">
          <Button type="submit">Continue</Button>
        </Flex>
      }
    >
      <Typography variant="body">* An asterisk indicates a required response</Typography>
      <Typography variant="largeBody">
        {adminFacing
          ? 'Which statement best describes this applicant?*'
          : 'Which statement best describes you?*'}
      </Typography>
      <div className="flex flex-col items-center w-field gap-y-4 lg:self-center lg:max-w-[20rem]">
        <RadioGroup
          name="applicantTypeRole"
          selectedValue={formData.applicantTypeRole}
          onChange={(value: ApplicantTypeRole): void =>
            dispatch(fieldUpdate('applicantTypeRole', value))
          }
          items={roleOptions}
          error={!!errors.applicantTypeRole}
          helperText={errors.applicantTypeRole as string}
        />
      </div>
    </FormContainer>
  );
}

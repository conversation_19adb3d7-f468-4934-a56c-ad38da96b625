import type { Meta, StoryObj } from '@storybook/nextjs';
import ProgramCreateDialog from './ProgramCreateDialog';
import { GET_PARTNER_FUNDS } from './PaymentCreateDialog.mock';

const meta: Meta<typeof ProgramCreateDialog> = {
  component: ProgramCreateDialog,
};

export default meta;
type Story = StoryObj<typeof ProgramCreateDialog>;

export const Primary: Story = {
  parameters: {
    apolloClient: {
      mocks: [GET_PARTNER_FUNDS]
    },
  },
};

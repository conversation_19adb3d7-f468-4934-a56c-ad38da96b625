#import "../../../../../spa-legacy/graphql/fragments/appconfig/ApplicationConfigurationFragment.graphql"
#import "../../../../../spa-legacy/graphql/fragments/CompleteProgramConfigFragment.graphql"
#import "../../../../../spa-legacy/graphql/fragments/ProgramApplicantTypeFragment.graphql"
#import "../../../../../spa-legacy/graphql/fragments/FeatureSettingFragment.graphql"

mutation createProgram($input: CreateProgramInput!, $externalId: NonEmptyString!) {
  program {
    create(input: $input) {
      metadata {
        status
        message
      }
      record {
        id
        name
        heroImage
        status

        applicantTypes {
          ...ProgramApplicantTypeFragment
        }

        applicationConfigurations {
          programId
          applicantTypeId
          configuration {
            ...ApplicationConfigurationFragment
          }
        }
        config {
          ...CompleteProgramConfigFragment
        }
        features {
          ...FeatureSettingFragment
        }
        funds {
          id
          name
        }
      }
      query {
        partners(filter: { externalId: $externalId }) {
          partners {
            id
            programs {
              id
            }
          }
        }
      }
    }
  }
}

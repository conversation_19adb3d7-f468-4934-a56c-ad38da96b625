import { ProgramDocumentStatus, ProgramStatus } from '@bybeam/platform-types';

export const ProgramStatusDisplay: { [status in ProgramStatus]: string } = {
  [ProgramStatus.Open]: 'Open',
  [ProgramStatus.ReferralOnly]: 'Referral-Only',
  [ProgramStatus.Closed]: 'Closed',
};

export const isVerificationFileCompleted = (status?: ProgramDocumentStatus) => {
  if (!status) return false;
  return [ProgramDocumentStatus.Completed, ProgramDocumentStatus.Failed].includes(status);
};

import CanAccess from '@/app/components/features/CanAccess';
import Table from '@/spa-legacy/common/components/Table';
import type { Column } from '@/spa-legacy/common/components/Table/types';
import Tooltip from '@/spa-legacy/common/components/Tooltip';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { useMutation, useQuery } from '@apollo/client';
import type { Partner, Tag } from '@bybeam/platform-types';
import DeletePartnerTag from './DeletePartnerTag.graphql';
import PartnerTagManager from './PartnerTagManager.graphql';
import { Button, Flex, Heading } from '@radix-ui/themes';
import { TrashIcon } from '@radix-ui/react-icons';
import { JSX } from 'react';

type DeleteTagColumn = {
  tag: Tag;
  onDeleteTag: (tag: Tag) => void;
};

interface TableRow {
  name: string;
  deleteTag: DeleteTagColumn;
}

const TagTable: React.FC = () => {
  const [deletePartnerTag] = useMutation(DeletePartnerTag);

  const { id: partnerId, externalId } = usePartner();
  const { showSnackbar } = useSnackbar();

  const { data, loading: tagsLoading } = useQuery<
    { partners: { partners: Partner[] } },
    { externalId: string }
  >(PartnerTagManager, {
    variables: { externalId },
  });

  const tags = data?.partners.partners[0].tags;

  const onDeleteTag = async (tag: Tag) => {
    const { data } = await deletePartnerTag({
      variables: {
        input: {
          ids: [tag.id],
        },
      },
    });

    const { status: responseStatus, errors } = data?.tag?.delete?.metadata || {};
    if (responseStatus < 400) return;
    const message = errors?.join(', ') || 'Unknown Issue';
    showSnackbar(`Failed to delete tag (${message}). Please refresh and try again.`);
  };

  const TableColumns = [
    {
      Header: 'Name',
      accessor: 'name',
    } as Column<TableRow>,
    {
      accessor: 'deleteTag',
      Cell: function DeleteTag({
        value: { tag, onDeleteTag },
      }: {
        value: DeleteTagColumn;
      }): JSX.Element {
        return (
          <>
            {tag.count === 0 ? (
              <CanAccess resource={{ objectId: partnerId, objectType: 'ORGANIZATION' }}>
                <Tooltip title="Delete Tag" placement="right">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={(): void => {
                      onDeleteTag(tag);
                    }}
                  >
                    <TrashIcon />
                    Delete
                  </Button>
                </Tooltip>
              </CanAccess>
            ) : (
              <>
                In use ({tag.count} {`case${tag.count > 1 ? 's' : ''}`})
              </>
            )}
          </>
        );
      },
    } as Column<TableRow>,
  ];

  const tableRows = tags?.map((tag) => {
    return {
      name: tag.name,
      deleteTag: {
        tag,
        onDeleteTag,
      },
    } as TableRow;
  });

  return (
    <Flex gap="2" direction="column">
      {tagsLoading ? null : (
        <>
          <Heading as="h2">Tags</Heading>
          <Table columns={TableColumns} data={{ rows: tableRows, total: tableRows.length }} />
        </>
      )}
    </Flex>
  );
};

export default TagTable;

import CanAccess from '@/app/components/features/CanAccess';
import Loading from '@/spa-legacy/common/components/Loading';
import Table from '@/spa-legacy/common/components/Table';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { downloadFile } from '@/spa-legacy/portal/utils/client';
import { checkFeatureAnyProgram } from '@/spa-legacy/utilities/checkFeature';
import { useQuery } from '@apollo/client';
import { FeatureName, type Program, ProgramDocumentStatus } from '@bybeam/platform-types';
import { ServiceType } from '@bybeam/verification-types';
import { useEffect, useState } from 'react';
import GetProgramsQuery from '../../graphql/GetProgramsQuery.graphql';
import { useVerificationFilesStatus } from '../../hooks/useVerificationFilesStatus';
import ProgramListModal from '../ProgramListModal';
import { VerificationActionType } from '../VerificationDataLookup/Actions';
import UploadVerificationFile from '../VerificationDataLookup/UploadVerificationFile';
import { UpsertLookupConfigModal } from '../VerificationDataLookup/UpsertLookupConfig';
import VerificationFilesStatus from '../VerificationDataLookup/VerificationFilesStatus';
import { getColumns, prepData } from './columns';
import { Button, Flex } from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { Pencil1Icon } from '@radix-ui/react-icons';
import ProgramCreateDialog from '@/app/[partnerId]/(authenticated)/settings/components/ProgramCreate/ProgramCreateDialog';

const ProgramListTable = (): JSX.Element => {
  const [isEditProgramsModalOpen, setOpenEditProgramsModal] = useState<boolean>(false);
  const [isUploadVerificationFileOpen, openVerificationFileModal] = useState<boolean>(false);
  const [isLookupFieldModalOpen, setIsLookupFieldModalOpen] = useState<boolean>(false);
  const [selectedProgram, setSelectedProgram] = useState<Program>();

  const partner = usePartner();
  const postHog = usePostHog();

  const { data, loading, refetch } = useQuery(GetProgramsQuery, {
    fetchPolicy: 'cache-first',
  });

  const { verificationFiles, refetchStatus, resetStatus } = useVerificationFilesStatus(refetch);

  const programs = data?.programs?.programs || [];

  const isVerificationFeatureEnabled = checkFeatureAnyProgram(
    programs,
    FeatureName.VerificationDataLookup,
  );

  const handleModalClose = (): void => {
    setOpenEditProgramsModal(false);
  };

  const generateSampleFile = (program: Program): Blob => {
    const config = program?.verificationConfigurations?.find(
      ({ service }) => service === ServiceType.DataLookup,
    );
    const setup = config?.dataLookup?.fields?.reduce<{
      headers: string[];
      row: string[];
    }>(
      (data, column) => {
        data.headers.push(column.key);
        data.row.push(column.sample);
        return data;
      },
      { headers: [], row: [] },
    );
    const csv = [setup.headers.join(','), setup.row.join(',')].join('\n');
    return new Blob([csv], { type: 'text/csv' });
  };

  const onClickVerificationAction = (action: VerificationActionType, program: Program) => {
    switch (action) {
      case VerificationActionType.UPLOAD_FILE:
      case VerificationActionType.REPLACE_FILE:
        openVerificationFileModal(true);
        setSelectedProgram(program);
        break;
      case VerificationActionType.DOWNLOAD_CSV_TEMPLATE:
        downloadFile(generateSampleFile(program), `${program.name}-sample.csv`);
        break;
      case VerificationActionType.UPDATE_FIELDS:
        setIsLookupFieldModalOpen(true);
        setSelectedProgram(program);
        break;
      case VerificationActionType.DOWNLOAD_FILE:
        break;
    }
  };

  const onCloseVerificationFileModal = (doRefetch: boolean) => {
    if (doRefetch) {
      refetchStatus(selectedProgram);
    }
    openVerificationFileModal(false);
    setSelectedProgram(null);
  };

  const onLookupConfigModalClose = (openFileUpload?: boolean) => {
    refetch();
    setIsLookupFieldModalOpen(false);
    openFileUpload && openVerificationFileModal(true);
  };

  const rowError =
    programs?.length &&
    verificationFiles?.length &&
    verificationFiles
      .filter((file) => file.status === ProgramDocumentStatus.Failed)
      .map((file) => programs.findIndex((program: Program) => program.id === file.program.id));

  useEffect(() => resetStatus(programs ?? []), [programs, resetStatus]);

  if (loading) return <Loading size="XXL" />;
  return (
    <>
      <ProgramListModal
        programs={programs}
        isOpen={isEditProgramsModalOpen}
        onClose={handleModalClose}
      />
      {isUploadVerificationFileOpen && selectedProgram && (
        <UploadVerificationFile
          program={selectedProgram}
          isOpen={isUploadVerificationFileOpen}
          onClose={onCloseVerificationFileModal}
        />
      )}
      {isLookupFieldModalOpen && selectedProgram && (
        <UpsertLookupConfigModal
          program={selectedProgram}
          isOpen={isLookupFieldModalOpen}
          onClose={onLookupConfigModalClose}
        />
      )}
      <div className="flex flex-col gap-6">
        <Typography variant="h2">{`Programs (${programs.length})`}</Typography>
        {isVerificationFeatureEnabled && <VerificationFilesStatus files={verificationFiles} />}
        <Table
          columns={getColumns({ programs, onClick: onClickVerificationAction })}
          data={{ errorRows: rowError, rows: prepData(programs), total: programs.length }}
        />
        <Flex gap="2">
          <CanAccess resource={{ objectId: partner?.id, objectType: 'ORGANIZATION' }}>
            <Button onClick={() => setOpenEditProgramsModal(true)}>
              <Pencil1Icon /> Update Program(s) Status
            </Button>
          </CanAccess>
          {postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.programCreation) && (
            <CanAccess resource={{ objectId: partner?.id, objectType: 'ORGANIZATION' }}>
              <ProgramCreateDialog
                onSuccess={() => {
                  refetch();
                }}
              />
            </CanAccess>
          )}
        </Flex>
      </div>
    </>
  );
};

export default ProgramListTable;

import DragzoneFileUpload from '@/spa-legacy/common/components/DragzoneFileUpload';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import Typography from '@/spa-legacy/common/components/Typography';
import UploadPreview from '@/spa-legacy/common/components/Upload/Preview';
import type { Program } from '@bybeam/platform-types';
import { Button, Flex } from '@radix-ui/themes';
import { useState } from 'react';
import useUploadVerificationFile from '../../hooks/useUploadVerificationFile';

const MAX_SIZE = '10 MB';

export default function UploadVerificationFile({
  program,
  isOpen,
  onClose,
}: { program: Program; isOpen: boolean; onClose: (refetch?: boolean) => void }) {
  const [file, setFile] = useState<File>();
  const { uploadVerificationFile, errors, loading, reset } = useUploadVerificationFile(program);

  const onUploadVerificationFile = async () => {
    const success = await uploadVerificationFile(file);
    if (success) {
      setFile(null);
      onClose(true);
    }
  };

  const onUpload = (acceptedFiles: File[]) => {
    if (acceptedFiles?.length) {
      setFile(acceptedFiles[0]);
    }
  };

  return (
    <Modal size="S" isOpen={isOpen} onClickClose={onClose} title="Upload Verification File">
      <div className="flex flex-col gap-6 w-full">
        <Typography variant="largeBody" bold>
          {program?.name ?? ''}
        </Typography>
        <Typography variant="body">Upload 1 file up to {MAX_SIZE} in CSV file format</Typography>
        <DragzoneFileUpload
          accept={{
            'text/csv': ['.csv'],
          }}
          maxFiles={1}
          onUpload={onUpload}
          reachedMaxFiles={!!file}
          maxSize={10000000}
        />

        {!!file && (
          <UploadPreview
            mimetype={file.type}
            name={file.name}
            url={URL.createObjectURL(file)}
            removeSelectedFile={() => {
              setFile(null);
              reset();
            }}
            editable
            {...(!!errors?.length && { variant: 'error' })}
          />
        )}
        {!!program?.documents?.[0] && (
          <Message variant="warning">
            A verification file already exists for this program. Uploading a new file will override
            the existing file. Submitted applications will not be affected.
          </Message>
        )}

        {!!errors?.length &&
          errors.map((error, idx) => (
            <Message variant="error" key={error}>
              {error}
            </Message>
          ))}

        <Flex gap="4" justify="end" mt="4">
          <Button type="button" onClick={() => onClose()} variant="soft" disabled={loading}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={onUploadVerificationFile}
            disabled={!file || loading || !!errors?.length}
            loading={loading}
          >
            Upload
          </Button>
        </Flex>
      </div>
    </Modal>
  );
}

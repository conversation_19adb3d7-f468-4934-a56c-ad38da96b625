import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import Icon from '@/spa-legacy/common/components/Icon';
import MenuButton from '@/spa-legacy/common/components/MenuButton';
import { ProgramDocumentStatus } from '@bybeam/platform-types';
import { Link } from '@mui/material';
import { usePostHog } from 'posthog-js/react';

export enum VerificationActionType {
  UPLOAD_FILE = 'upload File',
  DOWNLOAD_CSV_TEMPLATE = 'download CSV template',
  REPLACE_FILE = 'replace Verification File',
  DOWNLOAD_FILE = 'download Verification File',
  UPDATE_FIELDS = 'update lookup fields',
}

export default function VerificationActions({ program, onClick }) {
  const posthog = usePostHog();
  const isConfigEditEnabled = posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.verificationConfig);
  const hasVerificationConfig = !!program.verificationConfigurations?.length;
  const hasDocuments = program?.documents?.length;
  const options = [
    {
      key: VerificationActionType.UPLOAD_FILE,
      value: VerificationActionType.UPLOAD_FILE,
      label: 'Upload Verification File',
      icon: <Icon type="upload" size="MD" />,
      hidden: hasDocuments || !hasVerificationConfig,
    },
    {
      key: VerificationActionType.DOWNLOAD_CSV_TEMPLATE,
      value: VerificationActionType.DOWNLOAD_CSV_TEMPLATE,
      label: 'Download CSV Template',
      icon: <Icon type="save" size="MD" />,
      hidden: hasDocuments,
    },
    {
      key: VerificationActionType.REPLACE_FILE,
      value: VerificationActionType.REPLACE_FILE,
      label: 'Replace Verification File',
      icon: <Icon type="upload" size="MD" />,
      disabled: program?.documents?.[0]?.status === ProgramDocumentStatus.InProgress,
      hidden: !hasDocuments,
    },
    {
      key: VerificationActionType.DOWNLOAD_FILE,
      value: VerificationActionType.DOWNLOAD_FILE,
      label: 'Download Verification File',
      icon: <Icon type="save" size="MD" />,
      disabled: !program?.documents?.[0]?.document?.previewUrl,
      component: Link,
      href: program?.documents?.[0]?.document?.previewUrl,
      download: program?.documents?.[0]?.document?.filename,
      target: '_blank',
      rel: 'noreferrer',
      hidden: !hasDocuments,
    },
  ];

  if (isConfigEditEnabled)
    options.push({
      key: VerificationActionType.UPDATE_FIELDS,
      value: VerificationActionType.UPDATE_FIELDS,
      label: `${hasVerificationConfig ? 'Edit' : 'Create'} Lookup Configuration`,
      icon: <Icon type="edit" size="MD" />,
      disabled: program?.documents?.[0]?.status === ProgramDocumentStatus.InProgress,
      hidden: false,
    });

  return (
    <MenuButton
      id="verification_actions"
      type="button"
      variant="ghost"
      onClick={onClick}
      options={options}
      data-cy="verification-action"
    >
      <Icon type="moreDots" size="LG" />
    </MenuButton>
  );
}

import ErrorDisplay from '@/spa-legacy/common/components/ErrorDisplay';
import FormContainer from '@/spa-legacy/common/components/FormContainer';
import Modal from '@/spa-legacy/common/components/Modal';
import Typography from '@/spa-legacy/common/components/Typography';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { getAllQuestionKeys } from '@/spa-legacy/utilities/application/config';
import {
  getApplicationConfiguration,
  getFirstPartyApplicationConfig,
} from '@/spa-legacy/utilities/application/configs';
import { useMutation } from '@apollo/client';
import { ApplicantType, MutationResponse, Program } from '@bybeam/platform-types';
import {
  LookupConfigField,
  USER_FIELDS,
  UpsertLookupConfigInput,
} from '@bybeam/verification-types';
import { ErrorMessage } from '@hookform/error-message';
import {
  Alert,
  Autocomplete,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { PlusIcon, TrashIcon } from '@radix-ui/react-icons';
import { Button, Flex, IconButton, Tooltip } from '@radix-ui/themes';
import { useEffect, useMemo } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import UpsertLookupConfig from '../../graphql/UpsertLookupConfigMutation.graphql';

export const UpsertLookupConfigModal = ({
  program,
  isOpen,
  onClose,
}: { program: Program; isOpen: boolean; onClose: (openFileUpload?: boolean) => void }) => {
  const [upsertLookupConfig, upsertLookupConfigMetadata] = useMutation<
    { program: { upsertLookupConfig: MutationResponse } },
    { input: UpsertLookupConfigInput }
  >(UpsertLookupConfig);
  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      applicantTypeId: '',
      fields: [{ key: '', sample: '', details: '', metadata: false }],
    },
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'fields',
  });
  const fieldWatch = watch('fields');
  const savedFields = program.verificationConfigurations?.[0]?.dataLookup?.fields;
  // TODO: this does not work properly for multiparty right now - we need to add a field
  // to the form to capture applicant type.
  const applicantTypeId = program.applicantTypes?.[0]?.applicantType?.id;
  const hasVerificationConfig = !!program.verificationConfigurations?.length;
  const isMultiParty = (program.applicantTypes?.length || 0) > 1;

  const applicationQuestionKeys = useMemo(() => {
    const config =
      isMultiParty && applicantTypeId
        ? getApplicationConfiguration(program, { id: applicantTypeId } as ApplicantType)
        : getFirstPartyApplicationConfig(program);
    return [...USER_FIELDS, ...getAllQuestionKeys(config)];
  }, [applicantTypeId, isMultiParty, program]);

  useEffect(() => {
    applicantTypeId && setValue('applicantTypeId', applicantTypeId);
    if (savedFields) {
      setValue(
        'fields',
        savedFields.map((saved) => ({
          ...saved,
          metadata: saved?.metadata === undefined ? false : saved.metadata,
        })),
      );
    }
  }, [setValue, applicantTypeId, savedFields]);

  const { showSnackbar } = useSnackbar();
  const onSubmit = async (data: {
    applicantTypeId: string;
    fields: LookupConfigField[];
  }) => {
    try {
      const values = data.fields.map((field) => ({
        key: field.key,
        sample: field.sample,
        details: field.details,
        metadata: !!field.metadata,
      }));
      const result = await upsertLookupConfig({
        variables: {
          input: {
            fields: values,
            applicantTypeId: data.applicantTypeId,
            id: program.id,
          },
        },
      });
      if (result?.data?.program?.upsertLookupConfig?.metadata?.status !== 200) {
        throw new Error('Request failed');
      }

      showSnackbar(
        `Successfully ${hasVerificationConfig ? 'updated' : 'created'} the lookup config for ${program.name}`,
      );
      onClose(true);
    } catch (e) {
      console.error(e);
      showSnackbar('There was an error saving the lookup config');
    }
  };

  return (
    <Modal
      size="M"
      isOpen={isOpen}
      onClickClose={() => onClose()}
      title={`${hasVerificationConfig ? 'Edit' : 'Create'} Lookup Configuration`}
    >
      <div className="flex flex-col w-full">
        <Typography variant="largeBody" bold>
          {program?.name ?? ''}
        </Typography>

        <FormContainer
          onSubmit={handleSubmit(onSubmit)}
          buttons={
            <Flex gap="4" justify="end" mt="4">
              <Button type="button" onClick={() => onClose()} variant="soft">
                Cancel
              </Button>
              <Button type="submit" loading={upsertLookupConfigMetadata.loading}>
                Submit
              </Button>
            </Flex>
          }
        >
          <div className="w-full">
            {isMultiParty && (
              <FormControl fullWidth>
                <InputLabel id="applicant-type-label">Applicant Type</InputLabel>
                <Select
                  {...register('applicantTypeId', { required: true })}
                  label="Applicant Type"
                  labelId="applicant-type-label"
                  defaultValue={getValues().applicantTypeId}
                  error={!!errors.applicantTypeId}
                >
                  {program.applicantTypes?.map(({ applicantType }) => (
                    <MenuItem value={applicantType.id} key={applicantType.id}>
                      {applicantType.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </div>
          <>
            <Alert severity="warning">
              If you add to or edit these fields you will need to upload a new verification file
            </Alert>

            <Flex justify="between" align="center" className="w-full mb-2">
              <Typography variant="body" bold>
                {program.documents?.[0]?.document?.filename || ''} fields:
              </Typography>
              <Tooltip content="add new row">
                <IconButton
                  type="button"
                  onClick={() => {
                    append({ key: '', details: '', sample: '', metadata: false });
                  }}
                  variant="soft"
                  radius="full"
                  aria-label="Add new row"
                >
                  <PlusIcon />
                </IconButton>
              </Tooltip>
            </Flex>
            <ul className="space-y-6">
              {fields.map((item, index) => {
                return (
                  <li key={item.id}>
                    <Flex justify="between" align="start">
                      <div>
                        <div className="flex items-center gap-4">
                          {fieldWatch?.[index]?.metadata ? (
                            <TextField
                              variant="filled"
                              label="Key"
                              {...register(`fields.${index}.key`, { required: true })}
                              helperText={errors.fields?.[index]?.key && 'Key is required'}
                              error={!!errors.fields?.[index]?.key}
                            />
                          ) : (
                            <Controller
                              name={`fields.${index}.key`}
                              control={control}
                              render={(props) => (
                                <Autocomplete
                                  sx={{ minWidth: '203px', width: '100%' }}
                                  {...props}
                                  {...register(`fields.${index}.key`, { required: true })}
                                  defaultValue={fieldWatch?.[index]?.key}
                                  onChange={(_, data) => props.field.onChange(data)}
                                  options={applicationQuestionKeys}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      variant="filled"
                                      label="Key"
                                      helperText={errors.fields?.[index]?.key && 'Key is required'}
                                      error={!!errors.fields?.[index]?.key}
                                    />
                                  )}
                                />
                              )}
                            />
                          )}
                          <TextField
                            variant="filled"
                            {...register(`fields.${index}.details`, { required: true })}
                            label="Details"
                            helperText={errors.fields?.[index]?.details && 'Details is required'}
                            error={!!errors.fields?.[index]?.details}
                          />
                          <TextField
                            variant="filled"
                            {...register(`fields.${index}.sample`, { required: true })}
                            label="Sample"
                            helperText={errors.fields?.[index]?.sample && 'Sample is required'}
                            error={!!errors.fields?.[index]?.sample}
                          />
                          <Tooltip content="delete row">
                            <IconButton
                              type="button"
                              onClick={() => remove(index)}
                              variant="ghost"
                              aria-label="Delete row"
                            >
                              <TrashIcon height="16px" width="16px" />
                            </IconButton>
                          </Tooltip>
                        </div>
                        <FormControlLabel
                          className="!w-auto"
                          control={
                            <Checkbox
                              defaultChecked={fields[index].metadata}
                              {...register(`fields.${index}.metadata`, { required: false })}
                            />
                          }
                          label="Reference only"
                        />
                      </div>
                    </Flex>
                  </li>
                );
              })}
            </ul>
            <ErrorMessage
              errors={errors}
              name="fields"
              render={() => {
                const errorFields = errors?.fields ?? [];
                const totalErrors = (errorFields as Record<string, unknown>[]).reduce(
                  (acc, curr) => {
                    for (const value in curr ?? {}) {
                      value && acc.push(true);
                    }
                    return acc;
                  },
                  [] as boolean[],
                ).length;
                return <ErrorDisplay errors={totalErrors} visible />;
              }}
            />
          </>
        </FormContainer>
      </div>
    </Modal>
  );
};

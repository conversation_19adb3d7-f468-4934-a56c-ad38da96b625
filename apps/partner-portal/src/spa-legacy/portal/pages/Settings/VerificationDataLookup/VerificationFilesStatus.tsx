import EmailLink from '@/spa-legacy/common/components/Link/EmailLink';
import Message from '@/spa-legacy/common/components/Message';
import { BEAM_SUPPORT_EMAIL } from '@/spa-legacy/utilities/constants';
import { type Program, ProgramDocumentStatus } from '@bybeam/platform-types';
import { isVerificationFileCompleted } from '../utils';

export type VerificationFileStatus = {
  status?: ProgramDocumentStatus;
  program: Program;
};

export default function VerificationFilesStatus({
  files,
}: { files: VerificationFileStatus[] }): JSX.Element {
  if (!files?.length) return null;

  return (
    <>
      {files
        .filter(({ status }) => isVerificationFileCompleted(status))
        .map(({ program, status }) => (
          <div key={`msg-${program.id}`}>
            {status === ProgramDocumentStatus.Completed && (
              <Message variant="success">
                The verification file for {program.name} has uploaded successfully with no errors
                found.
              </Message>
            )}
            {status === ProgramDocumentStatus.Failed && (
              <Message variant="error">
                There is an error with the verification file for {program.name}. Please replace the
                verification file or contact us at <EmailLink>{BEAM_SUPPORT_EMAIL}</EmailLink> to
                resolve this issue.
              </Message>
            )}
          </div>
        ))}
    </>
  );
}

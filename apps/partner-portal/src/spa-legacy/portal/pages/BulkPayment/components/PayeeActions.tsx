import Icon from '@/spa-legacy/common/components/Icon';
import MenuButton from '@/spa-legacy/common/components/MenuButton';
import type { Case } from '@bybeam/platform-types';
import { PayeeActionType } from '../utils/types';

export function PayeeActionsButton({
  case: { fulfillments },
  onClick,
}: {
  case: Case;
  onClick: (value: PayeeActionType) => void;
}): JSX.Element {
  const options = [
    // TODO: Needs to be disabled until the global field is saved?
    // Or at least unless the case has a payment saved yet?
    // Otherwise, can't save a payment without a fund which is not an
    // option in this modal
    {
      label: 'Edit Approved Amount',
      value: PayeeActionType.EDIT_APPROVED_AMOUNT,
      key: PayeeActionType.EDIT_APPROVED_AMOUNT,
      icon: <Icon type="edit" size="MD" />,
      disabled: !fulfillments.length,
      // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
      ['data-cy']: 'edit-approved-amount-button',
    },
    // TODO: Does it really make sense to include this action for Claim Funds?
    {
      label: 'Add/Edit Payee Details',
      value: PayeeActionType.EDIT_PAYEE,
      key: PayeeActionType.EDIT_PAYEE,
      icon: <Icon type="add" size="MD" />,
    },
    {
      label: 'Remove Payee',
      value: PayeeActionType.REMOVE_PAYEE,
      key: PayeeActionType.REMOVE_PAYEE,
      icon: <Icon type="trash" size="MD" />,
      // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
      ['data-cy']: 'remove-payee-button',
    },
  ];
  return (
    <MenuButton
      id="bulk_actions"
      type="button"
      variant="text"
      onClick={onClick}
      options={options}
      data-cy="payee-action"
    >
      <Icon type="moreDots" size="LG" />
    </MenuButton>
  );
}

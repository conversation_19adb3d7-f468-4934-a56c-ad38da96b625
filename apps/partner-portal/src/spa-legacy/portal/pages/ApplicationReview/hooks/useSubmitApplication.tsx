import { isSectionComplete } from '@/spa-legacy/application/hooks/useApplicationProgress';
import SubmitApplicationMutation from '@/spa-legacy/common/graphql/mutations/SubmitApplication.graphql';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { useMutation } from '@apollo/client';
import type { Application, ApplicationConfig, MutationResponse } from '@bybeam/platform-types';
import { useCallback } from 'react';
import { Button } from '@radix-ui/themes';

interface UseSubmitApplicationResponse {
  submitApplication: (application: Application) => Promise<boolean>;
  loading: boolean;
}

export const canSubmitApplication = (
  answers,
  { sections }: ApplicationConfig,
  application,
): boolean => {
  const incomplete = sections.filter(
    (section) => !isSectionComplete(section, answers, application),
  );
  return !incomplete.length && !application.submittedAt;
};

export default function useSubmitApplication(): UseSubmitApplicationResponse {
  const [doMutation, { loading }] = useMutation<{
    application: { submit: MutationResponse<Application> };
  }>(SubmitApplicationMutation);

  const { showSnackbar } = useSnackbar();

  const submitApplication = useCallback(
    async (application) => {
      const {
        data: {
          application: {
            submit: { metadata },
          },
        },
      } = await doMutation({
        variables: { input: { id: application.id } },
      });

      if (metadata.status < 400) {
        return true;
      }

      showSnackbar(
        'There was an error submitting the application',
        <Button
          variant="ghost"
          onClick={(): Promise<boolean | Application> => submitApplication(application)}
        >
          Try Again
        </Button>,
      );

      return false;
    },
    [doMutation, showSnackbar],
  );

  return { submitApplication, loading };
}

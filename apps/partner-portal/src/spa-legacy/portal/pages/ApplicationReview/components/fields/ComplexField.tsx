import {
  type GenericFormData,
  type RepeatedValidationFieldConfig,
  type ValidationErrors,
  addRepeated,
  removeRepeated,
} from '@/app/hooks/useForm';
import Typography from '@/spa-legacy/common/components/Typography';
import { Button } from '@radix-ui/themes';
import {
  extractFieldValidation,
  getDefaultValue,
  getFieldLabel,
  isFieldEnabled,
} from '@/spa-legacy/utilities/application/fields';
import {
  type ComplexField as ComplexFieldDefinition,
  FieldType,
  type SubField,
} from '@bybeam/platform-types';
import cx from 'classnames';
import { useCallback } from 'react';
import Field from './Field';
import type { FieldProps } from './types';

function appendItemIndex(field: SubField, index: number, fieldIndex): SubField {
  const fieldName = getFieldLabel(field);
  if (!fieldName || fieldIndex !== 0) return field;

  const label = `${fieldName} ${Number(index) + 1}`;

  switch (field.type) {
    case FieldType.Document:
      return { ...field, copy: { ...field.copy, title: label } };
    case FieldType.Calculated:
      return { ...field, display: { copy: label } };
    default:
      return { ...field, copy: label };
  }
}

export default function ComplexField<FormData extends GenericFormData>({
  field: { key, copy, subFields },
  validation,
  answers,
  readonly,
  dispatch,
  errors,
  ...props
}: FieldProps<ComplexFieldDefinition, FormData>): JSX.Element {
  const removeEntry = useCallback(
    (index: number): void => dispatch(removeRepeated(key, index)),
    [dispatch, key],
  );
  const addEntry = useCallback(
    (): void =>
      dispatch(
        addRepeated(
          key,
          Object.fromEntries(
            subFields.map((subField) => [subField.key, getDefaultValue(subField)]),
          ),
        ),
      ),
    [dispatch, key, subFields],
  );

  const isShortForm = subFields.length <= 4;

  return (
    <div className={cx(['self-start flex flex-col gap-y-4 w-full'])}>
      {isShortForm && (
        <Typography variant="label" bold>
          {copy}
        </Typography>
      )}

      {answers[key].map((answer: FormData, index: number) => (
        <div
          key={`field.key-${index + 1}`}
          className={cx([
            'gap-y-4 flex flex-col w-full flex-wrap',
            { 'gap-y-0 gap-x-2 md:grid md:grid-cols-9': isShortForm },
            { 'md:grid-cols-8': readonly },
          ])}
        >
          <div
            className={cx([
              'flex flex-row items-center gap-x-5',
              { 'md:order-2 md:col-span-1': isShortForm },
            ])}
          >
            {!isShortForm && <Typography variant="label">{`${copy} ${index + 1}`}</Typography>}
            {!readonly && (
              <Button variant="ghost" onClick={(): void => removeEntry(index)}>
                Remove
              </Button>
            )}
          </div>
          <div
            className={cx([
              'flex gap-4 md:place-content-start flex-wrap ',
              { 'grid grid-cols-1 md:grid-cols-3': !isShortForm },
              { 'md:col-span-8': isShortForm },
            ])}
            aria-live="polite"
          >
            {subFields
              .filter((field) => isFieldEnabled(field, answer))
              .map(
                (subField, subIdx): JSX.Element => (
                  <Field
                    {...props}
                    key={subField.key}
                    field={appendItemIndex(subField, index, subIdx)}
                    validation={extractFieldValidation(
                      subField,
                      (validation as RepeatedValidationFieldConfig<FormData>)?.schema,
                    )}
                    repeated={{
                      parentKey: key,
                      index,
                    }}
                    answers={answer}
                    errors={(errors[key][index] ?? {}) as ValidationErrors<FormData[typeof key]>}
                    dispatch={dispatch}
                    readonly={readonly}
                  />
                ),
              )}
          </div>
        </div>
      ))}
      {!readonly && (
        <div className="py-4">
          <Button variant="ghost" onClick={addEntry}>
            + Add {copy}
          </Button>
        </div>
      )}
    </div>
  );
}

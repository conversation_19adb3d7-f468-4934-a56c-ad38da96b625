import FieldDetailList from '@/spa-legacy/common/components/FieldDetail/FieldDetailList';
import Loading from '@/spa-legacy/common/components/Loading';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import Typography from '@/spa-legacy/common/components/Typography';
import UploadPreview from '@/spa-legacy/common/components/Upload/Preview';
import { Button, Flex } from '@radix-ui/themes';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import {
  DateFormat,
  formatAddress,
  formatCurrency,
  formatDate,
  formatIfPhone,
  mask,
} from '@/spa-legacy/common/utilities/format';
import {
  AccountTypesDisplay,
  PayeeTypeDisplay,
  PaymentMethodDisplay,
  PaymentScheduleDisplay,
} from '@/spa-legacy/common/utilities/payment';
import { isPrepaidCard } from '@/spa-legacy/common/utilities/paymentMethods';
import { checkFeatures } from '@bybeam/platform-lib/features/check';
import {
  Address,
  ApplicantTypeRole,
  Application,
  type Case,
  FeatureName,
  type Fulfillment,
  MailingAddressType,
  type Payee,
  PayeeType,
  PaymentMethod,
  ScheduleType,
  type Vendor,
} from '@bybeam/platform-types';
import { useEffect, useMemo, useState } from 'react';
import useSubmitClaim from '../../hooks/usePartnerIssuedClaim';
import { ModalDetail } from '../../utils/types';
import ApprovePaymentField from '../ApprovePaymentField';
import {
  getMissingPaymentFieldsMessage,
  hasRequiredPaymentFields,
  isPaymentDateValid,
} from '../utils';
import usePayee from './usePayee';

interface PartnerIssuesContentProps {
  fulfillment: Fulfillment;
  case: Case;
  modalRedirect: (modal: ModalDetail) => void;
  onClose: () => void;
  isOpen: boolean;
}

function ModalContent({
  case: case_,
  fulfillment,
  onClose,
  modalRedirect,
  payee,
  submitClaim: { submitClaim, error, loading },
}: PartnerIssuesContentProps & {
  payee: Payee;
  submitClaim: ReturnType<typeof useSubmitClaim>;
}): JSX.Element {
  const today = new Date();
  const { approvedAmount, payments, fund, paymentPattern, schedule } = fulfillment;
  const { method, note, mailingAddress, mailingAddressType } = payments[0];
  const firstPartySubmitter = case_.applications?.find(
    (app: Application) =>
      app.submitter?.applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty,
  )?.submitter;

  const { email, taxId, bankAccount, phone } = payee || {};
  const pinnedDocuments = useMemo(() => {
    const documents =
      payee.payeeType === PayeeType.Vendor ? (payee as Vendor)?.documents : case_.documents;
    return documents?.filter((document) => document.pinned);
  }, [case_, payee]);

  const [hasSubmitted, setHasSubmitted] = useState(false);
  const onSubmit = async (): Promise<void> => {
    setHasSubmitted(true);
    if (!hasRequiredPaymentFields(method, payee)) return;

    const success = await submitClaim();
    if (success) modalRedirect(ModalDetail.Initiated);
  };

  useEffect(() => {
    if (error && !error?.retryable) modalRedirect(ModalDetail.Error);
  }, [error, modalRedirect]);

  const program = useProgram();
  const isRecurring = fulfillment.scheduleType === ScheduleType.Recurring;

  const renderMailingAddress = () => {
    if (program?.config?.mailingAddressOverride) {
      return (
        <>
          {formatAddress(program.config.mailingAddressOverride)}
          <Message variant="warning">
            <strong>NOTE: This payment is being sent to a program-wide address</strong>
          </Message>
        </>
      );
    }
    if (!mailingAddress) return null;
    return (
      <>
        {formatAddress(mailingAddress as Partial<Address>)}
        {mailingAddressType === MailingAddressType.Applicant && (
          <Message variant="warning">
            <strong>
              NOTE: This payment will be sent directly to applicant,{' '}
              {firstPartySubmitter?.name ?? 'N/A'}.
            </strong>
          </Message>
        )}
      </>
    );
  };

  return (
    <div className="flex flex-col gap-8">
      <Typography variant="h3">{fulfillment?.payments[0]?.payee.name ?? ''}</Typography>
      <div className="flex flex-col w-full gap-8 border-l border-l-textSecondary pl-4 py-2">
        <FieldDetailList columns={2} condensed>
          {case_.applications.length === 1 ? (
            <ApprovePaymentField label="Application ID">
              {case_.applications[0].displayId}
            </ApprovePaymentField>
          ) : (
            <ApprovePaymentField label="Case ID">{case_.displayId}</ApprovePaymentField>
          )}
          <ApprovePaymentField label="Fund Name">{fund?.name}</ApprovePaymentField>
          <ApprovePaymentField label="TaxId/SSN">{mask(taxId)}</ApprovePaymentField>
          <ApprovePaymentField label="Phone Number">
            {phone ? formatIfPhone(phone) : ''}
          </ApprovePaymentField>
          <ApprovePaymentField label="Email Address" stretch={2} required={isPrepaidCard(method)}>
            {email}
          </ApprovePaymentField>
          <ApprovePaymentField
            label="Mailing Address"
            stretch={2}
            required={isPrepaidCard(method) || method === PaymentMethod.Check}
          >
            {renderMailingAddress()}
          </ApprovePaymentField>
          {checkFeatures(program?.features, [
            FeatureName.PaymentsVendors,
            FeatureName.PaymentsApplicants,
          ]) && (
            <ApprovePaymentField label="Payee Type">
              {PayeeTypeDisplay[payee.payeeType]}
            </ApprovePaymentField>
          )}
          <ApprovePaymentField label="Payment Method">
            {PaymentMethodDisplay[method]}
            {isRecurring && `-${PaymentScheduleDisplay[paymentPattern.pattern]}`}
          </ApprovePaymentField>
          {isRecurring && (
            <ApprovePaymentField label="Number of Payments">
              {paymentPattern?.count ?? 0} payments
            </ApprovePaymentField>
          )}
          <ApprovePaymentField label="Approved Amount">
            {formatCurrency(approvedAmount, true)}
          </ApprovePaymentField>
          {isRecurring && (
            <ApprovePaymentField label="Per Payment Amount">
              {formatCurrency(paymentPattern?.amount ?? 0, true)}
            </ApprovePaymentField>
          )}
          {method === PaymentMethod.DirectDeposit && (
            <>
              <ApprovePaymentField label="Account Type" required>
                {AccountTypesDisplay[bankAccount?.accountType]}
              </ApprovePaymentField>
              <ApprovePaymentField label="Routing Number" required>
                {bankAccount?.routingNumber ?? ''}
              </ApprovePaymentField>
              <ApprovePaymentField label="Account Number" required>
                {bankAccount?.accountNumber}
              </ApprovePaymentField>
            </>
          )}
          <ApprovePaymentField label="Payment Submission Date">
            {formatDate(today, DateFormat.ShortSlashes)}
          </ApprovePaymentField>
          {method === PaymentMethod.Check && (
            <ApprovePaymentField label="Check Stub Note">{note}</ApprovePaymentField>
          )}
          {isRecurring && (
            <ApprovePaymentField label="Payment Schedule" stretch={2} required={isRecurring}>
              {schedule.length > 0 && isPaymentDateValid(paymentPattern.start) && (
                <Typography variant="body">
                  <strong>{schedule?.length ?? 0} total:</strong>{' '}
                  {schedule
                    ?.map((each) => formatDate(each, DateFormat.ShortSlashes, true))
                    ?.join(', ') || ''}
                </Typography>
              )}
            </ApprovePaymentField>
          )}
          {!!pinnedDocuments?.length && (
            <ApprovePaymentField label="Additional Documents" stretch={2}>
              {pinnedDocuments.map((document, index) => (
                <div className="py-1" key={document.id}>
                  <UploadPreview
                    {...document}
                    key={document.previewUrl}
                    name={document.filename}
                    editable={false}
                    id={index.toString()}
                    url={document.previewUrl}
                  />
                </div>
              ))}
            </ApprovePaymentField>
          )}
        </FieldDetailList>
      </div>

      <Typography variant="body">
        If the above information looks correct, select “Approve Payment” below to confirm.
      </Typography>
      {!hasSubmitted &&
        (hasRequiredPaymentFields(method, payee, fulfillment) ? (
          <Message variant="warning">
            <strong>WARNING: This action cannot be undone</strong>
          </Message>
        ) : (
          <Message variant="warning">
            This payment cannot be approved without the required information.
            <br />
            <br />
            {getMissingPaymentFieldsMessage(payee, fulfillment)}
          </Message>
        ))}
      {error?.retryable && <Message variant="warning">{error?.message}</Message>}

      <Flex gap="4" justify="end">
        <Button type="button" onClick={onClose} variant="soft" disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" onClick={onSubmit} disabled={loading} loading={loading}>
          Approve Payment
        </Button>
      </Flex>
    </div>
  );
}

export default function PartnerIssuesContent(props: PartnerIssuesContentProps): JSX.Element {
  const {
    onClose,
    isOpen,
    fulfillment,
    case: { id: caseId },
  } = props;
  const { payee, loading: payeeLoading } = usePayee(fulfillment);
  const submitClaim = useSubmitClaim(fulfillment, caseId);

  return (
    <Modal
      size="S"
      onClickClose={onClose}
      isOpen={isOpen}
      title="Initiate Payment"
      preventClose={submitClaim.loading}
    >
      {payeeLoading ? (
        <div className="w-full py-10 flex justify-center">
          <Loading size="XL" />
        </div>
      ) : (
        <ModalContent {...props} payee={payee} submitClaim={submitClaim} />
      )}
    </Modal>
  );
}

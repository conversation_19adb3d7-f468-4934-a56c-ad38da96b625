import FieldDetailList from '@/spa-legacy/common/components/FieldDetail/FieldDetailList';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import Typography from '@/spa-legacy/common/components/Typography';
import { DateFormat, formatCurrency, formatDate } from '@/spa-legacy/common/utilities/format';
import type { Case, Fulfillment } from '@bybeam/platform-types';
import { Button, Flex } from '@radix-ui/themes';
import useApprovePayments from '../../hooks/useApprovePayments';
import { ModalDetail } from '../../utils/types';
import ApprovePaymentField from '../ApprovePaymentField';

interface ClaimFundsContentProps {
  case: Case;
  fulfillment: Fulfillment;
  modalRedirect: (modal: ModalDetail) => void;
  onClose: () => void;
  isOpen: boolean;
}

export default function ClaimFundsContent({
  case: {
    applications: [{ displayId }],
  },
  fulfillment,
  onClose,
  modalRedirect,
  isOpen,
}: ClaimFundsContentProps): JSX.Element {
  const today = new Date();
  const { approvedAmount } = fulfillment;
  const { approveAmount, loading } = useApprovePayments();

  const onSubmit = async (): Promise<void> => {
    const success = await approveAmount();
    modalRedirect(success ? ModalDetail.Initiated : ModalDetail.Error);
  };

  return (
    <Modal
      size="S"
      onClickClose={onClose}
      isOpen={isOpen}
      title="Initiate Payment"
      preventClose={loading}
    >
      <div className="mt-2 mb-6">
        <Typography variant="h3">{fulfillment?.payments[0]?.payee.name ?? ''}</Typography>
      </div>
      <div className="flex w-full">
        <div>
          <FieldDetailList columns={2} condensed>
            <ApprovePaymentField label="Application ID">{displayId}</ApprovePaymentField>
            <ApprovePaymentField label="Payment Submission Date">
              {formatDate(today, DateFormat.ShortSlashes)}
            </ApprovePaymentField>
            <ApprovePaymentField label="Approved Amount">
              {formatCurrency(approvedAmount, true)}
            </ApprovePaymentField>
          </FieldDetailList>
          <div className="grid gap-8 mb-8 mt-8">
            <Typography variant="body">
              If the above information looks correct, select “Initiate Payment” below to confirm.
            </Typography>
          </div>
        </div>
      </div>
      <div className="grid gap-8 mb-8">
        <div className="flex flex-col gap-y-4">
          <Message variant="warning">
            <strong>WARNING: This action cannot be undone</strong>
          </Message>
        </div>
      </div>

      <Flex gap="4" justify="end">
        <Button type="button" onClick={onClose} variant="soft" disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" onClick={onSubmit} disabled={loading}>
          Initiate Payment
        </Button>
      </Flex>
    </Modal>
  );
}

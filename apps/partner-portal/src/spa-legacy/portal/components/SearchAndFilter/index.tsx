import SavedViewControl from '@/app/[partnerId]/(authenticated)/programs/[programId]/(details)/components/SavedViewControl/SavedViewControl';
import { Flex } from '@radix-ui/themes';
import { useParams } from 'next/navigation';
import FilterMenu from './components/FilterMenu';
import SearchBuckets from './components/SearchBuckets';
import type { Option } from './components/SearchBuckets';
import type useSearchAndFilters from './useSearchAndFilters';

type SearchAndFilterProps = ReturnType<typeof useSearchAndFilters> & {
  options: Option[];
  filterSections?: string[];
};

export default function SearchAndFilter({
  options,
  search,
  setSearch,
  filters,
  setFilters,
  searchCategories,
  setSearchCategories,
  filterSections,
}: SearchAndFilterProps) {
  const params = useParams();

  return (
    <Flex gap="3" align="center">
      <SearchBuckets
        initialSearch={search}
        onSearch={setSearch}
        buckets={searchCategories}
        setBuckets={setSearchCategories}
        options={options}
      />
      <FilterMenu filterSections={filterSections} selected={filters} setSelected={setFilters} />
      {params?.programId ? (
        <SavedViewControl />
      ) : null}
    </Flex>
  );
}

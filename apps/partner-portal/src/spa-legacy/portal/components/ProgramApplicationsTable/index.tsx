import { Permission } from '@/app/_utils/roles';
import CanAccess from '@/app/components/features/CanAccess';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import {
  ApplicationConfig,
  ApplicationQuestionGroup,
  BaseField,
  FeatureName,
  Nullable,
  Program,
} from '@bybeam/platform-types';
import Table from '@platform-ui-common/components/Table';
import TableActions from '@platform-ui-common/components/Table/components/TableActions';
import TableHeader from '@platform-ui-common/components/Table/components/TableHeader';
import usePartner from '@platform-ui-common/hooks/contexts/usePartner';
import useProgram from '@platform-ui-common/hooks/contexts/useProgram';
import useThrottle from '@platform-ui-common/hooks/useThrottle';
import { formatNumber } from '@platform-ui-common/utilities/format';
import { getProgramWorkflow } from '@platform-ui/portal/utils/workflow';
import { makeRoute } from '@platform-ui/utilities/Routes';
import { getFirstPartyApplicationConfig } from '@platform-ui/utilities/application/configs';
import { checkProgramHasAnyFeature } from '@platform-ui/utilities/checkFeature';
import { Button, Flex, Text, Tooltip } from '@radix-ui/themes';
import { Link as RadixLink } from '@radix-ui/themes';
import { Select, TextField } from '@radix-ui/themes';
import { useFeatureFlagEnabled, usePostHog } from 'posthog-js/react';
import { useEffect, useMemo, useState } from 'react';
import { PORTAL_ROUTES } from '../../PortalRoutes';
import SearchAndFilter from '../SearchAndFilter';
import { FilterSections } from '../SearchAndFilter/utils/filterSections';
import Tabs from '../Tabs';
import styles from './ProgramApplicationsTable.module.css';
import { BulkAction, BulkActionType, BulkActionsButton } from './components/Actions';
import { Action, TAB_LABELS, Tab } from './types';
import { getTabBulkActionProps } from './utils/bulkActions';
import getColumns from './utils/columns';
import getFiltersByWorkflow from './utils/filters';
import useProgramApplications from './utils/useProgramApplications';

const getFormattedQuestionKey = (questionKey: string | null | undefined): string => {
  const formattedKey =
    questionKey?.replace(/([A-Z])/g, ' $1').replace(/\.|(^.)/g, (str) => {
      if (str === '.') {
        return ' > ';
      }
      return str.toUpperCase();
    }) || '';

  return formattedKey;
};

interface SearchByQuestionProps {
  applicationConfig: ApplicationConfig;
  searchAnswers: Record<string, string | string[]>;
  setSearchAnswers: (searchAnswers: Record<string, string | string[]>) => void;
}

const OptionGroup = ({ questionGroup }: { questionGroup: ApplicationQuestionGroup }) => {
  return (
    <>
      <Select.Group>
        <Select.Label>
          <strong>{questionGroup.name}</strong>
        </Select.Label>
        {questionGroup.questions.flatMap((question) => {
          return question.fields.map((field: BaseField) => {
            if (field.type === 'typography' || !!field.isSensitive) return;
            return (
              <Select.Item
                value={field.key}
                key={`${question.key}-${field.key}`}
                title={`[${field.type}] ${question.copy.title}`}
              >
                <Flex gap="1">
                  <small>[{getFormattedQuestionKey(question.key)}]</small>
                  <small>{getFormattedQuestionKey(field.key)}</small>
                </Flex>
              </Select.Item>
            );
          });
        })}
      </Select.Group>
      <Select.Separator />
    </>
  );
};

const SearchByQuestion: React.FC<SearchByQuestionProps> = ({
  applicationConfig,
  searchAnswers,
  setSearchAnswers,
}) => {
  const firstPartyQuestionGroups = applicationConfig?.sections.flatMap((section) => {
    return section.questionGroups;
  });

  const [searchInput, setSearchInput] = useState<string>(
    searchAnswers.searchAnswersValue as string,
  );

  const throttledSearch = useThrottle(({ searchAnswersKey, searchAnswersValue }) => {
    const updatedSearchAnswers = { searchAnswersKey, searchAnswersValue };
    setSearchAnswers(updatedSearchAnswers);
  });

  useEffect(() => {
    if (searchInput !== searchAnswers.searchAnswersValue) {
      setSearchInput(searchAnswers.searchAnswersValue as string);
    }
  }, [searchAnswers.searchAnswersValue]);

  return (
    <>
      <Flex gap="1" align="center">
        <Select.Root
          value={searchAnswers?.searchAnswersKey as string}
          onValueChange={(newValue) => {
            setSearchAnswers({ searchAnswersKey: newValue, searchAnswersValue: '' });
          }}
        >
          <Tooltip
            content={
              getFormattedQuestionKey(searchAnswers?.searchAnswersKey as string) ||
              'Choose a question field'
            }
          >
            <Select.Trigger
              placeholder="Search by question responses"
              className={styles.questionFieldSelectTrigger}
            />
          </Tooltip>
          <Select.Content className={styles.questionFieldSelectContent}>
            {firstPartyQuestionGroups.map((questionGroup) => (
              <OptionGroup key={questionGroup.key} questionGroup={questionGroup} />
            ))}
          </Select.Content>
        </Select.Root>
        {searchAnswers?.searchAnswersKey ? (
          <Flex gap="3" align="center">
            <div>
              <TextField.Root
                className={styles.searchQuestionsTextField}
                placeholder={`Search "${getFormattedQuestionKey(searchAnswers?.searchAnswersKey as string)}"`}
                type="search"
                value={searchInput}
                onChange={(evt) => {
                  setSearchInput(evt.target.value);
                  throttledSearch({ ...searchAnswers, searchAnswersValue: evt.target.value });
                }}
              />
            </div>
            <Button
              variant="ghost"
              color="gray"
              onClick={async () => {
                setSearchInput('');
                setSearchAnswers({ searchAnswersKey: '', searchAnswersValue: '' });
              }}
            >
              Clear
            </Button>
          </Flex>
        ) : null}
      </Flex>
    </>
  );
};

const FISCAL_BULK_ACTIONS: BulkActionType[] = [BulkActionType.PAYMENT];

export default function ProgramApplicationsTable(): JSX.Element {
  const {
    actions,
    checkCases,
    checkedCases,
    counts,
    fetchData,
    filters,
    loading,
    preppedData,
    search,
    searchAnswers,
    searchCategories,
    setFilters,
    setSearch,
    setSearchAnswers,
    setSearchCategories,
    setTabFilter,
    tabFilter,
    tableParams,
    tabs,
    totalRows,
    uncheckCases,
  } = useProgramApplications();
  const posthog = usePostHog();
  // Reactive PostHog flag for rules engine – drives conditional Eligibility column
  const showEligibility = useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.rulesEngine);
  const program = useProgram();
  const partner = usePartner();
  const { canAccess: canAccessPayments } = useCanAccess({
    resource: { objectType: 'PROGRAM', objectId: program.id },
    permission: Permission.Fiscal,
  });
  const applicationConfig = getFirstPartyApplicationConfig(program);
  const hasCheckedCases = checkedCases.length > 0;
  const allCasesChecked = hasCheckedCases && checkedCases.length === preppedData.length;
  const columns = useMemo(
    () =>
      getColumns(
        tabFilter,
        allCasesChecked,
        checkCases,
        totalRows,
        actions?.length > 0,
        program,
        partner,
        applicationConfig,
        showEligibility,
      ),
    [
      tabFilter,
      allCasesChecked,
      checkCases,
      totalRows,
      actions,
      program,
      applicationConfig,
      partner,
      showEligibility,
    ],
  );

  const [action, setAction] = useState<Nullable<BulkActionType>>(undefined);

  const getTabLabel = (tab: Tab): string => {
    const label = TAB_LABELS[tab];
    if (!counts) return label;

    let count = counts[tab] ?? 0;
    if (tab === Tab.All && !filters.includes('archived_true')) count -= counts.Archived;
    return `${label} (${formatNumber(count)})`;
  };

  const shouldShowBulkActionsButton = actions.includes(Action.BulkAction) && hasCheckedCases;

  const canAccessAction = (action: BulkActionType) => {
    if (FISCAL_BULK_ACTIONS.includes(action)) return canAccessPayments;
    return true;
  };

  return (
    <>
      <Table
        border={false}
        key={tabFilter}
        columns={columns}
        data={{ rows: preppedData ?? [], total: totalRows }}
        state={tableParams}
        onStateChange={fetchData}
        loading={loading}
        checkboxes={actions?.length > 0}
      >
        <TableHeader>
          <Tabs
            id="applications"
            tabs={tabs.map((tab) => ({ label: getTabLabel(tab), value: tab }))}
            selectedTab={tabFilter}
            setSelectedTab={setTabFilter}
          />
        </TableHeader>
        <SearchAndFilter
          search={search}
          setSearch={setSearch}
          searchAnswers={searchAnswers}
          setSearchAnswers={setSearchAnswers}
          searchCategories={searchCategories}
          setSearchCategories={setSearchCategories}
          filters={filters}
          setFilters={setFilters}
          options={getFiltersByWorkflow(getProgramWorkflow(program))}
          filterSections={tabFilter === Tab.MyAssignments ? [FilterSections.Assignee] : []}
        />
        <TableActions>
          {actions && (
            <div className="flex items-center gap-4 grow">
              {shouldShowBulkActionsButton && (
                <CanAccess resource={{ objectId: program?.id, objectType: 'PROGRAM' }}>
                  <BulkActionsButton
                    actionProps={getTabBulkActionProps({
                      tab: tabFilter,
                      program: program as Program,
                      checkedCases,
                      data: preppedData,
                      canAccess: canAccessAction,
                    })}
                    onClick={(value) => setAction(value)}
                  />
                </CanAccess>
              )}
              {hasCheckedCases && <Text size="2">({checkedCases.length}) Items selected</Text>}
              {checkProgramHasAnyFeature(program, [FeatureName.SearchAnswers]) ? (
                <SearchByQuestion
                  applicationConfig={applicationConfig}
                  searchAnswers={searchAnswers}
                  setSearchAnswers={setSearchAnswers}
                />
              ) : null}
            </div>
          )}
          {posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.caseDetails2024)
            ? null
            : checkFeature(program?.features, FeatureName.WorkflowCreateApplications) && (
                <CanAccess resource={{ objectId: program?.id, objectType: 'PROGRAM' }}>
                  <Button asChild variant="ghost">
                    <RadixLink
                      href={makeRoute(PORTAL_ROUTES.CREATE_ACCOUNT, { programId: program.id })}
                    >
                      Add New Applicant
                    </RadixLink>
                  </Button>
                </CanAccess>
              )}
        </TableActions>
      </Table>
      {action && (
        <BulkAction
          action={action}
          ids={checkedCases}
          onClose={(): void => {
            setAction(undefined);
            uncheckCases();
          }}
        />
      )}
    </>
  );
}

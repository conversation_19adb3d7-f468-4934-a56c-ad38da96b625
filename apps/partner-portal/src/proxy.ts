import { trace } from '@opentelemetry/api';
import { env } from 'next-runtime-env';
import { NextRequest, NextResponse } from 'next/server';

export function proxy(request: NextRequest) {
  const nonce = Buffer.from(crypto.randomUUID()).toString('base64');
  const phCSPReportingEndpoint = `https://us.i.posthog.com/report/?token=${env('NEXT_PUBLIC_POSTHOG_KEY')}&sample_rate=0.2&v=1`;
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'nonce-${nonce}' 'strict-dynamic'
      ${process.env.NODE_ENV === 'development' ? "'unsafe-eval'" : ''}
      blob: *.bybeam.co *.google.com *.googleapis.com *.gstatic.com *.posthog.com;
    style-src 'self' 'unsafe-inline' *.posthog.com;
    connect-src 'self' ${process.env.NODE_ENV === 'development' ? 'localhost:* 127.0.0.1:*' : ''} blob: data: *.bybeam.co *.googleapis.com *.grafana.net *.gstatic.com *.hashboard.com *.instatus.com *.posthog.com *.search.hereapi.com api.schematichq.com hashboard.com ik.imagekit.io manage.app.preset.io *.preset.io https://api.app.preset.io;
    img-src 'self' blob: data: *.google.com *.googleapis.com *.gstatic.com ik.imagekit.io uploads-ssl.webflow.com;
    object-src 'self' blob: *.googleapis.com;
    base-uri 'self';
    frame-src 'self' blob: https://*.googleapis.com https://hashboard.com https://glean.io https://manage.app.preset.io https://*.preset.io https://*.presetcloud.io https://*.beam-gcp-mpc.app.preset.io https://*.google.com https://*.bybeam.co;
    frame-ancestors 'self';
    report-uri ${phCSPReportingEndpoint};
    report-to posthog;
  `;

  // Replace newline characters and spaces
  const contentSecurityPolicyHeaderValue = cspHeader.replace(/\s{2,}/g, ' ').trim();

  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-nonce', nonce);
  requestHeaders.set('Content-Security-Policy', contentSecurityPolicyHeaderValue);

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  const headers: Record<string, string> = {
    'Content-Security-Policy': contentSecurityPolicyHeaderValue,
    'Reporting-Endpoints': `posthog="${phCSPReportingEndpoint}"`,
    'Permissions-Policy': 'camera=(),microphone=()',
  };

  // Set server-timing header with traceparent if active span exists
  const currentSpan = trace.getActiveSpan();
  if (currentSpan) {
    headers['server-timing'] =
      `traceparent;desc="00-${currentSpan.spanContext().traceId}-${currentSpan.spanContext().spanId}-01"`;
  }

  // Set all headers
  for (const [key, value] of Object.entries(headers)) {
    response.headers.set(key, value);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    {
      source: '/((?!api|_next/static|_next/image|favicon.ico).*)',
      missing: [
        { type: 'header', key: 'next-router-prefetch' },
        { type: 'header', key: 'purpose', value: 'prefetch' },
      ],
    },
  ],
};

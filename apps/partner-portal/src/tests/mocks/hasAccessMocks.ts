import CAN_ACCESS_QUERY from '@/app/providers/HasAccessQuery.graphql';
import { CheckAction, ObjectReference } from '@bybeam/identity-client/types';

interface HasAccessInput {
  action: CheckAction;
  resource: ObjectReference;
}

interface HasAccessVariables {
  hasAccessInput: HasAccessInput;
}

// Common HasAccess mocks for different resource types and actions
export const HAS_ACCESS_ORGANIZATION_EDIT_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'ORGANIZATION', objectId: 'mockPartnerId' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:organization:mockPartnerId',
        canAccess: true,
        message: 'user:mock-user-id can access organization:mockPartnerId',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_ORGANIZATION_VIEW_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'view',
        resource: { objectType: 'ORGANIZATION', objectId: 'mockPartnerId' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:organization:mockPartnerId',
        canAccess: true,
        message: 'user:mock-user-id can access organization:mockPartnerId',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_VENDOR_VIEW_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'view',
        resource: { objectType: 'VENDOR', objectId: 'mock-vendor-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:vendor:mock-vendor-id',
        canAccess: true,
        message: 'user:mock-user-id can access vendor:mock-vendor-id',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_VENDOR_EDIT_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'VENDOR', objectId: 'mock-vendor-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:vendor:mock-vendor-id',
        canAccess: true,
        message: 'user:mock-user-id can access vendor:mock-vendor-id',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_CASE_EDIT_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'CASE', objectId: 'mock-case-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:case:mock-case-id',
        canAccess: true,
        message: 'user:mock-user-id can access case:mock-case-id',
        token: 'mockToken',
      },
    },
  },
  newData: () => ({
    data: {
      hasAccess: {
        id: 'user:mock-user-id:case:mock-case-id',
        canAccess: true,
        message: 'user:mock-user-id can access case:mock-case-id',
        token: 'mockToken',
      },
    },
  }),
};

export const HAS_ACCESS_CASE_EDIT_UNDEFINED_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'CASE', objectId: undefined },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:case:undefined',
        canAccess: true,
        message: 'user:mock-user-id can access case:undefined',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_CASE_VIEW_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'view',
        resource: { objectType: 'CASE', objectId: 'mock-case-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:case:mock-case-id',
        canAccess: true,
        message: 'user:mock-user-id can access case:mock-case-id',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_PROGRAM_EDIT_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'PROGRAM', objectId: 'mock-program-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:program:mock-program-id',
        canAccess: true,
        message: 'user:mock-user-id can access program:mock-program-id',
        token: 'mockToken',
      },
    },
  },
};

export const HAS_ACCESS_PROGRAM_VIEW_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'view',
        resource: { objectType: 'PROGRAM', objectId: 'mock-program-id' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:program:mock-program-id',
        canAccess: true,
        message: 'user:mock-user-id can access program:mock-program-id',
        token: 'mockToken',
      },
    },
  },
};

// Flexible mock that matches any HasAccess query with edit action and ORGANIZATION type
export const HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectType: 'ORGANIZATION', objectId: 'mockPartnerId' },
      },
    },
  },
  result: (variables: HasAccessVariables) => ({
    data: {
      hasAccess: {
        id: `user:mock-user-id:organization:${variables.hasAccessInput?.resource?.objectId || 'mockPartnerId'}`,
        canAccess: true,
        message: `user:mock-user-id can access organization:${variables.hasAccessInput?.resource?.objectId || 'mockPartnerId'}`,
        token: 'mockToken',
      },
    },
  }),
  newData: (variables: HasAccessVariables) => ({
    data: {
      hasAccess: {
        id: `user:mock-user-id:organization:${variables.hasAccessInput?.resource?.objectId || 'mockPartnerId'}`,
        canAccess: true,
        message: `user:mock-user-id can access organization:${variables.hasAccessInput?.resource?.objectId || 'mockPartnerId'}`,
        token: 'mockToken',
      },
    },
  }),
};

// Comprehensive mock suite for common HasAccess patterns
export const HAS_ACCESS_COMMON_MOCKS = [
  HAS_ACCESS_ORGANIZATION_EDIT_MOCK,
  HAS_ACCESS_ORGANIZATION_VIEW_MOCK,
  HAS_ACCESS_VENDOR_VIEW_MOCK,
  HAS_ACCESS_VENDOR_EDIT_MOCK,
  HAS_ACCESS_CASE_EDIT_MOCK,
  HAS_ACCESS_CASE_VIEW_MOCK,
  HAS_ACCESS_PROGRAM_EDIT_MOCK,
  HAS_ACCESS_PROGRAM_VIEW_MOCK,
  HAS_ACCESS_FLEXIBLE_ORGANIZATION_EDIT_MOCK,
];

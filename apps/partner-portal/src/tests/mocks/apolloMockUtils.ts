import { MockedResponse } from '@apollo/client/testing';
import { DocumentNode } from 'graphql';

/**
 * Utility functions for creating comprehensive Apollo mocks that prevent
 * "No more mocked responses" warnings in Storybook tests.
 */

/**
 * Creates a mock that matches any variables for a given query.
 * Useful for queries that might be called with different variable combinations.
 */
export function createFlexibleMock(
  query: DocumentNode,
  data: Record<string, unknown>,
  defaultVariables?: Record<string, unknown>,
): MockedResponse {
  return {
    request: { 
      query, 
      variables: defaultVariables || {} 
    },
    result: { data },
    // Allow this mock to be reused multiple times
    newData: () => ({ data }),
  };
}

/**
 * Creates a mock that matches specific variable patterns.
 * Useful when you need to handle multiple variable combinations for the same query.
 */
export function createVariableSpecificMock(
  query: DocumentNode,
  dataFactory: (variables: Record<string, unknown>) => Record<string, unknown>,
  variableMatcher?: (variables: Record<string, unknown>) => boolean,
): MockedResponse {
  return {
    request: { 
      query, 
      variables: variableMatcher ? {} : undefined 
    },
    result: (variables: Record<string, unknown>) => {
      if (variableMatcher && !variableMatcher(variables)) {
        throw new Error(`Mock variables don't match: ${JSON.stringify(variables)}`);
      }
      return { data: dataFactory(variables) };
    },
    newData: (variables: Record<string, unknown>) => {
      if (variableMatcher && !variableMatcher(variables)) {
        throw new Error(`Mock variables don't match: ${JSON.stringify(variables)}`);
      }
      return { data: dataFactory(variables) };
    },
  };
}

/**
 * Creates multiple mocks for the same query with different variable combinations.
 * This prevents "No more mocked responses" warnings by providing multiple
 * mock responses for different variable patterns.
 */
export function createMultipleVariableMocks(
  query: DocumentNode,
  mockConfigs: Array<{
    variables: Record<string, unknown>;
    data: Record<string, unknown>;
  }>,
): MockedResponse[] {
  return mockConfigs.map(config => ({
    request: {
      query,
      variables: config.variables,
    },
    result: { data: config.data },
  }));
}

/**
 * Creates a comprehensive mock setup for paginated queries.
 * Handles common pagination patterns like empty initial state and populated results.
 */
export function createPaginatedQueryMocks(
  query: DocumentNode,
  emptyData: Record<string, unknown>,
  populatedData: Record<string, unknown>,
  variableOverrides?: Record<string, unknown>,
): MockedResponse[] {
  const baseVariables = {
    pagination: { page: 0, take: 15 },
    ...variableOverrides,
  };

  return [
    // Empty state mock (for initial queries with empty filters)
    {
      request: {
        query,
        variables: {
          ...baseVariables,
          filter: { ids: [] },
        },
      },
      result: { data: emptyData },
    },
    // Populated state mock (for queries with actual data)
    {
      request: {
        query,
        variables: {
          ...baseVariables,
          filter: { ids: ['item-1', 'item-2', 'item-3'] },
        },
      },
      result: { data: populatedData },
    },
  ];
}

/**
 * Creates a loading mock with configurable delay.
 */
export function createLoadingMock(
  query: DocumentNode,
  variables?: Record<string, unknown>,
  delay = 100000000000000,
): MockedResponse {
  return {
    request: { query, variables },
    delay,
    result: undefined,
  };
}

/**
 * Creates an error mock for testing error states.
 */
export function createErrorMock(
  query: DocumentNode,
  variables?: Record<string, unknown>,
  errorMessage = 'Mock server error',
): MockedResponse {
  return {
    request: { query, variables },
    error: new Error(errorMessage),
  };
}

/**
 * Utility to suppress Apollo warnings in Storybook by providing
 * comprehensive mock coverage for common query patterns.
 */
export function createComprehensiveMockSuite(
  query: DocumentNode,
  mockConfig: {
    emptyData: Record<string, unknown>;
    populatedData: Record<string, unknown>;
    variableOverrides?: Record<string, unknown>;
    additionalVariableSets?: Array<Record<string, unknown>>;
  },
): MockedResponse[] {
  const mocks = createPaginatedQueryMocks(
    query,
    mockConfig.emptyData,
    mockConfig.populatedData,
    mockConfig.variableOverrides,
  );

  // Add additional variable sets if provided
  if (mockConfig.additionalVariableSets) {
    const additionalMocks = mockConfig.additionalVariableSets.map(variables => ({
      request: { query, variables },
      result: { data: mockConfig.populatedData },
    }));
    mocks.push(...additionalMocks);
  }

  return mocks;
}

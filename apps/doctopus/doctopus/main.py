from contextlib import asynccontextmanager

from doctopus.internal.entities.entities import EntityRequest, get_entities
import uvicorn
from fastapi import Fast<PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from doctopus.logger import logger
from doctopus.router import router
from doctopus.startup import initialize_tags
from doctopus.internal.programs.program_registry import (
    build_program_registry,
    ProgramRegistryManager,
)
from doctopus.utils import must_getenv

from doctopus.db.engine import async_doc_engine, doc_engine, core_engine
from doctopus.internal.ocr.document_processor import documentai_client

# Comment for deployment
PROGRAM_REGISTRY_MANAGER = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    global PROGRAM_REGISTRY_MANAGER

    # initialize tags
    await initialize_tags()
    logger.debug("Startup: tags initialized")

    # Initialize program specifications.
    initial_program_registry = await build_program_registry()

    # register to app state
    app.state.program_registry_manager = ProgramRegistryManager(initial_program_registry)

    logger.debug("Startup: Programs initialized")

    yield

    logger.debug("Cleanup: Shutting down.")

    # Add proper cleanup of database engines
    try:
        await async_doc_engine.dispose()
        logger.debug("Async document engine disposed")
    except Exception as e:
        logger.error(f"Error disposing async document engine: {e}")

    try:
        doc_engine.dispose()
        logger.debug("Document engine disposed")
    except Exception as e:
        logger.error(f"Error disposing document engine: {e}")

    try:
        core_engine.dispose()
        logger.debug("Core engine disposed")
    except Exception as e:
        logger.error(f"Error disposing core engine: {e}")

    # Add proper cleanup of gRPC client
    try:
        await documentai_client.close()
        logger.debug("Document AI client closed")
    except Exception as e:
        logger.error(f"Error closing Document AI client: {e}")


app = FastAPI(title="doctopus", lifespan=lambda app: lifespan(app))
app.include_router(router)

port = int(must_getenv("PORT"))


def start():
    """Launched with `poetry run start` at root level"""
    uvicorn.run("doctopus.main:app", host="0.0.0.0", port=port, reload=False, log_config=None)


def dev():
    """Launched with `poetry run dev` at root level"""
    print("Starting development server...")
    uvicorn.run("doctopus.main:app", host="0.0.0.0", port=port, reload=True)


# Add this to ensure the server starts when run as a module
if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "dev":
            dev()
        elif sys.argv[1] == "start":
            start()


@app.get("/healthy")
async def healthy():
    return {"HEALTH CHECK OK"}


@app.post("/entities")
async def get_entities_from_doc_ids(request: EntityRequest):
    # TODO try except logic here.
    document_ids = request.document_uuids()
    entities = await get_entities(document_ids)
    return [x.to_serializable_dict() for x in entities]


# Provides more detailed logging on request parsing/pydantic validation errors
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    exc_str = f"{exc}".replace("\n", " ").replace("   ", " ")
    logger.error(
        f"""Error in request validation: {exc_str}.
        {request.headers}
        Request Body: {await request.json()}
        """
    )
    content = {"status_code": 10422, "message": exc_str, "data": None}
    return JSONResponse(content=content, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY)

from dataclasses import dataclass
from google.cloud import documentai

from doctopus.internal.ocr.document_processor import documentai_client, documentai_processor
from doctopus.logger import logger


@dataclass
class OcrService:
    """This could be refactored to be more generic in case we want to have
    vertex OCR endpoints... but YAGNI prevails for now.
    """

    request_defects: bool = False
    request_langs: bool = False
    parser: str | None = None

    async def request(self, image_content: bytes, mimetype: str) -> dict:
        """Sends image content to OCR service for processing

        For more documentation on how to configure the request, see:
            - https://cloud.google.com/document-ai/docs/reference/rest/v1/ProcessOptions

        For more information on how the response is formatted, see:
            - https://cloud.google.com/document-ai/docs/reference/rest/v1/Document

        That returned dictionary can contain an error key, which indicates some
        error in the OCR process.

        It can also return additional metadata beyond the text, which contains
        information about e.g. defects etc.
        """
        raw_document = documentai.RawDocument(content=image_content, mime_type=mimetype)
        logger.debug("Created raw_document")

        # get process options
        process_options = self.build_process_options()
        logger.debug("Process options built.")

        # build appropriate field mask
        field_mask = self.build_field_mask()
        logger.debug(f"Requesting OCR with field_mask: {field_mask} ")

        # build final request
        request = documentai.ProcessRequest(
            name=self.parser or documentai_processor,
            raw_document=raw_document,
            field_mask=field_mask,
            process_options=process_options,
        )

        # submit request
        logger.debug(f"Sending request to OCR service. Mimetype {mimetype}.")
        result = await documentai_client.process_document(request=request)
        raw_document = documentai.Document.to_dict(result.document)
        logger.debug(f"Received document with the following keys: {list(raw_document.keys())}")

        return raw_document  # type: ignore

    def build_process_options(self) -> documentai.ProcessOptions:
        """Builds processor options depending on whether or not parser is selected."""
        logger.debug(
            f"building process options with parser: {self.parser} and {self.request_defects}."
        )
        if self.parser:
            # Processors don't support OCR config
            process_options = documentai.ProcessOptions(
                individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(pages=[1])
            )
        else:
            process_options = documentai.ProcessOptions(
                individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(
                    pages=[1]
                ),
                ocr_config=documentai.OcrConfig(enable_image_quality_scores=self.request_defects),
            )

        return process_options

    def build_field_mask(self) -> str:
        """Field mask limits what comes back from processor."""
        field_mask = "text"
        if self.request_defects:
            field_mask = field_mask + ",pages.imageQualityScores"
        if self.request_langs:
            field_mask = field_mask + ",pages.detectedLanguages"
        if self.parser:
            field_mask = field_mask + ",entities"

        return field_mask

from uuid import UUID

from doctopus.core.document_context import DocumentContext
from doctopus.internal.ocr.ocr_service import OcrService
from doctopus.internal.ocr.ocr_result_wrapper import (
    OcrResultWrapper,
    ocr_result_from_database,
    save_ocr_result_from_dict,
)
from doctopus.logger import logger


async def ocr_request_new(
    ctx: DocumentContext, request_defects: bool = True, request_langs: bool = True
) -> OcrResultWrapper | None:
    assert ctx.image_content, "Image content must exist."
    logger.debug("Requesting NEW ocr result from service.")

    # Get the raw result from the service
    try:
        raw_ocr_result = await OcrService(
            request_defects=request_defects,
            request_langs=request_langs,
        ).request(
            ctx.image_content,
            ctx.doc_data.mimetype,
        )
    except Exception:
        logger.error("Error requesting OCR from OCR service.")
        return None

    # Save result to DB and return new data
    try:
        wrapped_ocr_result = save_ocr_result_from_dict(
            ocr_id=ctx.summary.ocr_id,
            raw=raw_ocr_result,
            summary_id=ctx.summary.id,
        )
    except Exception:
        logger.error("Unable to save new OCR results to the database.")
        return None

    return wrapped_ocr_result


async def ocr_request_existing(ocr_id: UUID) -> OcrResultWrapper | None:
    try:
        wrapped_ocr_result = ocr_result_from_database(ocr_id)
        return wrapped_ocr_result
    except Exception:
        logger.error("Error fetching existing OCR from database.")
        return None


def is_defect_in_tags(tags: list[str]) -> bool:
    """Checks if any of the request tags are google ocr defects.

    These defects require us to set particular parameters in the request
    to the Enterprise OCR service.
    """
    google_ocr_defects = set(
        [
            "blurry",
            "document_cutoff",
            "faint",
            "glare",
            "text_cutoff",
            "text_too_small",
            "noisy",
        ]
    )

    # If more than one google ocr defect is requested, return true
    return len(set(tags).intersection(google_ocr_defects)) >= 1

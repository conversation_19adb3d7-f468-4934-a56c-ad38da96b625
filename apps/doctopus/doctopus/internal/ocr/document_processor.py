from google.cloud import documentai

from doctopus.utils import must_getenv
from doctopus.logger import logger

PROCESSOR_PROJ = must_getenv("PROCESSOR_PROJ")
PROCESSOR_ID = must_getenv("PROCESSOR_ID")


def _create_connections():
    try:
        doc_client = documentai.DocumentProcessorServiceAsyncClient()
        doc_proc = doc_client.processor_path(PROCESSOR_PROJ, "us", PROCESSOR_ID)
        logger.info("Connected to OCR SERVICE.")
    except Exception:
        logger.error("Failed to connect to OCR Processor.")
        exit()

    return doc_client, doc_proc


documentai_client, documentai_processor = _create_connections()

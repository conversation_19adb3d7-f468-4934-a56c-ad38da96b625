import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigService } from './config.service.js';

describe('ConfigService', () => {
  let service: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConfigService],
    })
      .useMocker((token) => {
        if (token === 'PinoLogger:ConfigService') return { info: vi.fn() };
        if (token === 'CONFIG_CLIENT') return {};
      })
      .compile();

    service = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getApplicationConfig', () => {
    it('should reject if there is an error', async () => {
      const getConfigFn = vi.fn().mockImplementationOnce((_, cb) => cb(new Error('failure')));
      const module: TestingModule = await Test.createTestingModule({
        providers: [ConfigService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:ConfigService') return { info: vi.fn() };
          if (token === 'CONFIG_CLIENT')
            return {
              getClientByServiceName: () => ({ getProgramApplicationConfigurations: getConfigFn }),
            };
        })
        .compile();

      await module.init();
      const service = module.get<ConfigService>(ConfigService);

      await expect(
        async () => await service.getApplicationConfig({ programId: ['mockProgramId'] }),
      ).rejects.toThrow('failure');
    });
    it('should return the config on success', async () => {
      const getConfigFn = vi.fn().mockImplementationOnce((_, cb) =>
        cb(null, [
          {
            programId: 'mockProgramId',
            applicantTypeId: 'mockFirstPartyId',
            configuration: { id: 'mockConfigId', config: '{}' },
          },
        ]),
      );
      const module: TestingModule = await Test.createTestingModule({
        providers: [ConfigService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:ConfigService') return { info: vi.fn() };
          if (token === 'CONFIG_CLIENT')
            return {
              getClientByServiceName: () => ({ getProgramApplicationConfigurations: getConfigFn }),
            };
        })
        .compile();

      await module.init();
      const service = module.get<ConfigService>(ConfigService);

      const result = await service.getApplicationConfig({ programId: ['mockProgramId'] });
      expect(result).toEqual([
        {
          applicantTypeId: 'mockFirstPartyId',
          configuration: { config: '{}', id: 'mockConfigId' },
          programId: 'mockProgramId',
        },
      ]);
    });
  });
});

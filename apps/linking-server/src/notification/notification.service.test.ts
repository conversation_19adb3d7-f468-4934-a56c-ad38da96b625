import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { NotificationService } from './notification.service.js';

describe('NotificationService', () => {
  let service: NotificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NotificationService],
    })
      .useMocker((token) => {
        if (token === 'PinoLogger:NotificationService') return { info: vi.fn() };
        if (token === 'NOTIFICATION_CLIENT') return {};
      })
      .compile();

    service = module.get<NotificationService>(NotificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmail', () => {
    it('should reject if there is an error', async () => {
      const sendEmailFn = vi.fn().mockImplementationOnce((_, cb) => cb(new Error('failure')));
      const module: TestingModule = await Test.createTestingModule({
        providers: [NotificationService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:NotificationService') return { info: vi.fn() };
          if (token === 'NOTIFICATION_CLIENT')
            return {
              getClientByServiceName: () => ({ sendEmail: sendEmailFn }),
            };
        })
        .compile();

      await module.init();
      const service = module.get<NotificationService>(NotificationService);

      await expect(
        async () => await service.sendEmail({ recipients: [], notificationConfig: {} }),
      ).rejects.toThrow('failure');
    });
    it('should return the response on success', async () => {
      const sendEmailFn = vi
        .fn()
        .mockImplementationOnce((_, cb) => cb(null, { message: 'success' }));
      const module: TestingModule = await Test.createTestingModule({
        providers: [NotificationService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:NotificationService') return { info: vi.fn() };
          if (token === 'NOTIFICATION_CLIENT')
            return {
              getClientByServiceName: () => ({ sendEmail: sendEmailFn }),
            };
        })
        .compile();

      await module.init();
      const service = module.get<NotificationService>(NotificationService);

      const result = await service.sendEmail({ recipients: [], notificationConfig: {} });
      expect(result).toEqual({ message: 'success' });
    });
  });
});

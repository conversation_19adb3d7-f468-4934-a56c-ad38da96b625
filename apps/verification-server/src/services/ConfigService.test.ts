import { ServiceType } from '@bybeam/verification-client';
import ConfigService, { ConfigServiceDependencies } from './ConfigService.js';

describe('ConfigService', () => {
  describe('getServiceConfig', () => {
    it('should throw if the service type is not implemented', async () => {
      const mockService = new ConfigService({
        configRepository: {
          findBy: vi.fn().mockResolvedValueOnce([{ id: 'mockConfigId', service: 'NotReal' }]),
        },
      } as unknown as ConfigServiceDependencies);
      await expect(
        mockService.getConfigurations({
          programIds: ['mockProgramId'],
        }),
      ).rejects.toThrow('Unsupported service NotReal');
    });

    it('should load the latest lookup configs for configs with service = DataLookup', async () => {
      const mockService = new ConfigService({
        configRepository: {
          findBy: vi.fn().mockResolvedValueOnce([
            {
              id: 'mockConfig1',
              applicantTypeId: 'mockApplicantType',
              programId: 'program1',
              service: ServiceType.DataLookup,
            },
            {
              id: 'mockConfig2',
              applicantTypeId: 'mockApplicantType',
              programId: 'program2',
              service: ServiceType.DataLookup,
            },
          ]),
        },
        lookupConfigRepository: {
          findLatest: vi.fn().mockResolvedValueOnce([
            { id: 'lookupConfig2', configId: 'mockConfig2', fields: [{ key: 'field2' }] },
            { id: 'lookupConfig1', configId: 'mockConfig1', fields: [{ key: 'field1' }] },
          ]),
        },
      } as unknown as ConfigServiceDependencies);

      const response = await mockService.getConfigurations({
        programIds: ['program1', 'program2'],
      });

      expect(response).toEqual({
        configurations: [
          {
            id: 'mockConfig1',
            applicantTypeId: 'mockApplicantType',
            programId: 'program1',
            service: ServiceType.DataLookup,
            dataLookup: { fields: [{ key: 'field1' }] },
          },
          {
            id: 'mockConfig2',
            applicantTypeId: 'mockApplicantType',
            programId: 'program2',
            service: ServiceType.DataLookup,
            dataLookup: { fields: [{ key: 'field2' }] },
          },
        ],
      });
    });

    it('should return an empty list of fields if there is no lookup config found', async () => {
      const mockService = new ConfigService({
        configRepository: {
          findBy: vi.fn().mockResolvedValueOnce([
            {
              id: 'mockConfig1',
              applicantTypeId: 'mockApplicantType',
              programId: 'program1',
              service: ServiceType.DataLookup,
            },
            {
              id: 'mockConfig2',
              applicantTypeId: 'mockApplicantType',
              programId: 'program2',
              service: ServiceType.DataLookup,
            },
          ]),
        },
        lookupConfigRepository: {
          findLatest: vi
            .fn()
            .mockResolvedValueOnce([
              { id: 'lookupConfig2', configId: 'mockConfig2', fields: [{ key: 'field2' }] },
            ]),
        },
      } as unknown as ConfigServiceDependencies);

      const response = await mockService.getConfigurations({
        programIds: ['program1', 'program2'],
      });

      expect(response).toEqual({
        configurations: [
          {
            id: 'mockConfig1',
            applicantTypeId: 'mockApplicantType',
            programId: 'program1',
            service: ServiceType.DataLookup,
            dataLookup: { fields: [] },
          },
          {
            id: 'mockConfig2',
            applicantTypeId: 'mockApplicantType',
            programId: 'program2',
            service: ServiceType.DataLookup,
            dataLookup: { fields: [{ key: 'field2' }] },
          },
        ],
      });
    });

    it('should return an empty list of configurations if none of the programs have a config', async () => {
      const mockService = new ConfigService({
        configRepository: {
          findBy: vi.fn().mockResolvedValueOnce([]),
        },
      } as unknown as ConfigServiceDependencies);

      const response = await mockService.getConfigurations({
        programIds: ['program1', 'program2'],
      });

      expect(response).toEqual({
        configurations: [],
      });
    });
  });
});

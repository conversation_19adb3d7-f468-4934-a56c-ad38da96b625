import { ServiceType } from '@bybeam/verification-client';
import { FileUploadStatus } from '../types/index.js';
import LookupConfigService, { ILookupConfig } from './LookupConfigService.js';

describe('LookupConfigService', () => {
  describe('setup service errors', () => {
    describe('when there is no lookup config for the given program', () => {
      it('should throw', async () => {
        const mockService = new LookupConfigService({
          fileRepository: {},
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce(undefined),
          },
          verifiedApplicantRepository: {},
        } as unknown as ILookupConfig['dependencies']);
        await expect(
          mockService.uploadFile({
            programId: 'mockProgramId',
            applicantTypeId: 'mockApplicantTypeId',
            service: ServiceType.DataLookup,
            filepath: 'mock/file/path/file.csv',
          }),
        ).rejects.toThrow('No data lookup config is defined for program mockProgramId');
      });
    });

    describe('when getFileHand<PERSON> throws', () => {
      it('should throw the error', async () => {
        const mockGetFileHandlerFn = vi.fn();
        const mockService = new LookupConfigService({
          fileRepository: {
            getFileHandler: mockGetFileHandlerFn.mockRejectedValueOnce(
              new Error('you have no domain over this file, brother'),
            ),
          },
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce({
              id: 'mockLookupConfigId',
              configId: 'mockConfigId',
              fields: [{ key: 'keyA' }],
            }),
          },
          verifiedApplicantRepository: {},
        } as unknown as ILookupConfig['dependencies']);
        await expect(
          mockService.uploadFile({
            programId: 'mockProgramId',
            applicantTypeId: 'mockApplicantTypeId',
            service: ServiceType.DataLookup,
            filepath: 'mock/file/path/file.csv',
          }),
        ).rejects.toThrow('you have no domain over this file, brother');
      });
    });
  });

  describe('validation errors', () => {
    describe("when the file headers don't match the keys on the config", () => {
      it('should throw and include the keys in the error message', async () => {
        const mockFileCleanupFn = vi.fn();
        const mockGetFileHandlerFn = vi.fn();
        const mockGetHeadersFn = vi.fn();
        const mockService = new LookupConfigService({
          fileRepository: {
            getFileHandler: mockGetFileHandlerFn.mockReturnValueOnce({
              cleanup: mockFileCleanupFn,
              getHeaders: mockGetHeadersFn.mockReturnValueOnce(['this', 'is', 'bad']),
            }),
          },
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce({
              id: 'mockLookupConfigId',
              configId: 'mockConfigId',
              fields: [{ key: 'this' }, { key: 'is' }, { key: 'good' }],
            }),
          },
          verifiedApplicantRepository: {},
        } as unknown as ILookupConfig['dependencies']);

        await expect(
          mockService.uploadFile({
            programId: 'mockProgramId',
            applicantTypeId: 'mockApplicantTypeId',
            service: ServiceType.DataLookup,
            filepath: 'mock/file/path/file.csv',
          }),
        ).rejects.toThrow(
          'Disjoint keys on config upsert request: {"headers":["bad","is","this"],"requestKeys":["good","is","this"]}',
        );
        expect(mockFileCleanupFn).toHaveBeenCalled();
      });
    });
  });

  const originalEnv = { ...process.env };
  beforeEach(() => {
    process.env.APPLICANT_CHUNK_SIZE = '3';
  });
  afterEach(() => {
    process.env = { ...originalEnv };
  });

  describe('valid uploads', () => {
    describe('when the file size is smaller than the chunk size', () => {
      it('should insert a new upload and load the applicants in one chunk', async () => {
        const mockCleanupFn = vi.fn();
        const insertApplicants = vi.fn();
        const deactivateApplicants = vi.fn();
        const saveFileUpload = vi.fn();
        const mockService = new LookupConfigService({
          fileRepository: {
            getFileHandler: vi.fn().mockReturnValueOnce({
              cleanup: mockCleanupFn,
              getHeaders: vi.fn().mockReturnValueOnce(['this', 'good']),
              getIterator: vi
                .fn()
                .mockReturnValueOnce([
                  { fields: { this: 'A', good: 1 } },
                  { fields: { this: 'B', good: 2 } },
                  { fields: { this: 'C', good: 3 } },
                ]),
            }),
          },
          fileUploadRepository: {
            save: saveFileUpload.mockResolvedValueOnce({ id: 'mockFileUploadId' }),
          },
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce({
              id: 'mockLookupConfigId',
              configId: 'mockConfigId',
              fields: [{ key: 'this' }, { key: 'good' }],
            }),
          },
          verifiedApplicantRepository: {
            insert: insertApplicants.mockResolvedValueOnce({}),
            deactivateExistingApplicants: deactivateApplicants,
          },
        } as unknown as ILookupConfig['dependencies']);

        const result = await mockService.uploadFile({
          programId: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          service: ServiceType.DataLookup,
          filepath: 'mock/file/path/file.csv',
        });

        expect(mockCleanupFn).toHaveBeenCalled();
        expect(result).toEqual({
          body: {
            message: 'Data Lookup configuration successfully updated for program mockProgramId',
          },
          status: null,
        });
        expect(saveFileUpload).toHaveBeenNthCalledWith(1, {
          lookupConfigId: 'mockLookupConfigId',
          filepath: 'mock/file/path/file.csv',
        });
        expect(saveFileUpload).toHaveBeenNthCalledWith(2, {
          id: 'mockFileUploadId',
          status: FileUploadStatus.Success,
        });
        expect(insertApplicants).toHaveBeenCalledTimes(1);
        expect(insertApplicants).toHaveBeenCalledWith([
          {
            fields: { fields: { good: 1, this: 'A' } },
            fileUploadId: 'mockFileUploadId',
          },
          {
            fields: { fields: { good: 2, this: 'B' } },
            fileUploadId: 'mockFileUploadId',
          },
          {
            fields: { fields: { good: 3, this: 'C' } },
            fileUploadId: 'mockFileUploadId',
          },
        ]);
        expect(deactivateApplicants).toHaveBeenCalledWith({
          newFileUploadId: 'mockFileUploadId',
          configId: 'mockConfigId',
        });
      });
    });

    describe('when the file size is larger than the chunk size', () => {
      it('should load the applicants in batches', async () => {
        const mockCleanupFn = vi.fn();
        const insertApplicants = vi.fn();
        const deactivateApplicants = vi.fn();
        const saveFileUpload = vi.fn();
        const mockService = new LookupConfigService({
          fileRepository: {
            getFileHandler: vi.fn().mockReturnValueOnce({
              cleanup: mockCleanupFn,
              getHeaders: vi.fn().mockReturnValueOnce(['this', 'good']),
              getIterator: vi
                .fn()
                .mockReturnValueOnce([
                  { fields: { this: 'A', good: 1 } },
                  { fields: { this: 'B', good: 2 } },
                  { fields: { this: 'C', good: 3 } },
                  { fields: { this: 'D', good: 4 } },
                  { fields: { this: 'E', good: 5 } },
                  { fields: { this: 'F', good: 6 } },
                  { fields: { this: 'G', good: 7 } },
                ]),
            }),
          },
          fileUploadRepository: {
            save: saveFileUpload.mockResolvedValueOnce({ id: 'mockFileUploadId' }),
          },
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce({
              id: 'mockLookupConfigId',
              configId: 'mockConfigId',
              fields: [{ key: 'this' }, { key: 'good' }],
            }),
          },
          verifiedApplicantRepository: {
            insert: insertApplicants.mockResolvedValue({}),
            deactivateExistingApplicants: deactivateApplicants,
          },
        } as unknown as ILookupConfig['dependencies']);

        const result = await mockService.uploadFile({
          programId: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          service: ServiceType.DataLookup,
          filepath: 'mock/file/path/file.csv',
        });

        expect(mockCleanupFn).toHaveBeenCalled();
        expect(result).toEqual({
          body: {
            message: 'Data Lookup configuration successfully updated for program mockProgramId',
          },
          status: null,
        });
        expect(saveFileUpload).toHaveBeenNthCalledWith(1, {
          lookupConfigId: 'mockLookupConfigId',
          filepath: 'mock/file/path/file.csv',
        });
        expect(saveFileUpload).toHaveBeenNthCalledWith(2, {
          id: 'mockFileUploadId',
          status: FileUploadStatus.Success,
        });
        expect(insertApplicants).toHaveBeenCalledTimes(3);
        expect(insertApplicants).toHaveBeenNthCalledWith(1, [
          {
            fields: { fields: { good: 1, this: 'A' } },
            fileUploadId: 'mockFileUploadId',
          },
          {
            fields: { fields: { good: 2, this: 'B' } },
            fileUploadId: 'mockFileUploadId',
          },
          {
            fields: { fields: { good: 3, this: 'C' } },
            fileUploadId: 'mockFileUploadId',
          },
        ]);
        expect(insertApplicants).toHaveBeenNthCalledWith(2, [
          {
            fields: { fields: { good: 4, this: 'D' } },
            fileUploadId: 'mockFileUploadId',
          },
          {
            fields: { fields: { good: 5, this: 'E' } },
            fileUploadId: 'mockFileUploadId',
          },
          {
            fields: { fields: { good: 6, this: 'F' } },
            fileUploadId: 'mockFileUploadId',
          },
        ]);
        expect(insertApplicants).toHaveBeenNthCalledWith(3, [
          {
            fields: { fields: { good: 7, this: 'G' } },
            fileUploadId: 'mockFileUploadId',
          },
        ]);
        expect(deactivateApplicants).toHaveBeenCalledWith({
          newFileUploadId: 'mockFileUploadId',
          configId: 'mockConfigId',
        });
      });
    });
  });

  describe('failed uploads', () => {
    describe('when the file upload was never saved', () => {
      it('propagates the error up', async () => {
        const mockCleanupFn = vi.fn();
        const mockService = new LookupConfigService({
          fileRepository: {
            getFileHandler: vi.fn().mockReturnValueOnce({
              cleanup: mockCleanupFn,
              getHeaders: vi.fn().mockReturnValueOnce(['this', 'good']),
            }),
          },
          fileUploadRepository: {
            save: vi.fn().mockRejectedValueOnce(new Error('this save went bad!')),
          },
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce({
              id: 'mockLookupConfigId',
              configId: 'mockConfigId',
              fields: [{ key: 'this' }, { key: 'good' }],
            }),
          },
          verifiedApplicantRepository: {},
        } as unknown as ILookupConfig['dependencies']);

        await expect(
          mockService.uploadFile({
            programId: 'mockProgramId',
            applicantTypeId: 'mockApplicantTypeId',
            service: ServiceType.DataLookup,
            filepath: 'mock/file/path/file.csv',
          }),
        ).rejects.toThrow('this save went bad!');

        expect(mockCleanupFn).toHaveBeenCalled();
      });
    });

    describe('when something fails in the middle of the update', () => {
      it('should deactivate any new applicants and leave the existing configuration active', async () => {
        const mockCleanupFn = vi.fn();
        const insertApplicants = vi.fn();
        const softDeleteApplicants = vi.fn();
        const saveFileUpload = vi.fn();
        const mockService = new LookupConfigService({
          fileRepository: {
            getFileHandler: vi.fn().mockReturnValueOnce({
              cleanup: mockCleanupFn,
              getHeaders: vi.fn().mockReturnValueOnce(['this', 'good']),
              getIterator: vi
                .fn()
                .mockReturnValueOnce([
                  { fields: { this: 'A', good: 1 } },
                  { fields: { this: 'B', good: 2 } },
                  { fields: { this: 'C', good: 3 } },
                  { fields: { this: 'D', good: 4 } },
                  { fields: { this: 'E', good: 5 } },
                  { fields: { this: 'F', good: 6 } },
                  { fields: { this: 'G', good: 7 } },
                ]),
            }),
          },
          fileUploadRepository: {
            save: saveFileUpload.mockResolvedValueOnce({ id: 'mockFileUploadId' }),
          },
          lookupConfigRepository: {
            findOneBy: vi.fn().mockResolvedValueOnce({
              id: 'mockLookupConfigId',
              configId: 'mockConfigId',
              fields: [{ key: 'this' }, { key: 'good' }],
            }),
          },
          verifiedApplicantRepository: {
            insert: insertApplicants
              .mockResolvedValueOnce({})
              .mockRejectedValueOnce(new Error('this row went bad, hope you can rollback')),
            softDelete: softDeleteApplicants,
          },
        } as unknown as ILookupConfig['dependencies']);

        await expect(
          mockService.uploadFile({
            programId: 'mockProgramId',
            applicantTypeId: 'mockApplicantTypeId',
            service: ServiceType.DataLookup,
            filepath: 'mock/file/path/file.csv',
          }),
        ).rejects.toThrow('this row went bad, hope you can rollback');

        expect(mockCleanupFn).toHaveBeenCalled();
        expect(insertApplicants).toHaveBeenCalledTimes(2);
        expect(softDeleteApplicants).toHaveBeenCalledWith({ fileUploadId: 'mockFileUploadId' });
        expect(saveFileUpload).toHaveBeenCalledWith({
          id: 'mockFileUploadId',
          status: FileUploadStatus.Failed,
        });
      });
    });
  });

  describe('upsertConfig', () => {
    it('should return a success response when finding a lookupConfig', async () => {
      const mockUpdateFn = vi.fn();
      const mockService = new LookupConfigService({
        lookupConfigRepository: {
          findOneBy: vi.fn().mockResolvedValue({ id: 'mockLookupConfigId', fields: [] }),
          update: mockUpdateFn,
          save: vi.fn(),
        },
      } as unknown as ILookupConfig['dependencies']);
      const result = await mockService.upsertConfig({
        programId: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [
          {
            key: 'this',
            details: 'foo',
            sample: 'verde',
            _weight: 'weight',
            metadata: false,
          },
          { key: 'good', details: 'gee', sample: 'azul', _weight: 'weight', metadata: false },
        ],
      });
      expect(result).toEqual({
        body: {
          message:
            'Data Lookup fields successfully updated lookup config for program: mockProgramId',
        },
        status: null,
      });
      expect(mockUpdateFn).toHaveBeenCalledWith('mockLookupConfigId', {
        fields: [
          {
            key: 'this',
            details: 'foo',
            sample: 'verde',
            _weight: 'weight',
            metadata: false,
          },
          { key: 'good', details: 'gee', sample: 'azul', _weight: 'weight', metadata: false },
        ],
      });
    });
    it('should return a success response when not finding a lookupConfig', async () => {
      const mockSaveFn = vi.fn();
      const mockCreate = vi.fn().mockReturnValue({ id: 'mockConfigId' });

      const mockService = new LookupConfigService({
        lookupConfigRepository: {
          findOneBy: vi.fn().mockResolvedValue(undefined),
          update: vi.fn(),
          save: mockSaveFn,
        },
        configRepository: {
          findOneBy: vi.fn().mockResolvedValue({ id: 'mockConfigId' }),
          create: mockCreate,
          insert: vi.fn(),
        },
      } as unknown as ILookupConfig['dependencies']);
      const result = await mockService.upsertConfig({
        programId: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        fields: [
          {
            key: 'this',
            details: 'foo',
            sample: 'verde',
            _weight: 'weight',
            metadata: false,
          },
          { key: 'good', details: 'gee', sample: 'azul', _weight: 'weight', metadata: false },
        ],
      });
      expect(result).toEqual({
        body: {
          message:
            'Data Lookup fields successfully updated lookup config for program: mockProgramId',
        },
        status: null,
      });
      expect(mockCreate).toHaveBeenCalledWith({
        programId: 'mockProgramId',
        applicantTypeId: 'mockApplicantTypeId',
        service: ServiceType.DataLookup,
      });
      expect(mockSaveFn).toHaveBeenCalledWith({
        configId: 'mockConfigId',
        fields: [
          {
            key: 'this',
            details: 'foo',
            sample: 'verde',
            _weight: 'weight',
            metadata: false,
          },
          { key: 'good', details: 'gee', sample: 'azul', _weight: 'weight', metadata: false },
        ],
      });
    });
    it('should return a failed response when repository returns a failure', async () => {
      const mockUpdateFn = vi.fn();
      const mockService = new LookupConfigService({
        lookupConfigRepository: {
          findOneBy: vi.fn().mockResolvedValue({ id: 'mockLookupConfigId', fields: [] }),
          update: mockUpdateFn.mockRejectedValue(new Error('bad tings')),
        },
      } as unknown as ILookupConfig['dependencies']);
      await expect(
        mockService.upsertConfig({
          programId: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          fields: [
            {
              key: 'this',
              details: 'foo',
              sample: 'verde',
              _weight: 'weight',
              metadata: false,
            },
            {
              key: 'good',
              details: 'gee',
              sample: 'azul',
              _weight: 'weight',
              metadata: false,
            },
          ],
        }),
      ).rejects.toThrow(new Error('bad tings'));
    });
    it('should return a failed response when input is invalid', async () => {
      const mockService = new LookupConfigService({
        lookupConfigRepository: {
          findOneBy: vi.fn().mockResolvedValue({ id: 'mockLookupConfigId', fields: [] }),
          update: vi.fn().mockResolvedValue({ id: 'mockConfigId' }),
        },
      } as unknown as ILookupConfig['dependencies']);
      await expect(
        mockService.upsertConfig({
          programId: 'mockProgramId',
          applicantTypeId: 'mockApplicantTypeId',
          fields: [
            {
              key: '',
              details: '',
              sample: '',
              _weight: 'weight',
              metadata: true,
            },
          ],
        }),
      ).rejects.toThrow(new Error('Invalid field array'));
    });
  });
});

{"name": "@bybeam/core", "version": "0.0.1", "private": true, "engines": {"node": "20.10.0", "pnpm": "10.4.1"}, "packageManager": "pnpm@10.4.1", "devDependencies": {"@biomejs/biome": "1.9.3", "@tsconfig/node20": "20.1.4", "@turbo/gen": "^2.5.8", "@types/argon2": "0.15.4", "@types/pg": "8.6.1", "lefthook": "1.6.8", "turbo": "^2.5.8", "ws": "^8.18.1"}, "pnpm": {"overrides": {"dset": "^3.1.4", "elliptic": "^6.6.1", "body-parser": "^1.20.3", "braces": "^3.0.3", "image-size": "^1.2.1", "multer": "^2.0.2", "form-data": "^2.5.5", "pbkdf2": "^3.1.3"}, "onlyBuiltDependencies": ["pprof", "protobufjs"]}, "scripts": {"check:devenv": "./setup.sh", "preinstall": "npx only-allow pnpm", "install:python": "uv sync --all-packages --all-extras", "prebuild": "pnpm preinstall && pnpm install:python", "firebase:emulator:init": "pnpm firebase emulators:start", "lint": "biome lint .", "seed:emulator:identity": "pnpm --filter identity-server run emulator:seed", "seed:emulator:pubsub": "pnpm --filter infrastructure-lib run emulator:seed", "seed:dataservices:elasticsearch": "pnpm --filter elasticsearch-sync run emulator:seed", "seed:emulators": "pnpm seed:emulator:identity && pnpm seed:emulator:pubsub", "seed:dataservices": "pnpm seed:dataservices:elasticsearch", "start:emulators": "docker compose build grpc-gateway && docker compose up grpc-gateway elasticsearch -d && pnpm firebase emulators:start --project core-platform-local-beam --log-verbosity DEBUG", "start:apis": "turbo run start:dev -F '@bybeam/platform-api' -F '@bybeam/payments-api'", "start:microservices": "turbo run start:dev -F '@bybeam/config-server' -F '@bybeam/linking-server' -F '@bybeam/identity-server' -F '@bybeam/notification-server' -F '@bybeam/scheduler-server' -F '@bybeam/verification-server'", "start:frontends": "turbo run start:dev -F '@bybeam/platform-ui' -F '@bybeam/partner-portal'", "start:dataservices": "turbo run start:dev -F '@bybeam/entity-resolution' -F '@bybeam/elasticsearch-sync' -F '@bybeam/doctopus'", "start:databases": "docker compose build config-db spice-db core-db verification-db scheduler-db identity-db payments-db doctopus-db && docker compose up config-db spice-db core-db verification-db scheduler-db identity-db payments-db doctopus-db", "start:databases:reset": "docker compose down --volumes && pnpm start:databases", "test:frontend-apps": "turbo run test --filter=@bybeam/partner-portal --filter=@bybeam/platform-ui -- --watch=false", "test:services-and-apis": "turbo run test --filter=!@bybeam/partner-portal --filter=!@bybeam/platform-ui --filter=!./packages/* -- --watch=false", "test:libraries": "turbo run test --filter=./packages/* -- --watch=false", "test:frontend-apps:ci": "CI=true turbo run test --filter=@bybeam/partner-portal[${TURBO_SCM_BASE:-origin/main}...HEAD] --filter=@bybeam/platform-ui[${TURBO_SCM_BASE:-origin/main}...HEAD] --concurrency=8 -- --coverage --pool threads", "test:services-and-apis:ci": "CI=true turbo run test --filter=[${TURBO_SCM_BASE:-origin/main}...HEAD] --filter=!@bybeam/partner-portal --filter=!@bybeam/platform-ui --filter=!./packages/* --concurrency=8 -- --coverage --retry 3 --pool threads", "test:libraries:ci": "CI=true turbo run test --filter=./packages/*[${TURBO_SCM_BASE:-origin/main}...HEAD] --concurrency=8 -- --coverage --retry 3 --pool threads", "build:ci": "turbo build --filter=[${TURBO_SCM_BASE:-origin/main}...HEAD] --concurrency=8", "api": "pnpm --filter=platform-api", "core-db": "pnpm --filter=core-db", "config-db": "pnpm --filter=config-db", "cypress": "pnpm --filter=cypress", "doctopus": "pnpm --filter=doctopus", "entity-resolution": "pnpm --filter=entity-resolution", "es-sync": "pnpm --filter=elasticsearch-sync", "infra": "pnpm --filter=infrastructure-lib", "linking": "pnpm --filter=linking-server", "load-test": "pnpm --filter=load-testing", "math-lib": "pnpm --filter=math-lib", "identity": "pnpm --filter=identity-server", "identity-db": "pnpm --filter=identity-db", "payments": "pnpm --filter=payments-api", "payments-db": "pnpm --filter=payments-db", "scheduler": "pnpm --filter=scheduler-server", "scheduler-db": "pnpm --filter=scheduler-db", "applicant-portal": "pnpm --filter=platform-ui", "partner-portal": "pnpm --filter=partner-portal", "verification": "pnpm --filter=verification-server", "verification-db": "pnpm --filter=verification-db"}, "dependencies": {"@gorules/jdm-editor": "^1.47.0", "@preset-sdk/embedded": "^0.1.13", "argon2": "0.44.0", "firebase-tools": "^14.12.0"}}
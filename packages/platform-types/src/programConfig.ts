import { AddressInput } from './address.js';
import { PaymentSchedule } from './paymentPattern.js';
import { ConfigurableReapplicationRule } from './reapplication.js';

export enum ApplicationConfigurationKey {
  // Beam Demo Configs
  DEMO_CHILDCARE = 'DEMO_CHILDCARE',
  DEMO_WORKFORCE = 'DEMO_WORKFORCE',
  DEMO_MULTIPARTY = 'DEMO_MULTIPARTY',
  WESTSIDE_COUNTY_V1 = 'WESTSIDE_COUNTY_V1',
  WESTSIDE_COUNTY_V2 = 'WESTSIDE_COUNTY_V2',

  // Government Partners
  BALTIMORE = 'BALTIMORE',
  BATON = 'BATON',
  CELL_ED_T1 = 'CELL_ED_T1',
  CELL_ED_T2 = 'CELL_ED_T2',
  EDC_KC = 'EDC_KC',
  FAIRFAX = 'FAIRFAX',
  FAIRFAX_ENROLL = 'FAIRFAX_ENROLL',
  HCAI = 'HCAI',
  HCAI_T1 = 'HCAI_T1',
  HCAI_T2 = 'HCAI_T2',
  KC_BIZCARE = 'KC_BIZCARE',
  KC_KIVA = 'KC_KIVA',
  KC_1MBB = 'KC_1MBB',
  KC_LIDO = 'KC_LIDO',
  KC_SMB_CAPITAL = 'KC_SMB_CAPITAL',
  KC_SMB_GRANT = 'KC_SMB_GRANT',
  KC_SMB_IDA = 'KC_SMB_IDA',
  KC_SMB_LOAN_PREP = 'KC_SMB_LOAN_PREP',
  NH = 'NH',
  TPP_PHILLY = 'TPP_PHILLY',
  TPP_NYC = 'TPP_NYC',

  // EDU/Philanthropy Partners (i.e., variations on the EDU app)
  EDU = 'EDU',
  EDU_ADDRESS = 'EDU_ADDRESS',
  EDU_COMPTON_DUAL = 'EDU_COMPTON_DUAL',
  EDU_DCPS_MICROGRANT = 'EDU_DCPS_MICROGRANT',
  EDU_DCPS_EMERGENCY = 'EDU_DCPS_EMERGENCY',
  EDU_DELMAR = 'EDU_DELMAR',
  EDU_ECMC = 'EDU_ECMC',
  EDU_LCCC = 'EDU_LCCC',
  EDU_MATC = 'EDU_MATC',
  EDU_MINNESOTA = 'EDU_MINNESOTA',
  EDU_NCCPC = 'EDU_NCCPC',
  EDU_NO_DOCS = 'EDU_NO_DOCS',
  EDU_PG = 'EDU_PG',
  MICHIGAN_EDUCATION_CORPS = 'MICHIGAN_EDUCATION_CORPS',
  OPPNET = 'OPPNET',
}

// TODO update data to match new keys
export interface RecurringPaymentConfig {
  amount: number;
  count: number;
  pattern: PaymentSchedule;
  // The start date should be stored as a full UTC timestamp in the database
  start?: string;
  totalAmount: number;
}

export interface PaymentsConfig {
  recurring?: RecurringPaymentConfig;
}

export interface VerificationFileKey {
  details: string;
  key: string;
  sample: string;
}
export interface ApplicationConfigReviewFields {
  appConfigId: string;
  fields: string[];
}

export interface ProgramContext {
  description: string;
  link?: string;
}

export interface ProgramConfig {
  applicationConfiguration: ApplicationConfigurationKey;
  documents: { modelId: string };
  maxFundingAmount?: number;
  payments?: PaymentsConfig;
  mailingAddressOverride?: AddressInput;
  reapplicationRules?: ConfigurableReapplicationRule[];
  sortOrder?: number;
  // When true, new submissions/updates auto-trigger rules evaluations (stored in programs.config)
  rulesEvaluationEnabled?: boolean;
  // Fields to display on the case review page, keyed by application config ID
  applicationReviewFields?: ApplicationConfigReviewFields[];
  programContext?: ProgramContext;
}

export default ProgramConfig;

import { ActiveLabel } from '@bybeam/doctopus-types';
import { VerificationConfiguration } from '@bybeam/verification-types';
import { ApplicationConfig } from './appconfig/index.js';
import { ApplicantType } from './applicantType.js';
import Case, { CaseStatus } from './case.js';
import Document from './document.js';
import Fund from './fund.js';
import { NotificationConfig } from './notification.js';
import Outcome from './outcome.js';
import Partner from './partner.js';
import { PaymentStatus } from './payment.js';
import ProgramConfig, { ApplicationConfigReviewFields, ProgramContext } from './programConfig.js';
import ProgramFeature from './programFeature.js';
import ProgramReferral from './programReferral.js';
import Service from './service.js';
import { Workflow } from './workflow.js';
import { WorkflowSummary } from './workflowEvent.js';

export enum ProgramStatus {
  Open = 'Open',
  ReferralOnly = 'ReferralOnly',
  Closed = 'Closed',
}

export enum ProgramDocumentStatus {
  InProgress = 'InProgress',
  Failed = 'Failed',
  Completed = 'Completed',
}

export interface ProgramDocument {
  id: string;
  status?: ProgramDocumentStatus;
  programId: string;
  program?: Program;
  documentId: string;
  document?: Document;
}

export interface ProgramApplicantType {
  id: string;
  applicantTypeId: string;
  programId: string;
  nameOverride?: string;
  applicantType: ApplicantType;
}

export interface ProgramApplicationConfiguration {
  applicantTypeId: string;
  programId: string;
  configurationId: string;
  configuration: ApplicationConfig;
}

export interface CreateProgramApplicationConfigurationInput {
  partnerId: string;
  programId: string;
  applicantTypeId: string;
}

export interface Program {
  id: string;
  name: string;
  status: ProgramStatus;
  heroImage?: string;
  partnerId: string;
  partner: Partner;
  config: ProgramConfig;

  applicantTypes?: ProgramApplicantType[];
  applicationConfigurations?: ProgramApplicationConfiguration[];
  cases?: Case[];
  documents?: ProgramDocument[];
  documentLabels?: ActiveLabel[];
  features?: ProgramFeature[];
  funds?: Fund[];
  outcomes?: Outcome[];
  programFunds?: ProgramFund[];
  referrals?: ProgramReferral[];
  services?: Service[];
  stats?: ProgramStats;
  verificationConfigurations?: VerificationConfiguration[];
  workflowSummary?: WorkflowSummary;
  workflow: Workflow;
  notifications?: NotificationConfig[];
}

export type UpdateProgramInput = {
  id: string;
  status?: ProgramStatus;
  // Write-only input that is persisted under programs.config
  rulesEvaluationEnabled?: boolean;
  applicationReviewFields?: ApplicationConfigReviewFields[];
  programContext?: ProgramContext;
};

export type UpdateProgramsInput = UpdateProgramInput[];

export interface ProgramFundStats {
  fundId: string;
  programId: string;
  awardedBalance: number;
  obligatedBalance: number;
  paymentCount: number;
}

export interface ProgramFunds {
  id: string;
  fundId: string;
  status: PaymentStatus;
  total: number;
  count: number;
}

export interface ProgramFund {
  id: string;
  fundId: string;
  programId: string;
  fund?: Fund;
  program?: Program;
}

export interface CaseStatusCounts {
  id: string;
  status: CaseStatus;
  count: number;
}

export type CaseCounts = { [status in CaseStatus | 'All']: number };

export interface ProgramStats {
  id: string;
  awardedBalance: number;
  caseCounts: CaseCounts;
  programFundStats: ProgramFundStats[];
}

export interface CreateProgramInput {
  name: string;
  status?: ProgramStatus;
  heroImage?: string;
  fundIds: string[];
}

export interface RemoveProgramFundInput {
  programId: string;
  fundId: string;
}

export interface AddProgramFundInput {
  programId: string;
  fundId: string;
}

export default Program;

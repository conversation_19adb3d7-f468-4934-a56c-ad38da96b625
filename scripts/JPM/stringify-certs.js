#!/usr/bin/env node

/**
 * Script to JSON stringify PEM and certificate files
 *
 * This script reads all .pem and .cert files in the current directory
 * and outputs them as JSON-stringified values, properly escaping newlines.
 */

const fs = require('node:fs');
const path = require('node:path');

const SCRIPT_DIR = __dirname;

function getFilesWithExtensions(directory, extensions) {
  try {
    const files = fs.readdirSync(directory);
    return files.filter((file) => {
      const ext = path.extname(file).toLowerCase();
      return extensions.includes(ext);
    });
  } catch (error) {
    console.error(`Error reading directory ${directory}:`, error);
    return [];
  }
}

function readAndStringifyCertificates() {
  const extensions = ['.pem', '.cert', '.crt'];
  const certFiles = getFilesWithExtensions(SCRIPT_DIR, extensions);

  if (certFiles.length === 0) {
    console.log('No PEM or certificate files found in the current directory.');
    return;
  }

  const results = {};

  for (const filename of certFiles) {
    const filePath = path.join(SCRIPT_DIR, filename);

    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Create a clean key name by removing the file extension
      const keyName = filename.replace(/\.(pem|cert|crt)$/i, '');

      // Replace actual newlines with the literal string '\n' so it appears as \\n in JSON
      const escapedContent = content.replace(/\n/g, '\\n');

      results[keyName] = escapedContent;

      console.log(`✓ Processed: ${filename} -> ${keyName}`);
    } catch (error) {
      console.error(`Error reading ${filename}:`, error);
    }
  }

  // Output all results as a single JSON object
  const jsonOutput = JSON.stringify(results, null, 4);
  console.log('\n=== JSON Output ===\n');
  console.log(jsonOutput);

  // Save to output file
  const outputPath = path.join(SCRIPT_DIR, 'certificates.json');
  try {
    fs.writeFileSync(outputPath, jsonOutput, 'utf8');
    console.log('\n✓ Output saved to:', outputPath);
  } catch (error) {
    console.error('Error writing output file:', error);
  }
}

// Run the script
readAndStringifyCertificates();

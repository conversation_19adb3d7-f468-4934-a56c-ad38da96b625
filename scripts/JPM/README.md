# JPM Certificate Stringify Script

This directory contains scripts for processing JPM certificates and keys.

## Usage

### stringify-certs.js

This script reads all `.pem`, `.cert`, and `.crt` files in the `scripts/JPM` directory and outputs them as JSON-stringified values with properly escaped newlines.

**To run:**

From the scripts/JPM directory:

```bash
node stringify-certs.js
```

**Example:**

If you have files like `digitalsig_key.pem`, `transport_cert.pem`, and `transport_key.pem` in this directory, the script will:
- Read each file's contents
- Create a JSON object with clean key names (file extensions removed)
- Escape newlines as `\n` in the JSON output
- Display it in the console
- Save it to `certificates.json`

The output format will be:

```json
{
    "digitalsig_key": "-----BEGIN PRIVATE KEY-----\nMIIE...\n-----END PRIVATE KEY-----\n",
    "transport_cert": "-----BEGIN CERTIFICATE-----\nMIIG...\n-----<PERSON><PERSON> CERTIFICATE-----\n",
    "transport_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIE...\n-----END PRIVATE KEY-----"
}
```
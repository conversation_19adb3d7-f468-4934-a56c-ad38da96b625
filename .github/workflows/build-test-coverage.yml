name: Build, Test, and Coverage
on:
  pull_request:
  push:
    branches:
      - main
  merge_group:
env:
  # Default fallback for Turbo SCM base (can be overridden by individual jobs)
  TURBO_SCM_BASE: origin/main
  # Artifact names for coverage uploads
  FRONTEND_COVERAGE_ARTIFACT: frontend-coverage
  BACKEND_COVERAGE_ARTIFACT: backend-coverage
jobs:
  frontend-tests:
    name: Frontend Applications
    runs-on: github-xlarge
    container:
      image: mcr.microsoft.com/playwright:v1.55.1-noble
    env:
      PLAYWRIGHT_BROWSERS_PATH: /ms-playwright
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Git for Turbo
        run: |
          git config --global --add safe.directory /__w/platform-core/platform-core
          git remote set-url origin https://github.com/${{ github.repository }}.git
      
      - name: Set Turbo comparison base for changed file detection
        run: |
          # On main branch merges, compare to previous commit (HEAD^) to detect changes
          # On PRs, compare to merge-base with origin/main to detect changes relative to main branch
          if [ "${{ github.event_name }}" = "push" ] && [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "TURBO_SCM_BASE=HEAD^" >> $GITHUB_ENV
          else
            git fetch origin main
            echo "TURBO_SCM_BASE=$(git merge-base origin/main HEAD)" >> $GITHUB_ENV
          fi
      
      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-
      
      - name: Install Python in container
        run: |
          apt-get update && apt-get install -y python3.12 python3-pip
          ln -sf /usr/bin/python3.12 /usr/bin/python
      
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
      
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22.20.0
      
      - name: Install modules
        run: pnpm install
      
      - name: Build workspace dependencies
        run: pnpm turbo build --filter=@bybeam/partner-portal^... --filter=@bybeam/platform-ui^...
      
      - name: Run frontend application tests
        run: pnpm test:frontend-apps:ci
      
      - name: Upload coverage artifacts for parallel processing
        if: hashFiles('apps/partner-portal/coverage/cobertura-coverage.xml', 'apps/platform-ui/coverage/cobertura-coverage.xml') != ''
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.FRONTEND_COVERAGE_ARTIFACT }}
          path: |
            apps/partner-portal/coverage/cobertura-coverage.xml
            apps/platform-ui/coverage/cobertura-coverage.xml
          retention-days: 1

  backend-tests:
    # Backend (APIs, Services, and Libraries)
    name: report
    runs-on: github-xlarge
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set Turbo comparison base for changed file detection
        run: |
          # On main branch merges, compare to previous commit (HEAD^) to detect changes
          # On PRs, compare to merge-base with origin/main to detect changes relative to main branch
          if [ "${{ github.event_name }}" = "push" ] && [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "TURBO_SCM_BASE=HEAD^" >> $GITHUB_ENV
          else
            git fetch origin main
            echo "TURBO_SCM_BASE=$(git merge-base origin/main HEAD)" >> $GITHUB_ENV
          fi
      
      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22.20.0
          cache: "pnpm"
      - name: Setup UV
        uses: astral-sh/setup-uv@v6
        with:
          version: 0.5.15
          activate-environment: true
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install modules
        run: pnpm install
      - name: Validate GraphQL codegen artifacts
        run: pnpm --filter=@bybeam/platform-api codegen:ci-check
      - name: Build apps and packages
        run: pnpm build:ci
      - name: Run backend tests in parallel
        run: |
          pnpm test:services-and-apis:ci &
          APIS_PID=$!
          pnpm test:libraries:ci &
          LIBS_PID=$!
          
          # Wait for both to complete
          wait $APIS_PID
          APIS_EXIT=$?
          wait $LIBS_PID
          LIBS_EXIT=$?
          
          # Fail if either failed
          if [ $APIS_EXIT -ne 0 ] || [ $LIBS_EXIT -ne 0 ]; then
            echo "One or more test suites failed"
            exit 1
          fi
      
      - name: Upload coverage artifacts for parallel processing
        if: hashFiles('apps/*/coverage/cobertura-coverage.xml', 'packages/*/coverage/cobertura-coverage.xml') != ''
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.BACKEND_COVERAGE_ARTIFACT }}
          path: |
            apps/*/coverage/cobertura-coverage.xml
            packages/*/coverage/cobertura-coverage.xml
          retention-days: 1

  upload-coverage:
    name: Upload Coverage to Codecov
    needs: [frontend-tests, backend-tests]
    uses: ./.github/workflows/upload-coverage.yml
    with:
      frontend_artifact: frontend-coverage
      backend_artifact: backend-coverage
      pr_number: ${{ github.event.pull_request.number }}
      head_sha: ${{ github.event.pull_request.head.sha || github.sha }}
      base_ref: ${{ github.event.pull_request.base.ref || 'main' }}
    secrets:
      codecov_token: ${{ secrets.CODECOV_TOKEN }}

  go-checks:
    name: Go Lint & Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.22"
          cache: true

      - uses: dominikh/staticcheck-action@v1
        with:
          version: "latest"
          working-directory: "apps/themis"

      - name: Run Gosec Security Scanner
        uses: securego/gosec@master

      - name: Upload Go test results (if any)
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: go-test-results
          path: |
            **/coverage.out

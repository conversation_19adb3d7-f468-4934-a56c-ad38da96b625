name: Upload Coverage to Codecov

on:
  workflow_call:
    inputs:
      frontend_artifact:
        description: 'Name of the frontend coverage artifact'
        required: true
        type: string
      backend_artifact:
        description: 'Name of the backend coverage artifact'
        required: true
        type: string
      pr_number:
        description: 'Pull request number'
        required: false
        type: string
      head_sha:
        description: 'Head commit SHA'
        required: false
        type: string
      base_ref:
        description: 'Base branch reference'
        required: false
        type: string
    secrets:
      codecov_token:
        description: 'Codecov token for uploading coverage'
        required: true

jobs:
  upload-all:
    name: Upload All Coverage
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code for commit context
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Download frontend coverage artifacts
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          name: ${{ inputs.frontend_artifact }}
          path: ./coverage-data
      
      - name: Download backend coverage artifacts
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          name: ${{ inputs.backend_artifact }}
          path: ./coverage-data
      
      - name: Upload all coverage to Codecov in parallel
        shell: bash {0}
        env:
          CODECOV_TOKEN: ${{ secrets.codecov_token }}
          PR_NUMBER: ${{ inputs.pr_number }}
          HEAD_SHA: ${{ inputs.head_sha }}
          BASE_REF: ${{ inputs.base_ref }}
        run: |
          # Download codecov uploader once
          curl -Os https://uploader.codecov.io/latest/linux/codecov
          chmod +x codecov
          
          # Track background processes
          declare -a pids=()
          upload_count=0
          
          # Build codecov CLI args for PR context
          CODECOV_ARGS="-t $CODECOV_TOKEN"
          if [ -n "$PR_NUMBER" ]; then
            CODECOV_ARGS="$CODECOV_ARGS --pr $PR_NUMBER"
            echo "Uploading for PR #$PR_NUMBER"
          fi
          if [ -n "$HEAD_SHA" ]; then
            CODECOV_ARGS="$CODECOV_ARGS --sha $HEAD_SHA"
            echo "Head SHA: $HEAD_SHA"
          fi
          if [ -n "$BASE_REF" ]; then
            CODECOV_ARGS="$CODECOV_ARGS --parent $(git rev-parse $BASE_REF 2>/dev/null || echo '')"
            echo "Base ref: $BASE_REF"
          fi
          
          # Function to upload coverage if file exists
          upload_if_exists() {
            local path=$1
            local flag=$2
            local file="./coverage-data/${path}/coverage/cobertura-coverage.xml"
            
            if [ -f "$file" ]; then
              echo "Uploading $flag..."
              ./codecov $CODECOV_ARGS -f "$file" -F "$flag" &
              pids+=($!)
              ((upload_count++))
            else
              echo "Skipping $flag (no coverage file)"
            fi
          }
          
          echo "=== Starting parallel coverage uploads ==="
          
          # Frontend Applications
          upload_if_exists "apps/partner-portal" "ui-partner-portal"
          upload_if_exists "apps/platform-ui" "ui-platform-ui"
          
          # Libraries
          upload_if_exists "packages/formatting" "lib-formatting"
          upload_if_exists "packages/infrastructure-lib" "lib-infrastructure-lib"
          upload_if_exists "packages/math-lib" "lib-math-lib"
          upload_if_exists "packages/platform-entities" "lib-platform-entities"
          upload_if_exists "packages/platform-lib" "lib-platform-lib"
          upload_if_exists "packages/scoring" "lib-scoring"
          
          # APIs and Services
          upload_if_exists "apps/config-server" "api-config-server"
          upload_if_exists "apps/doctopus" "api-doctopus"
          upload_if_exists "apps/elasticsearch-sync" "api-elasticsearch-sync"
          upload_if_exists "apps/entity-resolution" "api-entity-resolution"
          upload_if_exists "apps/identity-server" "api-identity-server"
          upload_if_exists "apps/linking-server" "api-linking-server"
          upload_if_exists "apps/notification-server" "api-notification-server"
          upload_if_exists "apps/payments-api" "api-payments-api"
          upload_if_exists "apps/platform-api" "api-platform-api"
          upload_if_exists "apps/scheduler-server" "api-scheduler-server"
          upload_if_exists "apps/verification-server" "api-verification-server"
          
          echo "Started $upload_count parallel uploads"
          
          # If no uploads were started, exit successfully
          if [ $upload_count -eq 0 ]; then
            echo "No coverage files found to upload"
            exit 0
          fi
          
          # Wait for all background uploads to complete
          # Note: Codecov CLI sometimes returns non-zero even on successful uploads
          # We'll wait for all to complete but not fail the job
          success=0
          for pid in "${pids[@]}"; do
            if wait "$pid"; then
              ((success++))
            else
              exit_code=$?
              echo "Warning: Upload process $pid exited with code $exit_code (may still have succeeded)"
            fi
          done
          
          echo "Completed $upload_count uploads (note: codecov CLI may report non-zero exit codes even on success)"
          
          echo "=== All $upload_count uploads completed successfully ==="


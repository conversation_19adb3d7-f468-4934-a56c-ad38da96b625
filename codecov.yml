comment:
  layout: "condensed_header, diff, components"
  show_carryforward_flags: false
  require_changes: false
  require_base: true
  require_head: true

ignore:
  - "**/__generated__/**"
  - "**/*.d.ts"
  - "**/migrations/**"

coverage:
  status:
    default_rules:
      flag_coverage_not_uploaded_behavior: exclude  # Don't send status checks for flags that don't upload coverage
    project:
      default:
        target: auto
        threshold: 0.5%
        informational: true
    patch:
      default:
        target: auto
        threshold: 0.5%
        informational: true

flag_management:
  default_rules:
    carryforward: true
    statuses:
      - type: patch
        target: auto
        threshold: 0.5%
        informational: true

component_management:
  default_rules:
    # Disable component status checks to avoid noise for unchanged components
    # Component coverage is still visible in the PR comment
    statuses: []

  individual_components:
  - component_id: uis
    name: Frontend Applications
    paths:
    - apps/partner-portal/**
    - apps/platform-ui/**
    flag_regexes:
    - "^ui-(partner-portal|platform-ui)$"
  - component_id: apis
    name: APIs and Services
    paths:
    - apps/config-server/**
    - apps/doctopus/**
    - apps/elasticsearch-sync/**
    - apps/entity-resolution/**
    - apps/identity-server/**
    - apps/linking-server/**
    - apps/notification-server/**
    - apps/payments-api/**
    - apps/platform-api/**
    - apps/scheduler-server/**
    - apps/verification-server/**
    flag_regexes:
    - "^api-(config-server|doctopus|elasticsearch-sync|entity-resolution|identity-server|linking-server|notification-server|payments-api|platform-api|scheduler-server|verification-server)$"
  - component_id: libs
    name: Libraries and Packages
    paths:
    - packages/**
    flag_regexes:
    - "^lib-[a-z0-9-]+$"
